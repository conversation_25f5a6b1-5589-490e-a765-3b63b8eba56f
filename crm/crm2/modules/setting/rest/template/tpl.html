<div class="crm-tit">
    <h2><span class="tit-txt">{{$t("CRM初始化")}}</span></h2>
</div>
<div class="crm-module-con">
    <div class="wrap">
        <div class="crm-intro">
            <h3>{{$t("说明：")}}</h3>
            <p>{{$t("crm.CRM初始化很危险")}}</p>
			<p style="margin-left:10px;">{{newoppo?$t("crm.预设对象新商机和自定义对象的所有数据全部删除"):$t("crm.预设对象和自定义对象的所有数据全部删除")}}</p>
			<p style="margin-left:10px;">{{$t("crm.数据触发的流程数据")}}</p>
			<br/>
			<p>{{$t("crm.将于后续开放的初始化内容")}}</p>
			<p style="margin-left:10px;">{{$t("crm.配置信息")}}</p>
			<p style="margin-left:10px;">{{$t("crm.销售记录以及角色权限数据")}}</p>
            <div class="share-group-box" data-title="crmrest"></div>
        </div>

        <div class="item">
            <h3>{{$t("适用场景：")}}</h3>
            <p>{{fxLabelRemoval?$t("name如何管理CRM信息",{name:companyName},"试用纷享一段时间后想正式管理CRM信息，需要清除测试数据。"):$t("crm.如何管理CRM信息")}}</p>
        </div>

        <div class="item">
            <h3>{{$t("操作步骤：")}}</h3>
			<p>{{fxLabelRemoval?$t("联系name服务中心",{name:companyName},"联系纷享服务中心"):$t("联系纷享服务中心")}}<strong> 400-1122-778</strong>，{{$t("crm.审核后可以开启高危操作权限")}}</p>
        </div>

        <div class="btn-box">
            <div class="crm-btn crm-btn-danger ##if(data.isOpen==0){##crm-btn-disabled##}##">{{$t("CRM数据初始化")}}</div>
        </div>
    </div>
</div>
