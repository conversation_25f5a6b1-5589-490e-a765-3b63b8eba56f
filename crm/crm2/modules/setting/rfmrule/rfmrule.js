define(function(require, exports, module) {

    var VcrmSdk = require('vcrm/sdk');
    
    var Rfm = Backbone.View.extend({
      initialize: function (opts) {
        this.setElement(opts.wrapper);
      },
  
      render: function() {
        var el = this.el;
        VcrmSdk.getComponent('rfm').then(function(comp){
            let Comp = comp.default;
            Comp(el);
        })
      }
    });
  
    module.exports = Rfm;
  });