/**
 * @desc 智能机器人
 */
define(function (require, exports, module) {
	// var util = require("crm-modules/common/util"),
	// 	tpl = require("./template/tpl-html");

  var Robot = Backbone.View.extend({
    initialize: function(opts) {
      this.setElement(opts.wrapper);
    },
    render: function() {
			this.initDefaultData()
			this.initRobot()
    },
    initDefaultData() {
			this.comps = {}		
    },
    initRobot($root) {
			$root || ($root = this.el);
			require.async('app-standalone/app', (StandAloneApp) => {
        StandAloneApp.getModuleComp('intelligent-robot/robot-index/robot-index-vue.js').then(Robot => {
          const robotInstance = new Robot({
						el: document.createElement('div'),
            propsData: {
              entrance: 'manage'
            }
          })
          this.comps.robotInstance = robotInstance;
					$root.append(robotInstance.$el);
        })
			})
		},
  })

  module.exports = Robot
});
