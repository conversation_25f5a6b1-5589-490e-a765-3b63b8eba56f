/**
 * 员工角色分配----添加角色
 * TODO
 */

define(function (require, exports, module){
    var util = require('crm-modules/common/util');
    var Select = require('crm-widget/select/select');
    var Dialog = require("crm-widget/dialog/dialog");
    var mainHtml = require('../template/edit-customizerole-html');

    var EditCustomizeRole = Dialog.extend({
        isSelected: false,
        attrs: {
            title: $t("新建角色"),
            content: '<div class="crm-loading "></div>',
            showBtns:  true,
            showScroll: false,
            datas: null,
        },
        events: {
            'click .b-g-btn-cancel': 'destroy',
            'click .b-g-btn': 'onSave',
            'focus .fm-ipt': 'onRemoveErr',
            'click .j-toggle': 'onToggle'
        },
        render: function(){
            var type = this.get('type');
            var result = EditCustomizeRole.superclass.render.call(this);
            this.setContent( mainHtml( {
                datas: this.get('datas'),
                type: type,
            } ) );
            (type === 'add') && this.initSelect();
            return result;
        },
        show: function(){
            var result = EditCustomizeRole.superclass.show.call(this);
            return result;
        },
        initSelect: function() {
            this.selectOptions = {
                $wrap: $('.j-select-box', this.$el),
                zIndex: 1010,
                disabled: true,
                options: this.get('roleList'),
            };
            this.select = new Select(this.selectOptions);  
        },
        onToggle: function(e) {
            var $target = $(e.currentTarget);
            $target.toggleClass('mn-selected');
            this.isSelected = $target.hasClass('mn-selected');
            var selectOptions = _.extend({}, this.selectOptions, {disabled: !this.isSelected});

            this.select.destroy();
            this.select = new Select(selectOptions);  

            e.stopPropagation();
            return false;
        },
        onSave: function(){
            var $rolenameIpt = this.$('.rolename-ipt');
            var $roledesIpt = this.$('.roledes-ipt');
            var nameV = $rolenameIpt.val().trim();
            var desV = $roledesIpt.val().trim();
            if( nameV == '' || nameV.length === 0 ){
                util.showErrmsg($rolenameIpt, $t("角色名称不能为空"));
                return false;
            }else if( nameV.length > 20 ){
                util.showErrmsg($rolenameIpt, $t("角色名称不能超过20个中文字符"));
                return false;
            }

            if( desV == '' || desV.length === 0 ){
                util.showErrmsg($roledesIpt, $t("角色描述不能为空"));
                return false;
            }else if (desV.length > 2000 ) {
                util.showErrmsg($roledesIpt, $t("备注不能超过2000个中文字符"));
                return false;
            }

            if (util.isContainEmojiCharacter(nameV)) {
                util.showErrmsg($rolenameIpt, $t("角色名称不能包含emoji表情符"));
                return false;
            }else if (util.isContainEmojiCharacter(desV)) {
                util.showErrmsg($roledesIpt, $t("角色描述不能包含emoji表情符"));
                return false;
            }

            this.submit(_.extend({
                roleCode: this.get('datas') ? this.get('datas').id : null,
                roleName: nameV,
                description: desV,
                isCopy: this.isSelected
            }, {
                roleType: this.get('roleType')
            }));
        },
        onRemoveErr: function (e) {
            $(e.currentTarget).next().remove();
        }, 
        submit: function(req){
            var me = this;
            var isAdd = me.get('type') === 'add';
            util.FHHApi({
                url: isAdd ? '/EM1HPAASUdobj/roleApi/addRole' : '/EM1HPAASUdobj/roleApi/updateRole',
                data: req,
                success: function(res){
                    if( res.Result.StatusCode == 0 ){
                        me.trigger('success', isAdd ? {
                            sourceRoleCode: me.isSelected && me.select.getValue(),
                            destRoleCode: res.Value,
                        } : null);
                        me.hide();
                    }
                }
            }, {
                submitSelector: me.$('.b-g-btn')
            });
        },
        destroy: function(){
            var result = EditCustomizeRole.superclass.destroy.call(this);
            return result;
        },
        hide: function(){
            var result = EditCustomizeRole.superclass.hide.call(this);
            return result;
        }
    });
    module.exports = EditCustomizeRole;
})