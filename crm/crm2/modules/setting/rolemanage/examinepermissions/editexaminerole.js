/**
 * 审批权限设置--编辑审批部门
 */
define(function (require,exports,module){
    var util = require('crm-modules/common/util');
    var Dialog = require("crm-widget/dialog/dialog");
    var SelectBar = require('crm-widget/selector/selector');
    var mainHtml = require('./template/edit-examinerole-html');
    var EditExamineRole = Dialog.extend({
        attrs: {
            title: $t("添加员工"),
            content: '<div class="crm-loading "></div>',
            showBtns:  true,
            showScroll: false,
            type: 'add',
            delEmp: [],
            data: null,
            FiledList: null,
            className: 'crm-s-rolemanage'
        },

        events: {
            'click .b-g-btn': 'onSave',
            'click .b-g-btn-cancel': 'destroy'
        },

        //显示
        show: function(opts){
            var FiledList = this.get('FiledList');
            var result = EditExamineRole.superclass.show.call(this);

            this.widget = [];
            this.set(opts);
            this.setContent(mainHtml({subtitle: FiledList.subtitle, AllowLeaderFinanceConfirm: FiledList.AllowLeaderFinanceConfirm}));
            this.widgetCircleFn();
            return result;
        },

        //初始化选择部门组件
        widgetCircleFn: function(){
            var me = this;

            var circleWidget = new SelectBar({
                $wrap:  me.$('.widget-circle'),
                zIndex: 1000,
                group:  true,
                single: false,
                label: $t("crm.选择部门"),
                defaultSelectedItems: {
                    group: _.pluck(me.get('FiledList').circleIds, 'id')
                }
            });
            circleWidget.on('addItem', function() {
                 me.$('.widget-circle').next().remove();
            });
            
            me.circleWidget = circleWidget;
        },

        //表单验证
        onSave: function(){
            var CircleIDs = this.circleWidget.getValue('group');
            var FiledList = this.get('FiledList');
            
            if( CircleIDs && CircleIDs.length == 0 ){
                util.showErrmsg($('.widget-circle', this.element), $t("请选择部门！")); 
                return false;
            }
            this.submit({
                CircleIDs: CircleIDs,
                EmployeeID: FiledList.EmployeeID,
                RoleID: FiledList.RoleID,
                AllowLeaderFinanceConfirm: !!this.$('.mn-selected').length
            });
        },

        //保存设置
        submit: function(req){
            var me = this;
			var url = '/EM1HCRM/ApprovalPermission/SetEmployeeRoleCircle';
			if (FS.util.getUserAttribute('approveServerApi') || window.location.host.indexOf('hwcloud') != -1) {
				url = '/EM1HNCRM/API/v1/object/ApprovalPermission/service/update';
			}
            util.FHHApi({
                url: url,
                data: req,
                success: function(res){
                    if( res.Result.StatusCode == 0 ){
                        me.trigger('success');
                        util.remind(1, $t("操作成功！"))
                        me.hide();
                    } else {
                        util.alert(res.Result.FailureMessage);
                    }
                }
            }, {
                errorAlertModel: 1
            });
        },

        destroy: function(){
            _.each( this.widget ,function (item){
                item && item.destroy && item.destroy();
            }, this);
            this.circleWidget && this.circleWidget.destroy();
            return EditExamineRole.superclass.destroy.call(this);
        },

        hide: function(){
            _.each( this.widget ,function (item){
                item && item.destroy && item.destroy();
            }, this);
            this.circleWidget && this.circleWidget.destroy();
            this.widget = null;
            return EditExamineRole.superclass.hide.call(this);
        }
    });
    module.exports = EditExamineRole;
})