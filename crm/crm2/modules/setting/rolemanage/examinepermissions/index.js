/*
 *   审批权限设置
 */

define(function (require, exports, module){
    var util = require('crm-modules/common/util');
 	var Selector = require('crm-widget/selector/selector');
    var DataTable = require('crm-widget/table/table');
    var EditExamineRole = require('./editexaminerole');
    var indexTpl = require('./template/index-html');

    module.exports = Backbone.View.extend({
    	events: {
            'click .role-item': 'onClickItem'
        },

        initialize: function() {
			this.el.innerHTML = indexTpl();
			this.examineSubtitle = `${$t("crm.销售订单")}/${$t("crm.退货单")}`;
            this.renderExamineRoleTable();
        },

        // 渲染审批设置表单
        renderExamineRoleTable: function(){
            var me = this;
            
            me.examineRoleID = '00000000000000000000000000000016';
			
            if(!me.examineDt){
				var url = '/EM1HCRM/ApprovalPermission/GetEmployeeRoleCircleList';
				if (FS.util.getUserAttribute('approveServerApi') || window.location.host.indexOf('hwcloud') != -1) {
					url = '/EM1HNCRM/API/v1/object/ApprovalPermission/service/list';
				}
                me.examineDt = new DataTable({
                    $el: me.$('.right-wrapper'),
                    url: url,
                    requestType:   'FHHApi',
                    showFilerBtn: false,
                    showMultiple:  false,
                    trHandle:      false,
                    autoWidth:     true,
                    search: {
                        'pos': 'T', 
                        'placeHolder': $t("搜索员工"), 
                        'type': 'Keyword', 
                        'fileName': 'Keyword'
                    },
                    postData: {
                        CircleIDs: '',
                        RoleID: me.examineRoleID,
                    },
                    columns: [{
                            data: 'Name',
                            title: $t("姓名"),
                        },{
                            data: 'Department',
                            title: $t("crm.部门"),
                        },{
                            data:   'Post',
                            title: $t("职位")
                        },{
                            data:   '',
                            title: $t("审批负责部门"),
                            render: function(data, type, full){
                                var value = _.map(full.CircleSolidInfoList, function(item) {
                                    return item.Name;
                                }).join($t("、"));
                                return value ? '<span title="'+ value +'">'+ value +'</span>' : '--';
                            }
                        },{
                            data: '',
                            title: $t("操作"),
                            render: function(data, type, full){
                                return '<div><a class="role-edit" style="margin-right: 5px;cursor:pointer;">'+ $t("编辑审批部门")+'</a></div>'
                            }
                        }],
                    formatData: function(data) {
                        var EmployeeInfo = [];
                        _.each(data.EmployeeRoleScopeInfoList, function(item) {
                            EmployeeInfo.push( _.extend({}, item.EmployeeInfo, {CircleSolidInfoList: item.CircleSolidInfoList, AllowLeaderFinanceConfirm: item.AllowLeaderFinanceConfirm}) );
                        });
                        return {
                            totalCount: (data.Page && data.Page.TotalCount) || 0,
                            data: EmployeeInfo     
                        }
                    },
                    initComplete: function($el){
                        $('.first-target-item', $el).before([
                            '<div class="item batch-c">',
                                '<span class="item-tit">'+ $t("范围") +'：</span>',
                                '<div class="item-con selector j-selector"></div>',
                            '</div>'].join(''));
                        me.initExamineCircle();
                    }
                });
                me.examineDt.on('trclick', function(data,$tr,$target) { 
                    if( $target.hasClass('role-edit') ){
                        me.editExamineRoleHandle( data, $tr );
                    }
                });
            }else{
                me.examineDt.setParam({},true);
            }
        },

        // 初始化审批权限的查看范围
        initExamineCircle: function(){
            var me = this;
            me.examineCircleWidget && me.examineCircleWidget.destroy();
            me.examineCircleWidget = new Selector({
                $wrap:  me.$('.j-selector'),
                zIndex: 1000,
                group:  true,
                single: true,
                selectedAfterHideLabel: true,
                label: $t("选择审批负责部门")
            });
            
            me.examineCircleWidget.on('addItem', function() {
                util.hideErrmsg(this.$el.closest('.v-box'));
            });
            
            me.examineCircleWidget.on('change',function (d){
                var _selectData = this.getValue('group');
                var circleIds = '';
                if( _selectData.length > 0){
                    circleIds = [];
                    _.each(_selectData, function (item,index){
                        circleIds.push( item * 1 );
                    });
                }
                me.examineDt.setParamByKey('Keyword', me.$('.dt-ipt').val());
                me.examineDt.setParam({CircleIDs: circleIds}, true, true);
            });
        },

        // 编辑审批部门
        editExamineRoleHandle: function(data, $tr) {
            var me = this;
            var circleIds = [];

            _.each(data.CircleSolidInfoList, function(item) {
                circleIds.push({
                    id: item.CircleID, 
                    type: 'g'
                });
            });

            me.editExamineRoleWidget && (me.editExamineRoleWidget.destroy(),me.editExamineRoleWidget = null);
            me.editExamineRoleWidget = new EditExamineRole({
                title: $t("编辑审批部门"),
                FiledList: _.extend({
                    EmployeeID: data.EmployeeID,
                    AllowLeaderFinanceConfirm: data.AllowLeaderFinanceConfirm,
                    RoleID: me.examineRoleID,
                    circleIds: circleIds,
                    subtitle: me.examineSubtitle
                }) 
            });
            me.editExamineRoleWidget.on('success',function(){
                me.examineDt.setParam({}, true); 
            });
            me.editExamineRoleWidget.show();
        },

        onClickItem: function(e){
            var $target = $(e.currentTarget);
            if( $target.hasClass('cur')) return;

            this.$('.leftnav .cur').removeClass('cur');
            $target.addClass('cur');
            
            this.$('.dt-ipt').val('');
            this.$('.remove-h').click();

            this.examineRoleID = $target.data('roleid');

            // 清空筛选条件
            this.examineCircleWidget.clearAll();
            this.$('.dt-ipt').val('');
            this.examineDt._keyWord = '';

            this.examineDt.refresh({
                CircleIDs: '',
                Keyword: '',
                RoleID: this.examineRoleID
            });

            this.examineSubtitle = $target.data('subtitle');
        },

        destroy: function(){
            var me = this;
            _.each(['examineDt','examineCircleWidget','editExamineRoleWidget'], function(item){
                me[item] && me[item].destroy();
                me[item] && (me[item] = null);
            });
        }
    });
});
