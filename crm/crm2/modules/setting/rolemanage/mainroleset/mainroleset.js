/**
 * 主角色设置
 */

define(function (require, exports, module){
	var util = require('crm-modules/common/util');
	var Dialog = require("crm-widget/dialog/dialog");
    var Select = require('crm-widget/select/select');
    var mainHtml = require('./template/mainroleset-html');

	var MainRoleSet = Dialog.extend({
		attrs: {
            title: $t("提示"),
            content: '<div class="crm-loading"></div>',
            showScroll: true,
            showBtns:  true,
            datas: null,
            className: 'crm-s-rolemanage'
        },
        events: {
            'click .b-g-btn-cancel': 'destroy',
            'click .b-g-btn': 'submit'
        },
        render: function(){
        	var me = this;
            var result = MainRoleSet.superclass.render.call(me);
            var userIdList = [];

            me.select = [];
            util.FHHApi({
                url: '/EM1HPAASUdobj/userApi/getUserInfoByMajorRole',
                data: {
                    roleCode: me.get('roleCode'),
                    userIds: me.get('userIds')
                },
                success: function(res){
                    if( res.Result.StatusCode == 0 ){
                        userIdList = _.map(res.Value, function(item) {
                            return item.userId;
                        });

                        if (userIdList.length === 0) {
                            me.hide();
                            me.trigger('success');
                            return;
                        };

                        me.roleCodeList = _.map(res.Value, function(item) {
                            return item.roleCode2RoleNameMap;
                        });
                        me.userIdList = userIdList;

                        me.setContent(mainHtml({userIdList: userIdList, rolename: me.get('rolename')}));

                        _.each(me.select, function(item) {
                            item.destroy && item.destroy();
                        });
                        me.select = [];
                        _.each(res.Value, function(item, index) {
                            me.select[index] = new Select({
                                $wrap: me.$('.j-select').eq(index),
                                zIndex: 2010,
                                options: item.roleCode2RoleNameMap
                            });  
                        });
                        me.resizedialog();
                    } else {
                        util.alert(res.Result.FailureMessage);
                        me.hide();
                    }
                }
            },{
                errorAlertModel: 1
            });
            return result;
        },

        show: function(){
        	return MainRoleSet.superclass.show.call(this);
        },
        
        submit: function() {
            var me = this;
            var userId2RoleInfoMap = {};

            /* 
             * 数据拼凑
             * format: {"1000":{"00000000000006":true}} 值为true则为主角色
            */ 
            _.each(me.userIdList, function(item, index) {
                var roleInfoMap = {};
                _.each(me.roleCodeList[index], function(ite) {
                    if (ite.value === me.select[index].getValue() ) {
                        roleInfoMap[ite.value] = true;
                    }else {
                        roleInfoMap[ite.value] = false;
                    }
                });

                userId2RoleInfoMap[item] = roleInfoMap;
            });

            util.FHHApi({
                url: '/EM1HPAASUdobj/userApi/updateUserMajorRoleRespectively',
                data: {
                    userId2RoleInfoMap: userId2RoleInfoMap
                },
                success: function(res){
                    me.trigger('success');
                    me.hide();
                }
            },{
                errorAlertModel: 1
            })
        },

        hide: function(){
            return MainRoleSet.superclass.hide.call(this);
        },

        destroy: function(){
            _.each(this.select, function(item) {
                item.destroy && item.destroy();
            });
            return MainRoleSet.superclass.destroy.call(this);
        }
	});
	module.exports = MainRoleSet;
})