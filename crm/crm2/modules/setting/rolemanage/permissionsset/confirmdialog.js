define(function (require, exports, module){
    var Dialog = require("crm-widget/dialog/dialog");

    var ConfirmDialog = Dialog.extend({
        attrs: {
            title: $t("提示"),
            classPrefix: 'crm-c-confirm-dialog crm-c-dialog',
            content: $t("本次权限更改尚未保存是否先保存本次更改。"),
            showBtns:  true,
        },
        events: {
            'click .b-g-btn-cancel': 'destroy',
            'click .b-g-btn': 'save',
            'click .b-g-btn-not-save': 'notSave'
        },
        render: function(){
            var result = ConfirmDialog.superclass.render.call(this);
            $('.dialog-btns', ConfirmDialog.element).prepend('<span class="b-g-btn-not-save">'+ $t("不保存") +'</span>');
            $('.dialog-btns .b-g-btn', ConfirmDialog.element).html($t("保存"));
            return result;
        },
        show: function(){
            var result = ConfirmDialog.superclass.show.call(this);
            return result;
        },
        save: function() {
            this.trigger('save');
            this.hide();
        },
        notSave: function() {
            this.trigger('notSave');
            this.hide();
        },
        destroy: function(){
            return ConfirmDialog.superclass.destroy.call(this);
        },
        hide: function(){
            return ConfirmDialog.superclass.hide.call(this);
        }
    });
    module.exports = ConfirmDialog;
})
