/*
 *   角色权限设置
 */

define(function (require, exports, module){
    var LeftNav = require('./leftnav');
    var RightContent = require('./rightcontent');
    var indexTpl = require('./template/index-html');
    var util = CRM.util;

    module.exports = Backbone.View.extend({
        events: {
            'click .j-toggle-role': 'onToggleRole'                    
        },

        initialize: function() {
            this.isOutRoles = false;
            this.render();
        },

        render: function(roleCode) {
            var me = this;
            var isOutRoles = me.isOutRoles;

            me.el.innerHTML = indexTpl({isOutRoles: isOutRoles}); 
            var $rightWrapper = me.$('.right-wrapper');
            me.getRoleList(function(roleList) {
                me.destroy();
                var res = roleList.length ? (roleList[0].roleInfoList[0] || {})  : {};

                if (roleCode) {
                    _.each(roleList, function(item) {
                        _.each(item.roleInfoList, function(ite) {
                            if (ite.roleCode === roleCode) {
                                res = ite;
                            };
                        });
                    });
                }

                me.leftnav = new LeftNav({
                    el: me.$('.leftnav'), 
                    data: roleList, 
                    roleCode: res.roleCode,
                    isOutRoles: isOutRoles,
                    hiddenOps: me.options.data ? true : false
                });
                me.rightcontent = new RightContent({
                    el: $rightWrapper, 
                    data: res,
                    searchData: me.options.data,  
                    isOutRole: isOutRoles
                });
 
                me.leftnav.on('click_item', function(data) {
                    me.rightcontent.render({
                        el: $rightWrapper, 
                        data: data,
                        searchData: me.options.data,  
                        isOutRole: isOutRoles
                    });
                })
                me.leftnav.on('refresh', function(data) {
                    me.render(data);
                })
                me.rightcontent.on('reset', function(data) {
                    me.rightcontent.render(data);
                })
            })
        },

        getRoleList: function(cb){
            util.FHHApi({
                url:'/EM1HPAASUdobj/roleApi/roleList',
                data: {
                    isOutRoles: this.isOutRoles
                },
                success: function(res){
                    if (res.Result.StatusCode === 0) {
                        var roleList = _.compact(res.Value);

                        var customizeIndex = util.findIndex(roleList, function(item) {
                            return item.groupType === 1
                        });

                        // 初始化自定义角色
                        if (customizeIndex === -1) {
                            roleList.push({
                                groupName: $t("自定义角色"),
                                groupType: 1,
                                roleInfoList: []
                            })
                        }

                        cb(roleList);
                    }
                }
            }); 
        },

        onToggleRole: function() {
            this.isOutRoles = !this.isOutRoles;
            this.render();
        },

        destroy: function(){
            _.each(['leftnav','rightcontent'], function(item){
                this[item] && (this[item].destroy(), this[item] = null);
            }, this);
        }
    })
});
