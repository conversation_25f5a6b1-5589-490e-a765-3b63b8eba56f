define(function (require, exports, module){
    var leftNavTpl = require('../template/leftnav-html');
    var outRoleListTpl = require('./template/outrole-list-html');
    var ConfirmDialog = require('./confirmdialog');
    var EditCustomizeRole = require('../editcustomizerole/editcustomizerole');
    var MainRoleSet = require('../mainroleset/mainroleset');
    var util = CRM.util;

    module.exports = Backbone.View.extend({
        initialize: function(options) {
            this.roleCode = options.roleCode;
            this.isOutRoles = options.isOutRoles;
            this.roleList = _.compact(options.data);
            this.searchRoleList = null;
            
            if (this.isOutRoles) {
                this.el.innerHTML = outRoleListTpl({roles: this.roleList, roleCode: this.roleCode});
            }else {
                this.el.innerHTML = leftNavTpl({
                    roles: this.roleList, 
                    roleCode: this.roleCode, 
                    isPermissionsSet: true,
                    searchRoleList: this.searchRoleList
                });
            }
            // 获取搜索条件 隐藏自定义角色的新增删除编辑
            if (options.hiddenOps) {
                this.$('.j-add').hide();
                this.$('.customize-rolelist .ops').hide();
            }
            // 默认展开左侧第一项
            this.$('.rolelist-box .item_box').eq(0).show();
        },

        events: {
            'click .default-rolelist li': 'onGetRoleFcByRoleId',         
            'click .customize-rolelist li': 'onGetDefaultRoleList',      
            'click .j-add': 'onAdd',                                    
            'click .checkbox-head-toggle': 'onClickArrow',                    
            'keyup .j-s-field': 'onKeyUp',
        },

        // 角色列表折叠
        onClickArrow: function (e) {
            var $div = $(e.currentTarget), $itemBox = $div.next();
            $itemBox.css('display') == 'none' ? $div.addClass('current') : $div.removeClass('current')
            $itemBox.slideToggle()
        },
        // 搜索
        onKeyUp: function (e) {
            var me = this;
            var key = me.$('.j-s-field').val();
            if ((key && e.which == 13) || !key) {
                me.search(key);
            }
        },
        search: function (data) {
            var me = this;
            if (data) {
                var list = [];
                me.$('.item_box .item').each(function () {
                    var text = $(this).data().rolename || '';
                    if (data && text.toString().indexOf(data) >= 0) {
                        list.push($(this).data())
                    }
                });
                me.searchRoleList = list;
                me.el.innerHTML = leftNavTpl({
                    roles: me.roleList,
                    roleCode: me.roleCode,
                    isPermissionsSet: true,
                    searchRoleList: me.searchRoleList
                });
                me.$('.rolelist-box').hide();
            } else {
                me.searchRoleList = null;
                me.$('.rolelist-box').show();
                me.$('.search-rolelist-box').remove();
                me.$(`.item[data-id=${me.roleCode}]`).addClass('cur');
            }
            // me.el.innerHTML = leftNavTpl({
            //     roles: me.roleList,
            //     roleCode: me.roleCode,
            //     isPermissionsSet: true,
            //     searchRoleList: me.searchRoleList
            // });
            // if(me.searchRoleList && me.searchRoleList.length >= 0) me.$('.rolelist-box').hide();
            me.$('.j-s-field').val(data).focus();
        },

        // 通过角色id获取角色具体权限
        onGetRoleFcByRoleId: function(e){
            var me = this,
                $target = $(e.currentTarget);

            if( $target.hasClass('cur')) return;

            if(me.toSubmit){
                me.confirmdialogWidget && (me.confirmdialogWidget.destroy(),me.confirmdialogWidget = null);
                me.confirmdialogWidget = new ConfirmDialog({
                    title: $t("提示"),
                });
                me.confirmdialogWidget.on('save',function( data ){
                    me.onSave();
                    me.roleType = $target.data('type'); 
                    me.jumpPage(e);
                    me.toSubmit = false;
                });
                me.confirmdialogWidget.on('notSave',function( data ){
                    me.roleType = $target.data('type'); 
                    me.jumpPage(e);
                    me.toSubmit = false;
                });
                me.confirmdialogWidget.show();
            }else{
                me.roleType = $target.data('type'); 
                me.jumpPage(e);
            }
        },

        // 自定义角色列表操作
        onGetDefaultRoleList: function(e) {
            var me = this;
            var $target = $(e.target);
            var datas = $target.closest('li').data();

            if ( $target.hasClass('crm-ico-del') ) {
                me.onDel( datas );
            }else if ( $target.hasClass('crm-ico-edite') ) {
                me.addCustomizeRoleWidget && me.addCustomizeRoleWidget.destroy();
                me.addCustomizeRoleWidget = new EditCustomizeRole({
                    title: $t("编辑角色"),
                    type: 'edit',
                    datas: datas,
                    height: 308
                });
                me.addCustomizeRoleWidget.on('success',function(data){
                    me.toSubmit = false;
                    me.trigger('refresh', me.roleCode);
                });
                me.addCustomizeRoleWidget.show();
            }else if ( $target.has('li') ) {
                me.onGetRoleFcByRoleId(e);
            }
        },

        // 切换角色跳页
        jumpPage: function(e) {
            var $target = $(e.currentTarget);
            
            this.roleCode = $target.data().id;
            this.$('.item').removeClass('cur');
            $target.addClass('cur');
            this.trigger('click_item', $target.data());
        },

        // 新建自定义角色
        onAdd: function(e) {
            e.stopPropagation();
            var me = this;
            var roleList = [];

            _.each(me.roleList, function(item) {
                _.each(item.roleInfoList, function(ite) {
                    if (ite.roleCode !== CRM.config.MANAGER_ROLE_CODE) {
                        roleList.push({
                            value: ite.roleCode,
                            name: ite.roleName
                        })
                    };
                });
            });
            me.addCustomizeRoleWidget && me.addCustomizeRoleWidget.destroy();
            me.addCustomizeRoleWidget = new EditCustomizeRole({
                title: $t("新建角色"),
                type: 'add',
                roleList: roleList,
                roleType: me.isOutRoles ? '4' : '2'
            });
            me.addCustomizeRoleWidget.on('success',function(data){
                // 新建角色才能复制角色权限
                data.sourceRoleCode && util.FHHApi({
                    url: '/EM1HPAASUdobj/roleApi/copyRole',
                    data: {
                        destRoleCode: data.destRoleCode,
                        sourceRoleCode: data.sourceRoleCode
                    },
                    success: function( res ){
                        if (res.Result.StatusCode == 0) {
                            util.remind(1, $t("新建成功"));
                        }
                    }
                });

                me.toSubmit = false; 
                me.trigger('refresh', data.destRoleCode);
            });
            me.addCustomizeRoleWidget.show();
        },

        // 删除自定义角色
        onDel: function(datas){
            var me = this;
            
            var confirm = util.confirm('<p>'+ $t("确定删除本角色")+'?</p><p class="crm-s-rolemanage-dialog-text">'+ $t("crm.删除角色员工权限会受影响")+'</p>',$t("删除"),function(){
                me.mainRoleSetWidget && me.mainRoleSetWidget.destroy();
                me.mainRoleSetWidget = new MainRoleSet({
                    roleCode: datas.id
                });
                me.mainRoleSetWidget.show();

                confirm.hide();
                me.mainRoleSetWidget.on('success',function(){
                    util.FHHApi({
                        url: '/EM1HPAASUdobj/roleApi/deleteRole',
                        data: {
                            roleCode : datas.id
                        },
                        success: function(res){
                            if(res.Result.StatusCode == 0){
                                util.remind(1, $t("操作成功"));
                                confirm.destroy();
                                me.toSubmit = false;

                                me.trigger('refresh', me.roleCode);

                                // 删除成功划到顶部
                                // me.$leftnav.scrollTop(0);
                            }
                        },
                        error: function() {
                            confirm.show();
                        }
                    })
                });  
            });
        },

        destroy: function(){
            this.$el.off();
            _.each(['confirmdialogWidget','addCustomizeRoleWidget', 'mainRoleSetWidget'], function(item){
                this[item] && (this[item].destroy(), this[item] = null);
            }, this);
        }
    })
})
