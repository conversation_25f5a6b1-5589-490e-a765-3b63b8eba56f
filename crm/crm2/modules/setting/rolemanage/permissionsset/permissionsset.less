.crm-s-rolemanage .permissions-set {
	.content-wrap {
		position: absolute;
	    top: 0;
	    margin-bottom: 60px;
    	padding: 30px 0 0;
	    right: 0;
	    bottom: 0;
	    left: 20px;
		display: flex;
		flex-direction: column;

	    // TODO
	    .no-data {
	    	width: 100%;
		    height: 100%;
		    display: flex;
		    justify-content: center;
		    align-items: center;
	    }
	}
	.info-words{
		h3{
			font-size: 20px;
			color: #333;
		}
		p{
			font-size: 12px;
			color: #999;
		}
	}
	.info-words {
		max-width: 1030px;
		display: flex;
	}
	.info-words .search-wrap{
		.b-g-ipt{
			width: 150px;
		}
	}
	// 折叠
	.collapse-box {
		font-size: 24px;
		display: flex;
		align-items: center;
		cursor: pointer;
		margin-right: 20px;
		.collapse-icon-shouqi {
			display: none;
		}
	}
	.role-privilege-wrapper-scroll-box {
		flex: 1;
	}

	// 重置loading样式
	/*.crm-s-loading {
		margin: 0;
		.content {
			height: 80px;
		}
	}*/

	.btn-wrapper {
		display: flex;
		height: 50px;
		justify-content: center;
		align-items: center;
	}

	.out-role-wrapper {
		position: absolute;
		width: 100%;
		display: flex;
		height: 47px;
		border-bottom: 1px solid #eee;
		line-height: 47px;
		font-size: 14px;
	    color: var(--color-neutrals19);
	    .label {
	    	margin: 0 20px;
	    }
	    .back-btn {
	    	cursor: pointer;
	    	color: #407FFF;
	    	margin-left: 20px;
	    }
	    .b-g-btn {
	    	position: absolute;
		    right: 30px;
		    top: 8px;
	    }
	}
}

.crm-c-confirm-dialog {
	.b-g-btn-not-save {
		float: left;
		display: inline-block;
	    height: 32px;
	    line-height: 32px;
	    margin-left: 0;
	    padding: 0 20px;
	    border: 1px solid #ccc;
	    border-radius: 3px;
	    color: #787878;
	    text-align: center;
	    text-decoration: none;
	    cursor: pointer;
	    font-family: inherit;
	    vertical-align: middle;
	}
}