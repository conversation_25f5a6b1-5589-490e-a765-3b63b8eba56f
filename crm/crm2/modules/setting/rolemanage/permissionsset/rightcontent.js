define(function (require, exports, module){
    var util = require('crm-modules/common/util');
    var Tpl = require('./template/right-content-html');
    var errorTpl = require('./template/error-html');
    var RolePrivilege = require('./roleprivilege/index');
    var BTN_DISABLED_CLASS = 'b-g-btn-disabled';

    module.exports = Backbone.View.extend({
        ajax: null,

        initialize: function(options) {
            this.render(options);
        },

        render: function(options) {
            var me = this;
            var data = options.data; 
            me.options = options;
            me.roleCode = data.id || data.roleCode;
            me.roleType = data.type || data.roleType;

            me.isAllFold = false;

            // 暂无数据，直接返回
            if (!me.roleCode) return;

            //roletype为1为默认角色，为2为自定义角色
            me.$el.html(Tpl({
                description: data.description,
                rolename: data.rolename || data.roleName,
                rolecode: me.roleCode,
                roletype: me.roleType 
            }));

            // 隐藏搜索框
            if (me.options.searchData) {
                var $el = me.$('.j-s-field');
                $el.parent().hide();
            }

            // TODO
            // console.log(me.ajax);
            me.ajax && me.ajax.abort();
            me.ajax = util.FHHApi({
                url: '/EM1HPAASUdobj/roleApi/getRolePrivilegeWithLinkage',
                data: {
                    roleCode: me.roleCode,
                    roleType: me.roleType,
                    isOutRole: options.isOutRole,
                    appId: options.isOutRole ? 'PRM' : 'CRM' 
                },
                success: function(res){  
                    if( res.Result.StatusCode === 0 ){
                        me.rolePrivilegeData = res.Value;
                        me.roleprivilege && me.roleprivilege.destroy();
                        me.roleprivilege = new RolePrivilege({
                            el: me.$('.role-privilege-wrapper'),
                            roleCode: me.roleCode,
                            data: me.formatData(res.Value.privilegeLists),
                            linkData: res.Value
                        })

                        me.initOriginNosArr();

                        me.roleprivilege.on('check', function() {
                            me.$('.j-save').removeClass(BTN_DISABLED_CLASS);
                        });

                        // 获取搜索条件，执行搜索
                        if (me.options.searchData) {
                            me.$('.lazyload-item').each(function () {
                                var text = $(this).data().descapiname, $item = $(this), data = me.options.searchData;
                                data && text != data ? $item.hide() : $item.show();
                            });
                        }
                    }else {
                        me.$el.html(errorTpl());
                    }
                },
                complete: function () {
                    me.ajax = null;
                }
            }, {
                errorAlertModel: 1
            });
        },

        events: {
            'click .j-save': 'onSave',                                  
            'click .j-reset': 'onReset',                            
            'click .j-refresh': 'onRefresh',  
            'keyup .j-s-field': 'onKeyUp',                        
            'click .j-fold': 'onFold',                        
        },

        onKeyUp: function (e) {
            var me = this;
            var key = me.$('.j-s-field').val();
            if ((key && e.which == 13) || !key) {
                me.search(key);
            }
        },
        search: function (data) {
            var me = this;
            me.$('.lazyload-item').each(function () {
                var text = $(this).data().descapidisplayname || '', $item = $(this);
                data && text.toString().indexOf(data) < 0 ? $item.hide() : $item.show();
            });
        },
        onFold: function () {
            var me = this;
            me.isAllFold = !me.isAllFold;
            const $checkboxList = me.$('.lazyload-item .mn-checkbox-box'), 
                  $collapseIcon = me.$('.collapse-icon'),
                  $h3 = me.$('.checkbox-head-toggle');
            me.isAllFold ? $h3.removeClass('current') : $h3.addClass('current')
            me.isAllFold ? $checkboxList.hide() : $checkboxList.show();
            $collapseIcon.attr('xlink:href', me.isAllFold ? "#zhankai2" : "#shouqi");
        },

        // 格式化权限数据，将产品和商品数据拼在一起
        formatData: function(data) {
            return _.filter(data, function(item) {
                return !(item.descApiName === 'Visit' && CRM.control.isYunZhiJia);
            })
        },

        onSave: function(e){
            var me = this;
            var functionNosArr = [];
            var updateNosArr = [];

            if($(e.currentTarget).hasClass(BTN_DISABLED_CLASS)) return;

            me.$(".mn-selected").each(function (index,item){
                $(item).data('functionnumber') && !$(item).hasClass('j-set') && functionNosArr.push( $(item).data('functionnumber') );

                if ($(item).hasClass('look-item') && !$(item).hasClass('bi-item')) {
                    updateNosArr.push( $(item).data('functionnumber') );
                };
            });
            me.$('script').each(function(idx,ite) {
                $($(ite).html()).find(".mn-selected").each(function (index,item){
                    $(item).data('functionnumber') && functionNosArr.push( $(item).data('functionnumber') );

                    if ($(item).hasClass('look-item') && !$(item).hasClass('bi-item')) {
                        updateNosArr.push( $(item).data('functionnumber') );
                    };
                });
            });

            util.FHHApi({
                url: '/EM1HPAASUdobj/roleApi/updateRolePrivilege',
                data: {
                    roleType: me.roleType,
                    roleCode: me.roleCode,
                    funcCode: functionNosArr,
                    /*
                     * “查看列表”字段发生变更的functionCode,
                     * 使用initOriginNosArr记录初始化状态
                    */
                    updatedViewListFuncCode: (me.originNosArr.length > updateNosArr.length) ? _.difference(me.originNosArr, updateNosArr) : _.difference(updateNosArr, me.originNosArr)         
                },
                success: function( res ){
                    if( res.Result.StatusCode === 0 ){
                        util.remind(1, $t("保存成功"));
                        me.toSubmit = false;
                        me.$('.j-save').addClass(BTN_DISABLED_CLASS);

                        me.initOriginNosArr();
                    }
                }
            },{
                submitSelector: e ? $(e.currentTarget) : ''
            })
        },

        onReset: function(e){
            var me = this;

            if($(e.currentTarget).hasClass(BTN_DISABLED_CLASS)) return;
			var rconfirm;
			rconfirm = util.confirm($t("是否恢复默认设置？"), $t("提示"), function() {
				rconfirm.destroy();
				util.FHHApi({
					url: '/EM1HPAASUdobj/roleApi/resetRole',
					data: {
						roleCode: me.roleCode
					},
					success: function( res ){
						if( res.Result.StatusCode === 0 ){
							util.remind(1, $t("保存成功"));
							me.toSubmit = false;
							me.trigger('reset', me.options);
						}else {
							util.alert(res.Result.FailureMessage);
						}
					}
				},{
					errorAlertModel: 1
				})
			});
        },

        onRefresh: function() {
            this.render(this.options);
        },

        initOriginNosArr: function() {
            var me = this;

            me.originNosArr = [];
            me.$('.mn-selected.look-item:not(.bi-item)').each(function (index,item){
                $(item).data('functionnumber') && me.originNosArr.push( $(item).data('functionnumber') );
            });
            me.$('script').each(function(idx,ite) {
                $($(ite).html()).find(".mn-selected.look-item:not(.bi-item)").each(function (index,item){
                    $(item).data('functionnumber') && me.originNosArr.push( $(item).data('functionnumber') );
                });
            });
        },

        destroy: function(){
            this.$el.off();
            this.roleprivilege && (this.roleprivilege.destroy(), this.roleprivilege = null);
        }
    })
})


