/*
 * 角色权限设置
 * params
 *   roleCode 角色code 
 *   data     数据列表
 * events
 *   check    点击checkbox触发的事件
 * methods
 *   onCheck  设置角色权限
 *   onSet    设置字段权限
 */

define(function (require, exports, module){
    var FieldPrivilege = require('../fieldprivilege/index');
    var FieldPrivilegeTpl = require('../fieldprivilege/index-html');
    var Tpl = require('./index-html');
    var Link = require('./link');
    var Lazyload = require('./lazyload-any');
    var SELECTED_CLASS = 'mn-selected';

    module.exports = Backbone.View.extend({
        initialize: function(options) {
            this.roleCode = options.roleCode;
            this.$el.html(Tpl({
                list: options.data
            }));
			// this.initLazyload();
			
			this.link = new Link(options.linkData);
			this.link.init(options.data);
        },

        events: {
            'click .mn-checkbox-item': 'onCheck',                        
            'click .j-set': 'onSet',   
            'click .checkbox-head-toggle': 'onClickArrow',                                
        },
        // 权限列表折叠
        onClickArrow: function (e) {
            var $h3 = $(e.currentTarget), $ul = $h3.next();
            $ul.css('display') == 'none' ? $h3.addClass('current') : $h3.removeClass('current')
            $ul.slideToggle()
        },
        onCheck: function(e){
			var $target = $(e.currentTarget);
			var $itembox = $target.closest('.mn-checkbox-box');
			var $items = null;

			// 如果点击禁用状态的权限
			if ($target.hasClass('disabled-selected')) {
				/** 客户地址的新建和编辑权限受客户的编辑权限影响 */
				if (['AccountAddrObj||Add', 'AccountAddrObj||Edit'].indexOf($target.data('functionnumber')) != -1) {
					CRM.util.remind(3, $t("该权限受客户的【编辑】权限配置影响"));
				}
                e.stopPropagation();
                return false;
			}

			// 有编辑才能保存和重置
			this.trigger('check');
			$target.toggleClass(SELECTED_CLASS);

			// 选中
			if ($target.hasClass(SELECTED_CLASS)) {
				if ($target.hasClass('j-check-all')) {
					$items = $('.mn-checkbox-item', $itembox).not('.disabled-selected');
					$items.addClass(SELECTED_CLASS)
					this.link.update(this.$el, $items);
				} else {
					this.link.update(this.$el, $target);
				}
			//反选
			} else {
				if ($target.hasClass('j-check-all') || $target.hasClass('look-item')) { 
					this.link.unCheckedAll(this.$el, $target)
				} else {
					this.link.update(this.$el, $target);
				}
			}

			/** 更新全选按钮的状态 */
			this.link.updateAllStatus($itembox);
			/** 更新设置字段权限按钮的状态 */
			this.link.updateSetDisabledStatus($itembox);
			/** 更新查看列表按钮的状态 */
			this.link.updateLookStatus($itembox)
			
            e.stopPropagation();
            return false;
		},
        // 设置字段权限
        onSet: function(e){
            e.stopPropagation()
            var me = this; 
            var $target = $(e.currentTarget);
            var datas = $target.data();

            if ($target.hasClass('disabled-btn')) return;

            // crm管理员字段权限不能设置
            var disabled = me.roleCode === CRM.config.MANAGER_ROLE_CODE;

            // 新建、导入、销售订单的编辑勾选，必填字段不可编辑
            var readonly = _.some($target.parent().next().find('.readonly-item'), function(item) {
                return $(item).hasClass(SELECTED_CLASS);
            });


            me.fieldprivilege && (me.fieldprivilege.destroy(), me.fieldprivilege = null);
            me.fieldprivilege = new FieldPrivilege({
                title: $t("设置") + datas.name + $t("字段权限"),
                showBtns: !disabled,
                className: 'crm-s-rolemanage'
            });
            me.fieldprivilege.on('submit', function(data) {
                CRM.util.FHHApi({
                    url: '/EM1HPAASUdobj/roleApi/updateRoleObjectFieldPrivilege',
                    data: {
                        descApiName: datas.objectid,
                        roleCode: me.roleCode,
                        fieldPermission: data
                    },
                    success: function(res){
                        if( res.Result.StatusCode == 0 ){
							CRM.util.remind(1, $t("操作成功"));
                            me.fieldprivilege.destroy();
                        }
                    }
                }, {
                    submitSelector: me.fieldprivilege.element.find('.b-g-btn')
                });
            })
            me.fieldprivilege.show();

			CRM.util.FHHApi({
                url: '/EM1HPAASUdobj/roleApi/getRoleObjectFieldPrivilege',
                data: {
                    roleCode: me.roleCode,
                    descApiName: datas.objectid
                },
                success: function(res){
                    if(res.Result.StatusCode == '0'){
                        me.fieldprivilege.setContent(FieldPrivilegeTpl({
                            roleCode: me.roleCode,
                            fieldInfoList: res.Value,
                            disabled: disabled,
                            readonly: readonly
                        }));
                        me.fieldprivilege.resizedialog();
                    }else{
						CRM.util.remind(3, $t("操作失败"));
                    }
                }
            }, {
                errorAlertModel: 1
            });
        },

        initLazyload: function() {
            new Lazyload(this.$('.lazyload-item'));    
		},
		
        destroy: function(){
            this.$el.off();
			this.fieldprivilege && (this.fieldprivilege.destroy(), this.fieldprivilege = null);
			this.link = null;
        }
    })
});
