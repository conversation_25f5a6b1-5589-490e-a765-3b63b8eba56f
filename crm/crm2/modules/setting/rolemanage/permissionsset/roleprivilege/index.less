.crm-s-rolemanage .role-privilege-wrapper {
	max-width: 1030px;
    padding-bottom: 20px;
	.checkbox-head{
		display: flex;
		align-items: center;
		font-size: 16px;
		margin-top: 30px;
		margin-bottom: 10px;
		margin-right: 20px;
		padding: 6px;
		border-bottom: 1px solid #eee;
		color: #333;
		background-color: #F4F8FE;
		cursor: pointer;
		&.current .ico-arrow{
			background-position: -20px -20px;
		}
		.ico-arrow {
			width: 20px;
			height: 20px;
			.img-retina("@{imgUrl}/<EMAIL>","@{imgUrl}/<EMAIL>",40px,40px);
			background-repeat: no-repeat;
			background-position: 0 -20px;
			background-size: 40px 40px;
		}
	}

	.bi-object-title-desc {
		display: inline-block;
		width: 16px;
		height: 16px;
		margin-left: 8px;
		.img-retina("@{imgUrl}/icon-qus.png", "@{imgUrl}/icon-qus_2x.png", 48px, 16px);
		font-size: 12px;
		color: var(--color-neutrals11);
		cursor: help;
	}
	.set-btn {
		font-size: 13px;
		color: #3487e2;
		margin-left: 16px;
		cursor: pointer;
	}
	.disabled-btn {
		color: #ccc;
		cursor: inherit;
	}
	.checkbox-title {
		width: 94px;
		margin-top: 10px;
	}
	.checkbox-item {
		float: left;
		width: 190px;
		padding: 10px 15px 0 0;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		word-break: keep all;
	}
	.mn-checkbox-item {
		margin-right: 5px;
	}
	.mn-checkbox-box{
		// display: none;
		margin-left: 10px;
	}
}
