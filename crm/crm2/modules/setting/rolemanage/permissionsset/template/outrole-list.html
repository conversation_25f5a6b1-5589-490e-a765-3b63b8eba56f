<ul class="customize-rolelist">
	<div class="btn-wrapper">
		<div class="b-g-btn j-add">+{{$t("新建角色")}}</div>
	</div>
	##if(roles.length){##
		##_.each(roles, function(item,index){##
			##_.each(item.roleInfoList, function(ite, idx){##
			<li class="item ##if(ite.roleCode === roleCode){## cur##}##" data-id="{{ite.roleCode}}" data-type="{{ite.roleType}}" data-rolename="{{ite.roleName}}" data-description="{{ite.description}}">
				<div class="name" title="{{ite.roleName}}">{{ite.roleName}}</div> 
				<div class="ops">
					<span class="crm-ico-edite" action-type="reject"></span><span class="crm-ico-del" action-type="reject"></span>
				</div>
			</li>
			##})##
		##})##
	##}else{##
		<div>{{$t("暂无角色")}}</div>
	##}## 
</ul>