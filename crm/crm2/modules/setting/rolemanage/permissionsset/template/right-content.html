##var loadTpl = require('crm-modules/setting/common/loading/loading-html');##
<div class="content-wrap">
	##if(rolename){##
		<div class="info-words">
			<div style="flex: 1">
				<h3>{{rolename}}</h3>
				<p>{{description || '--'}}</p>
			</div>
			<div class="search-wrap"><span>{{$t('搜索')}}:</span><input class="b-g-ipt j-s-field" placeholder="{{$t('搜索对象名')}}"/></div>
			<span class="j-fold collapse-box">
				<svg class="crm-icon" aria-hidden="true">
					<use class="collapse-icon" xlink:href="#shouqi"></use>
				</svg>
			</span>
		</div>
		<div class="crm-scroll role-privilege-wrapper-scroll-box">
			<div class="role-privilege-wrapper">
				{{loadTpl()}}
			</div>
		</div>
	##}else{##
		<div class="no-data">{{$t("暂无数据")}}</div>
	##}##
</div>
##if(rolename){##
<div class="checkbox-btn">
	<div class="b-g-btn j-save b-g-btn-disabled">{{$t("保存")}}</div>
	##if(roletype !== 2 && rolecode != 'personnelrole'){##
	<div class="j-reset reset-btn {{rolecode === CRM.config.MANAGER_ROLE_CODE ? 'b-g-btn-disabled' : ''}}">
		{{$t("恢复默认")}}
	</div>
	##}##
</div>
##}##
