define(function (require, exports, module){
	var Table = require('crm-widget/table/table');
	var Dialog = require("crm-widget/dialog/dialog");
	var EditPowerDialog = require('./editpowerdialog');
	var util = require('crm-modules/common/util');
	var tmpUtil = require('../util');

	module.exports = Backbone.View.extend({
		initialize: function(options) {
			this.roleList = options.data;
			this.render();
		},

		events: {
			'click .j-add': 'onAdd',
			'click .j-del': 'onDel',
			'click .j-export': 'onExport',
        },

        render: function() {
        	var me = this;
            var btns = [];
            var functionCodes = CRM.control.functionCodes;
            if (_.contains(functionCodes, 'permission_business_role_manage')) {
                btns.push({text:$t("添加员工"),className: 'j-add'})
            }
            if (_.contains(functionCodes, 'permission_business_role_export')) {
                btns.push({text:$t("导出"),className: 'j-export'})
            }
        	me.dt = new Table({
                $el: me.$el,
                url: '/EM1HPAASUdobj/userApi/crmUserList',
                requestType:   'FHHApi',
                showFilerBtn: false,
                showMultiple: true,
                trHandle: false,
                operate: {
                    pos: 'T',
                    btns: btns
                },
                search: {
					'pos': 'T',
					'placeHolder': $t("搜索员工"),
					'type': 'username',
                    'fileName': 'username'
                },
                batchBtns: [{
                    text: $t("删除"),
                    className: 'j-del'
                }],
                postData: {
                    deptId: ''
                },
                columns: [{
						data: 'name',
                        title: $t("姓名")
                    },{
                        data: 'mainDepartment',
                        title: $t("crm.主属部门")
                    },{
						data: 'departmentInfoList',
						title: $t("附属部门")
					},{
                        data: 'post',
                        title: $t("职位"),
                        width: 120
                    },{
                        data: 'defualtRoleName',
                        title: $t("主角色")
                    },{
                        data: 'roleList',
                        title: $t("角色"),
                        render: function(data) {
                            return _.map(data, function(item) {
                                        return item.roleName;
                                   }).join($t("、")) || '--'
                        }
                    },{
                        data: '',
						lastFixed: true,
						width: 180,
                        title: $t("操作"),
                        render: function(data, type, full){
                            var className = (data.length === 1 && data[0].roleCode === CRM.config.MANAGER_ROLE_CODE) ? 'disable' : 'j-copy';
                            return '<div class="table-ops"><a class="j-edit">'+ $t("编辑")+'</a><a class="j-del ' + className + '">'+ $t("删除")+'</a><a class="'+ className +'">'+ $t("复制角色到员工") +'</a></div>'
                        }
                    }],
                formatData: function(data) {
                    return {
                        totalCount: (data.page && data.page.totalCount) || 0,
						data: me.getformatData(data.map, data.userInfo)
                    }
                },
                initComplete: function($el){
                    $('.first-target-item', $el).before([
                        '<div class="item dt-sc-box batch-c">',
                            '<span class="item-tit">'+ $t("范围：") +'</span>',
                            '<div class="item-con selector j-selector"></div>',
                        '</div>'].join(''));
                    tmpUtil.initSelector(me.$('.j-selector'), function(data, selector) {
                        me.selector = selector;
                        me.dt.setParam({deptId: data.join(',')}, true, true);
                    });
                }
            });
			me.dt.on('trclick', function (data, $tr, $target) {
                if( $target.hasClass('j-edit') ){
                    me.editHandle( data );
				} else if ($target.hasClass('j-del')) {
                    me.onDel( data );
                }else if( $target.hasClass('j-copy') ){
                    me.copyHandle( data );
                }
            });
            me.dt.on('selector.change', function(data) {
                me.dt.setParam({deptId: data.join(',')}, true, true);
            });
        },

        /*
         *   格式化数据
         *   格式化成表格的数据结构
        */
		getformatData: function (map, userInfo) {
            var tableData = [];
			var me = this;
			_.each(map, function (item, employeeId) {
				let info = userInfo[employeeId];
				if (info) {
					tableData.push({
						employeeId: employeeId,
						name: info.name,
						mainDepartment: tmpUtil.getMainDepartment(info),
						departmentInfoList: me.getDeparts(info),
						post: tmpUtil.getPostByEmployeeId(info),
						defualtRoleName: (_.findWhere(item, {defaultRole: true}) || {}).roleName || '',
						// 弄成这种数据结构方便编辑时使用
						roleList: _.map(item, function (ite) {
							return {
								roleCode: ite.roleCode,
								roleName: ite.roleName,
								defaultRole: ite.defaultRole
							}
						})
					});
				}
            });
            return tableData;
        },

		getDeparts: function(info) {
			var list = info.departmentInfoList;
			var main = info.mainDepartment;
			var names = []
			_.each(list, function(item) {
				if (main && item.departmentId != main.departmentId) {
					names.push(item.departmentName);
				}
			})
			return names.length > 0 ? names.join('、') : '--';
		},


		// 添加员工
        onAdd: function(e){
            var me = this;
            me.addPowerWidget && (me.addPowerWidget.destroy(),me.addPowerWidget = null);
            me.addPowerWidget = new EditPowerDialog({
                title: $t("添加员工"),
                type: 'add',
                FiledList: me.roleList
            });
            me.addPowerWidget.on('success', function(){
                me.trigger('update_quota');
                me.dt.setParam({}, true);
            });
            me.addPowerWidget.show({});
        },

        onExport: function(e) {
            var me = this;
            var title = $t("全部CRM用户");
            util.FHHApi({
                url: '/EM1HPAASUdobj/bulkimport/exportUser',
                data: {
                    roleCode: ''
                },
                success: function(res){
                    if( res.Result.StatusCode == 0 ){
                        var value = res.Value;
                        var url = CRM.util.getFscLink(value.path + '.' + value.ext, value.file_name + '.' + value.ext, true);
                        tmpUtil.showExportDialog(url, title);
                    }else {
                        tmpUtil.showExportDialog('', title);
                    }
                }
            }, {
                submitSelector: me.$('.j-export')
            })
        },

        onDel: function(data) {
            var me = this;
            var selectArr = me.dt.getCheckedData();
            var confirm = util.confirm($t("确定从CRM用户中删除员工")+"<br/>"+$t("此操作会删除CRM管理员以外的所有角色。"),$t("提示"),function(){
                var req = [];
                _.each(selectArr, function (item, index){
                    req.push( item.employeeId );
                });

                util.FHHApi({
                    url: '/EM1HPAASUdobj/userApi/removeCrmUser',
                    data: {
                        userIds : data.employeeId ? data.employeeId : req.join(',')
                    },
                    success: function(res){
                        if( res.Result.StatusCode == 0 ){
                            util.remind(1, $t("操作成功"));
                            confirm.destroy();
                            me.trigger('update_quota');
                            me.dt.setParam({}, true);
                        }
                    }
                }, {
                    submitSelector: confirm.$('.b-g-btn')
				})
            });
        },

        // 编辑员工权限
        editHandle: function(data){
            var me = this;
            me.editPowerWidget && (me.editPowerWidget.destroy(),me.editPowerWidget = null);
            me.editPowerWidget = new EditPowerDialog({
                title: $t("编辑角色"),
                type: 'edit',
                FiledList: me.roleList
            });
            me.editPowerWidget.on('success',function(){
                me.trigger('update_quota');
				me.dt.setParam({}, true);
            });
            me.editPowerWidget.show({
                data: data
            });
        },

        copyHandle: function(data) {
            var me = this;
            me.copyPowerWidget && (me.copyPowerWidget.destroy(),me.copyPowerWidget = null);
            me.copyPowerWidget = new EditPowerDialog({
                title: $t("复制角色到员工"),
                type: 'copy',
                FiledList: me.roleList
            });
            me.copyPowerWidget.on('success', function(){
                me.trigger('update_quota');
                me.dt.setParam({}, true);
            });
            me.copyPowerWidget.show({
                data: data
            });
        },

        destroy: function(){
            this.$el.off();
            _.each(['addPowerWidget', 'editPowerWidget', 'copyPowerWidget','dt', 'selector'], function(item){
                this[item] && (this[item].destroy(), this[item] = null);
            }, this);
        }
	})
})
