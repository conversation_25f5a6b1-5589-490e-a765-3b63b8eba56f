/**
 * 员工角色分配--添加员工权限
 */
define(function (require,exports,module){
    var util = require('crm-modules/common/util');
    var tempUtil = require('../util');
    var Dialog = require("crm-widget/dialog/dialog");
    var SelectBar = require('crm-widget/selector/selector');
    var Select = require('crm-widget/select/select');
    var mainHtml = require('./template/edit-power-html');

    var EditPower = Dialog.extend({
        attrs: {
            title: $t("添加员工"),
            content: '<div class="crm-loading "></div>',
            size: 'md',
            showBtns:  true,
            showScroll: false,
            type: 'add',
            delEmp: [],
            data: null,
            FiledList: null,
            className: 'crm-s-rolemanage',
			zIndex: 800
        },

        events: {
            'click .form-item .mn-checkbox-item': 'onSelect',
            'click .j-checkbox': 'onCheck',
            'click .b-g-btn': 'onSubmit',
            'click .b-g-btn-cancel': 'destroy'
        },
        render: function(){
            return EditPower.superclass.render.call(this);
        },

        //显示
        show: function(opts){
            var me = this;
            var type = me.get('type');

            me.widget = [];
            me.set(opts);
            me.isEditPower = type === 'edit' ? true : false;
            var result = EditPower.superclass.show.call(me);
            var roleList = me.get('data') ? me.get('data').roleList : [];

            me.setContent( mainHtml({
                type: type,
                FiledList: me.get('FiledList'),
                roleList: me.get('data') ? _.map(roleList, function(item) {
                    return item.roleCode;
                }) : me.get('roleCode')
            }));
            !me.isEditPower && me.widgetEmployeeFn();

            // 处理主角色列表项
            if (me.$('.form-item .mn-selected').length === 0) {
                me.selectData = [{
                    value: '',
                    name: $t("请选择")
                }];
            }else {
                me.selectData = [];
                if (type === 'edit' || type === 'copy') {
                    // 将主角色提到第一位
                    _.each(_.sortBy(roleList, function(item){return !item.defaultRole}), function (item) {
						me.selectData.push({
                            value: item.roleCode,
                            name: item.roleName
                        });
                    });

                    // 主角色id
                    me.mainRoleId = me.selectData[0].value;
                }else{
                    _.each(me.$('.form-item .mn-selected'), function (item) {
						me.selectData.push({
                            value: $(item).data('id'),
                            name: $(item).data('rolename')
                        });
                    });
                }
            }

            me.initSelect(type === 'add');
            me.resizedialog();
            return result;
        },

        //选中按钮
        onSelect: function(e){
            var $target = $(e.currentTarget);
            var me = this;
            if( $target.hasClass('disabled-selected') ) {
                e.stopPropagation();
                return false;
            }
            $target.toggleClass('mn-selected');

            var checkboxBtn = me.$('.j-checkbox');
            var $selectedItem = me.$('.form-item .mn-selected');
            if ($selectedItem.length) {
                me.selectData = [];
                _.each(_.sortBy($selectedItem, function(item){return $(item).data('id') !== me.mainRoleId}), function(item) {
                    me.selectData.push({
                        value: $(item).data('id'),
                        name: $(item).data('rolename')
                    });
                });
                me.initSelect(false);
                checkboxBtn.removeClass('disabled-selected');
            }else {
                me.selectData = [{
                    value: '',
                    name: $t("请选择")
                }];
                me.initSelect(true);
                checkboxBtn.addClass('disabled-selected');
            }
            e.stopPropagation();
            return false;
        },

        onCheck: function(e) {
            var $target = $(e.currentTarget);
            if(!$target.hasClass('disabled-selected')) {
                $target.toggleClass('mn-selected');
            }
            e.stopPropagation();
            return false;
        },

        //初始化选择员工组件
        widgetEmployeeFn: function(){
            var me = this;
			tempUtil.getEmployeesAndCircles('permission_business_role_manage').then(function (res) {
				var tab1 = {
					id: 'member',
					type: 'sort',
					title: $t('同事_s'),
					data: res.employees
				}
				var tab2 = {
					id: 'group',
					type: 'tree',
					title: $t('部门_s'),
					data: res.circles
				};
                var employeeWidget = new SelectBar({
                    $wrap:  me.$('.widget-employee'),
                    zIndex: me.get('zIndex') * 1 + 10,
					tabs: [tab1, tab2],
					showProfileCard: true,
					// group:  true,
					// member: true,
                    label: $t("选择员工或部门"),
                    defaultSelectedItems: (function(){
                        var data = {member: [], group: []};
                        _.each(me.get('employee') || [], function(item) {
                            if (item.type == 'p') {
                                data.member.push(item.id);
                            } else {
                                data.group.push(item.id);
                            }
                        });
                        return data;
                    }())
                });

				employeeWidget.on('addItem', function() {
                    me.$('.widget-employee').next().remove();
                });

				me.employeeWidget = employeeWidget;
			})
        },

        initSelect: function(disabled) {
            var me = this;
            this.mainRoleWidget && this.mainRoleWidget.destroy();
            this.mainRoleWidget = new Select({
                $wrap: this.$('.main-role-select'),
                zIndex: 1010,
                disabled: disabled,
                options: this.selectData
            });
		},

		onSubmit() {
			var me = this;
			var EmployeeIDList = [],
				CircleIDList = [],
				roleList = [],
				type = me.get('type'),
				submitData;
			var $selectedItem = me.$('.form-item .mn-selected');

			var empCircleData = me.employeeWidget ? me.employeeWidget.getValue() : { member: [], group: [] };

			if (!me.isEditPower && (empCircleData.member.length + empCircleData.group.length == 0)) {
				util.showErrmsg(me.$('.widget-employee'), $t("请选择员工或部门"));
				return false;
			}
			if (!$selectedItem.length) {
				util.remind(3, $t("请选择员工角色"));
				return false;
			}
			EmployeeIDList = empCircleData.member;
			CircleIDList = empCircleData.group.map(function (val) { return parseInt(val) });

			$selectedItem.each(function (index, item) {
				roleList.push($(item).data('id'));
			});

			if (type === 'add' || type === 'add2') {
				submitData = {
					url: '/EM1HPAASUdobj/userApi/batchAddUsersToRoles',
					data: {
						employeeIds: _.uniq(EmployeeIDList),
						departmentIds: _.uniq(CircleIDList),
						roleCodes: roleList.join(','),
						majorRoleCode: me.mainRoleWidget.getValue(),
						updateFlag: !me.$('.j-checkbox').hasClass('mn-selected')
					}
				};
			} else if (type === 'edit') {
				submitData = {
					url: '/EM1HPAASUdobj/userApi/updateUserRole',
					data: {
						userId: me.get('data').employeeId,
						roleCodes: roleList.join(','),
						majorRoleCode: me.mainRoleWidget.getValue(),
					}
				};
			} else {
				submitData = {
					url: '/EM1HPAASUdobj/userApi/copyUserRoleToUsers',
					data: {
						employeeIds: _.uniq(EmployeeIDList),
						departmentIds: _.uniq(CircleIDList),
						userIdOld: me.get('data').employeeId,
						majorRoleCode: me.mainRoleWidget.getValue(),
					}
				};
			};

			me.submit(submitData);
		},

        //保存设置
        submit: function( submitData ){
            var me = this;
            util.FHHApi({
                url: submitData.url,
                data: submitData.data,
                success: function(res){
                    if( res.Result.StatusCode == 0 ){
                        me.trigger('success');
                        util.remind(1, $t("操作成功！"))
                        me.hide();
                    } else {
                        util.alert(res.Result.FailureMessage);
                    }
                }
            }, {
                submitSelector: me.$('.b-g-btn'),
                errorAlertModel: 1
            });
        },

        hide: function(){
            var me = this;
            _.each( me.widget ,function (item){
                item && item.destroy && item.destroy();
            });
            me.employeeWidget && me.employeeWidget.destroy();
            me.widget = [];
            return EditPower.superclass.hide.call(me);
        },

        destroy: function(){
            var me = this;
            me.employeeWidget && me.employeeWidget.destroy();
            me.mainRoleWidget && me.mainRoleWidget.destroy();
            _.each( me.widget ,function (item){
                item && item.destroy && item.destroy();
            });
            return EditPower.superclass.destroy.call(me);
        }
    });
    module.exports = EditPower;
})
