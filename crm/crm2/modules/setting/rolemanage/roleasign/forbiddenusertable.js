define(function (require, exports, module){
	var Table = require('crm-widget/table/table');
	var util = require('crm-modules/common/util');
	var tmpUtil = require('../util');

	module.exports = Backbone.View.extend({
		initialize: function(options) {
			this.roleList = options.data; 
			this.render();
		},

        events: {
            'click .j-recovery': 'onRecovery'                  
        },

        render: function() {
        	var me = this;
        	me.dt = new Table({
                $el: me.$el,
                url: '/EM1HPAASUdobj/forbiddenUsesApi/queryRoleInfoListByForbiddenUsers',
                requestType:   'FHHApi',
                showFilerBtn: false,
                showMultiple: true,
                trHandle: false,
                checked: {idKey: 'employeeId'},
                search: {
                    'pos': 'T', 
                    'placeHolder': $t("搜索员工"), 
                    'type': 'username', 
                    'fileName': 'username'
                },
                batchBtns: [{
                    text: $t("恢复"),
                    className: 'j-recovery'
                }],
                postData: {
                    forbiddenFlag: true
                },
                columns: [{
                        data: 'name',
                        title: $t("姓名"),
                    },{
                        data: 'mainDepartment',
                        title: $t("crm.主属部门")
                    },{
                        data: 'post',
                        title: $t("职位"),
                        width: 120
                    },{
                        data: 'defualtRoleName',
                        title: $t("主角色")
                    },{
                        data: 'roleNames',
                        title: $t("角色")
                    },{
                        data: '',
						lastFixed: true,
						width: 180,
                        title: $t("操作"),
                        render: function(){
                            return '<div class="table-ops"><a class="j-recovery">'+ $t("恢复") +'</a></div>'
                        }
                    }],
                formatData: function(data) {
                    return {
                        totalCount: (data.page && data.page.totalCount) || 0,
						data: me.getformatData(data.userRoles, data.userInfo)
                    }
                },
                initComplete: function($el){
                    $('.first-target-item', $el).before([
                        '<div class="item dt-sc-box batch-c">',
                            '<span class="item-tit">'+ $t("范围：") +'</span>',
                            '<div class="item-con selector j-selector"></div>',
                        '</div>'].join(''));
                    tmpUtil.initSelector(me.$('.j-selector'), function(data, selector) {
                        me.selector = selector;
                        me.dt.setParam({deptId: data.join(',')}, true, true);
                    });
                }
            });
            me.dt.on('trclick', function(data,$tr,$target) { 
                if($target.hasClass('j-recovery')){
                    me.onRecovery(data);
                }
            });
            me.dt.on('selector.change', function(data) {
                me.dt.setParam({deptId: data.join(',')}, true, true);
            });
        },

        /*
         *   格式化数据
         *   格式化成表格的数据结构
        */
		getformatData: function (userRoles, userInfo) {
            var tableData = [];
			_.each(userRoles, function (item, employeeId) {
				let info = userInfo[employeeId];
                var defualtRole = item.defualtRole;
                var roleInfoList = item.roleInfoList;
                tableData.push({
					employeeId: employeeId,
					name: info.name,
                    mainDepartment: tmpUtil.getMainDepartment(info),
                    post: tmpUtil.getPostByEmployeeId(info),
                    defualtRoleName: (_.findWhere(roleInfoList, {roleCode: defualtRole}) || {}).roleName || '--',
                    roleNames: _.map(roleInfoList, function(ite) {
                                    return ite.roleName
                               }).join($t("、")) || '--'
                });
            });
            return tableData;
        },

        onRecovery: function(data) {
            var me = this; 
            var selectArr = me.dt.getRemberData() || [];
            var length = selectArr.length || 1;
            var confirm = util.confirm('<div style="text-align: center;">'+ $t('本操作将恢复{{length}}个用户的原角色<br/>（包含角色与主角色）', {length: length})+'</div>',$t("提示"),function(){
                util.FHHApi({
                    url: '/EM1HPAASUdobj/forbiddenUsesApi/updateForbiddenUses',
                    data: {
                        orgIds : data.employeeId ? [data.employeeId] : _.map(selectArr, function(item) {return item.employeeId}),
                        forbiddenFlag: false
                    },
                    success: function(res){
                        if(res.Value.success){
                            util.remind(1, $t("操作成功"));
                            me.dt.setParam({}, true);
                            me.trigger('update_quota');
                        }else {
                            util.alert('<div style="text-align: center;">' + $t('配额不足!您还可以恢复【{{res.Value.quota}}】个用户，建议您联系纷享客服增购。', {"res.Value.quota": res.Value.quota}) + '</div>');
                        }
                        confirm.destroy();
                    }
                }, {
                    submitSelector: confirm.$('.b-g-btn')
                })     
            });
        },

        destroy: function(){
            this.$el.off();
            _.each(['dt', 'selector'], function(item){
                this[item] && (this[item].destroy(), this[item] = null);
            }, this);
        }
	})
})
