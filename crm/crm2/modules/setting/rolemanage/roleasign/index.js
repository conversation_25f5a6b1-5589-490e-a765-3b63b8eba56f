/*
 *   员工角色分配
 */

define(function (require, exports, module){
    var indexTpl = require('./template/index-html');
    var LeftNav = require('./leftnav');
    var allUserTable = require('./allusertable');
    var forbiddenUserTable = require('./forbiddenusertable');
    var userTable = require('./usertable');

    module.exports = Backbone.View.extend({
        initialize: function() {
            this.el.innerHTML = indexTpl();
            
            this.render('all');
        },

        render: function(roleCode) {
            var me = this;
            var $wrapper = me.$('.right-wrapper');

            /*
             * 左侧导航点击类型
             * 包括click_item、click_all_item、click_forbidden_item
             */
            var clickType = _.contains(['all', 'forbidden'], roleCode) ? ('_' + roleCode + '_') : '_';

            new Promise(me.getRoleList).then(function(data) {
                me.destroy();
                me.leftnav = new LeftNav({
                    el: me.$('.leftnav'), 
                    data: data,
                    roleCode: roleCode 
                });

                me.leftnav.on('click_item', function(rolecode) {
                    me.rightcontent && me.rightcontent.destroy();
                    me.rightcontent = new userTable({
                        el: $wrapper, 
                        data: data, 
                        roleCode: rolecode, 
                        roleName: me.$('.leftnav cur').data('rolename')
                    });
                })
                me.leftnav.on('click_all_item', function() {
                    me.rightcontent && me.rightcontent.destroy();
                    me.rightcontent = new allUserTable({el: $wrapper, data: data});
                })
                me.leftnav.on('click_forbidden_item', function() {
                    me.rightcontent && me.rightcontent.destroy();
                    me.rightcontent = new forbiddenUserTable({el: $wrapper, data: data});
                })
 

                me.leftnav.trigger('click' + clickType + 'item', roleCode);

                me.leftnav.on('refresh', function(rolecode) {
                    me.render(rolecode);
                })
                me.rightcontent.on('update_quota', function() {
                    me.leftnav.updateQuota();
                })
            })
        },

        getRoleList: function(resolve, reject){
            CRM.util.FHHApi({
				url:'/EM1HPAASUdobj/roleApi/roleListWithOutPersonnelRole',
                success: function(res){
                    if (res.Result.StatusCode === 0) {
                        var roleList = _.compact(res.Value);

                        var customizeIndex = CRM.util.findIndex(roleList, function(item) {
                            return item.groupType === 1
                        });
                        
                        // 初始化自定义角色
                        if (customizeIndex === -1) {
                            roleList.push({
                                groupName: $t("自定义角色"),
                                groupType: 1,
                                roleInfoList: []
                            })
                        }

                        resolve(roleList);
                    }
                }
            }); 
        },

        destroy: function(){
            _.each(['leftnav', 'rightcontent'], function(item){
                this[item] && (this[item].destroy(), this[item] = null);
            }, this);
        }
    });
});
