define(function (require, exports, module){
    var util = require('crm-modules/common/util');
    var Dialog = require("crm-widget/dialog/dialog");
    var MainRoleSet = require('../mainroleset/mainroleset');
    var leftNavTpl = require('../template/leftnav-html'); 
    var EditCustomizeRole = require('../editcustomizerole/editcustomizerole');

    module.exports = Backbone.View.extend({
        initialize: function(options) {
            var data = _.compact(options.data);
            
            this.roleList = data;
            this.roleCode = options.roleCode;
            this.searchRoleList = null;

            this.el.innerHTML = leftNavTpl({
                roles: this.roleList, 
                isPermissionsSet: false,
                roleCode: options.roleCode,
                searchRoleList: this.searchRoleList
            });
            this.updateQuota();
        },

        events: {
            'click .j-add': 'onAdd',  
            'click .item': 'onClickItem',    
            'click .checkbox-head-toggle': 'onClickArrow',
            'keyup .j-s-field': 'onKeyUp',
        },

        // 角色列表折叠
        onClickArrow: function (e) {
            var $div = $(e.currentTarget), $itemBox = $div.next();
            $itemBox.css('display') == 'none' ? $div.addClass('current') : $div.removeClass('current')
            $itemBox.slideToggle()
        },
        // 搜索
        onKeyUp: function (e) {
            var me = this;
            var key = me.$('.j-s-field').val();
            if ((key && e.which == 13) || !key) {
                me.search(key);
            }
        },
        search: function (data) {
            var me = this;
            if (data) {
                var list = [];
                me.$('.item_box .item').each(function () {
                    var text = $(this).data().rolename || '';
                    if (data && text.toString().indexOf(data) >= 0) {
                        list.push($(this).data())
                    }
                });
                me.searchRoleList = list;
                me.el.innerHTML = leftNavTpl({
                    roles: me.roleList,
                    isPermissionsSet: false,
                    roleCode: me.roleCode,
                    searchRoleList: me.searchRoleList
                });
                me.updateQuota();
                me.$('.rolelist-box').hide();
            } else {
                me.searchRoleList = null;
                me.$('.rolelist-box').show();
                me.$('.search-rolelist-box').remove();
                me.$(`.item[data-id=${me.roleCode}]`).addClass('cur');
            }
            // me.el.innerHTML = leftNavTpl({
            //     roles: me.roleList,
            //     isPermissionsSet: false,
            //     roleCode: me.options.roleCode,
            //     searchRoleList: me.searchRoleList
            // });
            // me.updateQuota();
            // if(me.searchRoleList && me.searchRoleList.length >= 0) me.$('.rolelist-box').hide();
            me.$('.j-s-field').val(data).focus();
        },

        updateQuota: function() {
            var me = this;
            util.FHHApi({
                url: '/EM1HPAASUdobj/userApi/queryQuota',
                success: function(res){
                    if( res.Result.StatusCode == 0 ){
                        var value = res.Value;
                        me.$('.quota').html(value.userNum + '/' + value.quota);
                        me.$('.total').html(value.total);
                    }
                }
            })
        },

        onAdd: function(e) {
            e.stopPropagation();
            var me = this;
            var roleList = [];

            _.each(me.roleList, function(item) {
                _.each(item.roleInfoList, function(ite) {
                    if (ite.roleCode !== CRM.config.MANAGER_ROLE_CODE) {
                        roleList.push({
                            value: ite.roleCode,
                            name: ite.roleName
                        })
                    };
                });
            });
            me.addCustomizeRoleWidget && me.addCustomizeRoleWidget.destroy();
            me.addCustomizeRoleWidget = new EditCustomizeRole({
                title: $t("新建角色"),
                type: 'add',
                roleList: roleList,
            });
            me.addCustomizeRoleWidget.on('success',function(data){
                // 新建角色才能复制角色权限
                if (data.sourceRoleCode) {
                    util.FHHApi({
                        url: '/EM1HPAASUdobj/roleApi/copyRole',
                        data: {
                            destRoleCode: data.destRoleCode,
                            sourceRoleCode: data.sourceRoleCode
                        },
                        success: function( res ){
                            if (res.Result.StatusCode == 0) {
                                util.remind(1, $t("新建成功"));
                            }
                        }
                    });
                }else {
                    util.remind(1, $t("新建成功"));
                }

                me.trigger('refresh', data.destRoleCode);
            });
            me.addCustomizeRoleWidget.show();
        },

        onClickItem: function(e) {
            var $target = $(e.currentTarget);

            if($target.hasClass('cur')) return;
            this.$('.item').removeClass('cur');
            $target.addClass('cur');
            
            if ($target.hasClass('j-all')) {
                this.trigger('click_all_item');
            }else if ($target.hasClass('j-forbidden')) {
                this.trigger('click_forbidden_item');
            }else {
                this.roleCode = $target.data('id');
                this.trigger('click_item', $target.data('id'));
            }
        },

        destroy: function(){
            this.$el.off();
            this.addCustomizeRoleWidget && (this.addCustomizeRoleWidget.destroy(), this.addCustomizeRoleWidget = null);
        }
    })
})