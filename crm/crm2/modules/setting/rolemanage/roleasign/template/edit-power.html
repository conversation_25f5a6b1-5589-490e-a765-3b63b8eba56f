##if(type === 'edit'){##
<div class="crm-g-form mn-checkbox-box edit-power-box">
	##_.each(FiledList,function(item,index){##
	<div class="form-item">
		<label class="fm-lb">{{item.groupName}}</label>
		<div class="fm-wrap">
			##_.each(item.roleInfoList, function(ite, ind){##
			<div class="checkbox-item">
				<span class="mn-checkbox-item {{roleList.indexOf(ite.roleCode) > -1 ? 'mn-selected' : ''}} {{ite.roleCode === CRM.config.MANAGER_ROLE_CODE ? 'disabled-selected' : ''}}" data-id="{{ite.roleCode}}" data-type="{{ite.roleType}}" data-rolename="{{ite.roleName}}" data-description="{{ite.description}}"></span>
				<span class="check-lb">{{ite.roleName}}</span>
			</div> 
			##})##
		</div>
	</div>
	##})##
	<div class="fm-item" style="border-bottom: 1px solid #eee;color:#999;">{{$t("主角色")}}</div>
	<div class="fm-item" style="display: flex;">
		<label class="fm-lb">{{$t("主角色")}}</label>
		<div class="fm-wrap">
			<div class="main-role-select"></div>
		</div>
	</div>
</div>
##}else{##
<div class="crm-g-form mn-checkbox-box edit-power-box">
	<div class="fm-item">
		<div style="color:#999;">{{$t("说明：")}}{{type === 'copy' ? $t("crm.复制的角色将覆盖原有") : $t("新添加的角色会与员工的原有角色进行合并")}}</div>
		<div class="fm-wrap" style="width: 440px;">
			<div class="widget-employee"></div>
		</div>
	</div>
	<div class="fm-item" style="border-bottom: 1px solid #eee;color:#999;">{{$t("员工角色配置")}}</div>
	##_.each(FiledList,function(item,index){##
	<div class="form-item">
		<label class="fm-lb">{{item.groupName}}</label>
		<div class="fm-wrap">
			##_.each(item.roleInfoList, function(ite, ind){##
			<div class="checkbox-item">
				<span class="mn-checkbox-item {{(type === 'add' || roleList.indexOf(ite.roleCode) === -1) || (type === 'copy' && ite.roleCode === CRM.config.MANAGER_ROLE_CODE) ? '' : 'mn-selected'}} {{type === 'copy' || ite.roleCode === CRM.config.MANAGER_ROLE_CODE ? 'disabled-selected' : ''}}" data-id="{{ite.roleCode}}" data-type="{{ite.roleType}}" data-rolename="{{ite.roleName}}" data-description="{{ite.description}}" ></span>
				<span class="check-lb" title="{{ite.roleName}}">{{ite.roleName}}</span>
			</div>
			##})##
		</div>
	</div>
	##})##
	<div class="fm-item" style="border-bottom: 1px solid #eee;color:#999;">{{$t("主角色")}}</div>
	<div class="fm-item" style="display: flex;">
		<label class="fm-lb">{{$t("主角色")}}</label>
		<div class="fm-wrap">
			<div class="main-role-select"></div>
			##if(type !== 'copy'){##
				<div class="main-role-checkbox">
					<span class="mn-checkbox-item j-checkbox ##if(type==='add'){##disabled-selected##}##"></span>
					<span class="check-lb">{{$t("已有主角色的员工和部门不更新主角色")}}</span>
				</div>
			##}##
		</div>
	</div>
</div>
##}##
