define(function (require, exports, module){
    var util = require('crm-modules/common/util');
    var tmpUtil = require('../util');
	var Table = require('crm-widget/table/table');
	var Dialog = require("crm-widget/dialog/dialog");
	var EditPowerDialog = require('./editpowerdialog');
    var MainRoleSet = require('../mainroleset/mainroleset');

	module.exports = Backbone.View.extend({
		initialize: function(options) {
			this.roleList = options.data; 
            this.roleCode = options.roleCode;
            this.roleName = options.roleName;
			this.render();
		},

		events: {
            'click .j-add': 'onAdd',                        
            'click .j-del': 'onDel',                 
            'click .j-set': 'onSet',                 
            'click .j-export': 'onExport',                      
        },
 
        render: function() {
        	var me = this;
            var isCrmMgr = me.roleCode === CRM.config.MANAGER_ROLE_CODE;
            var functionCodes = CRM.control.functionCodes;
            var btns = [];
            if (_.contains(functionCodes, 'permission_business_role_manage')) {
                btns.push({text:$t("添加员工"),className: 'j-add'})
            }
            if (_.contains(functionCodes, 'permission_business_role_export')) {
                btns.push({text:$t("导出"),className: 'j-export'})
            }

            me.dt = new Table({
                $el: me.$el,
                url: '/EM1HPAASUdobj/userApi/roleUsers',
                requestType:   'FHHApi',
                showFilerBtn: false,
                showMultiple:  true,
                trHandle:      false,
                operate: {
                    pos: 'T',
                    btns: btns
                },
                search: {
                    pos: 'T',
                    'placeHolder': $t("搜索员工"), 
                    'type':       'username', 
                    'fileName':   'username'
                },
                batchBtns: [{
                    text: $t("删除"),
                    className: 'j-del'
                },{
                    text: $t("当前角色设为主角色"),
                    className: 'j-set'
                }],
                postData: {
                    roleCode: me.roleCode
                },
                columns: [{
                    data: 'name',
                    title: $t("姓名"),
                },{
                    data: 'mainDepartment',
                    title: $t("crm.主属部门")
                },{
                    data: 'departmentInfoList',
                    title: $t("附属部门")
                },{
                    data: 'post',
                    title: $t("职位"),
					width: 120
                },{
                    data: '',
                    title: $t("操作"),
					width: 180,
                    render: function(){
                        return me.roleCode === CRM.config.MANAGER_ROLE_CODE ? 
                            '<div class="table-ops"><span style="color:#ccc;margin-right:5px;">'+ $t("删除")+'</span><a class="j-set">'+ $t("当前角色设为主角色")+' </a></div>' :
                            '<div class="table-ops"><a class="j-del">'+ $t("删除") +'</a><a class="j-set">'+ $t("当前角色设为主角色")+'</a></div>';
                    }
                }],
                formatData: function(data) {
                    return {
                        totalCount: (data.page && data.page.totalCount) || 0,
						data: me.getformatData(data.map, data.userInfo)
                    }
                },
                initComplete: function($el){ 
                    $('.first-target-item', $el).before([
                        '<div class="item dt-sc-box batch-c">',
                            '<span class="item-tit batch-c">'+ $t("范围：") +'</span>',
                            '<div class="item-con selector j-selector"></div>',
                        '</div>'].join(''));
                    tmpUtil.initSelector(me.$('.j-selector'), function(data, selector) {
                        me.selector = selector;
                        me.dt.setParam({deptId: data.join(',')}, true, true);
                    });

                    // crm管理员列表隐藏添加员工按钮 
                    isCrmMgr ? me.$('.j-add').hide() : me.$('.j-add').show();
                }
            });
            me.dt.on('trclick', function(data,$tr,$target) { 
                if($target.hasClass('j-del')){
                    me.onDel(data);
                }else if ($target.hasClass('j-set')) {
                    me.onSet(data);
                };
            });
        },

        /*
         *   格式化数据
         *   格式化成表格的数据结构
        */
		getformatData: function (map, userInfo) {
            var tableData = [];
			var me = this;
			_.each(map, function (item, employeeId) {
				let info = userInfo[employeeId];
                tableData.push({
					employeeId: employeeId,
					name: info.name,
                    mainDepartment: tmpUtil.getMainDepartment(info),
                    post: tmpUtil.getPostByEmployeeId(info),
					departmentInfoList: me.getDeparts(info)
                });
            });
            return tableData;
        },
		
		getDeparts: function(info) {
			var list = info.departmentInfoList;
			var main = info.mainDepartment;
			var names = []
			_.each(list, function(item) {
				if (main && item.departmentId != main.departmentId) {
					names.push(item.departmentName);
				}
			})
			return names.length > 0 ? names.join('、') : '--';
		},
		
        onAdd: function(e){
            var me = this;
            me.addPowerWidget && (me.addPowerWidget.destroy(),me.addPowerWidget = null);
            me.addPowerWidget = new EditPowerDialog({
                title: $t("添加员工"),
                type: 'add2',
                FiledList: me.roleList
            });
            me.addPowerWidget.on('success', function(){
                me.trigger('update_quota');
                me.dt.setParam({}, true);
            });
            me.addPowerWidget.show({
                data: {
                    roleList:[{roleCode: me.roleCode}]
                }
            });
        },

        onExport: function(e) {
            var me = this;
            var title = $t("全部CRM用户");
            util.FHHApi({
                url: '/EM1HPAASUdobj/bulkimport/exportUser',
                data: {
                    roleCode: me.roleCode
                },
                success: function(res){
                    if( res.Result.StatusCode == 0 ){
                        var value = res.Value;
                        var url = CRM.util.getFscLink(value.path + '.' + value.ext, value.file_name + '.' + value.ext, true);
                        tmpUtil.showExportDialog(url, title);
                    }else {
                        tmpUtil.showExportDialog('', title);
                    }
                }
            }, {
                submitSelector: me.$('.j-export')
            })
        },

        onDel: function(data){
            var me = this;
            var selectArr = me.dt.getCheckedData();

            if (me.roleCode === CRM.config.MANAGER_ROLE_CODE) {
                util.remind(3, $t("不能删除管理员角色"));
                return false;
            };
            var confirm = util.confirm($t("确定从当前角色中删除员工"),$t("提示"),function(){
                var req = [];
                _.each(selectArr, function (item, index){
                    req.push( item.employeeId );
                });

                var userIds = data.employeeId ? [data.employeeId] : req;

                me.mainRoleSetWidget && me.mainRoleSetWidget.destroy();
                me.mainRoleSetWidget = new MainRoleSet({
                    roleCode: me.roleCode,
                    userIds: userIds,
                    rolename: me.roleName
                });
                me.mainRoleSetWidget.show();

                me.mainRoleSetWidget.on('success',function(){
                    util.FHHApi({
                        url: '/EM1HPAASUdobj/roleApi/roleRemoveUsers',
                        data: {
                            roleCode: me.roleCode,
                            users : userIds
                        },
                        success: function(res){
                            if( res.Result.StatusCode == 0 ){
                                util.remind(1, $t("操作成功"));
                                confirm.destroy();
                                me.trigger('update_quota');
                                me.dt.setParam({}, true);
                            }
                        }
                    }, {
                        submitSelector: confirm.$('.b-g-btn')
                    })
                });  
            });
        },

        onSet: function(data) {
            var me = this;
            var selectArr = me.dt.getCheckedData();

            var confirm = util.confirm($t("确定将当前角色设为主角色")+'?', $t("当前角色设为主角色"), function(){
                var req = [];
                _.each(selectArr, function (item, index){
                    req.push( item.employeeId );
                });
                util.FHHApi({
                    url: '/EM1HPAASUdobj/userApi/updateUsersMajorRole',
                    data: {
                        majorRoleCode: me.roleCode,
                        userIds : data.employeeId ? [data.employeeId] : req
                    },
                    success: function(res){
                        if( res.Result.StatusCode == 0 ){
                            util.remind(1, $t("操作成功"));
                            confirm.destroy();
                            me.dt.setParam({}, true);
                        }
                    }
                }, {
                    submitSelector: confirm.$('.b-g-btn')
                })
            });
        },

        destroy: function(){
            this.$el.off();
            _.each(['addPowerWidget', 'dt', 'selector', 'mainRoleSetWidget'], function(item){
                this[item] && (this[item].destroy(), this[item] = null);
            }, this);
        }
	})
})
