/*
 *  功能权限管理
 *  <AUTHOR>
 */

define(function (require, exports, module) {
	var indexTpl = require('./template/index-html');
	var RoleAuthorityManage = require('manage-modules/vues/businessauthority/index').default;
	var leftNav = require('manage-modules/leftnav/leftnav');

	var RoleManage = Backbone.View.extend({
		initialize: function (opts) {
			this.widgets = {};
			this.setElement(opts.wrapper);
			this.el.innerHTML = indexTpl();
		},

		render: function (param) {
			const me = this;
			leftNav.getValidateStatusDeferred().done(function () {
				var app = new RoleAuthorityManage({
					el: me.$('.crm-rolemanage-con'),
					title: leftNav.getMenuTitles(location.hash.replace('#', ''))
				});
				me.widgets.app = app;
				app.render();
			})
		},
		destroy: function () {
			this.widgets.app.destroy();
			this.widgets.app = null;
		}
	});
	module.exports = RoleManage;
});