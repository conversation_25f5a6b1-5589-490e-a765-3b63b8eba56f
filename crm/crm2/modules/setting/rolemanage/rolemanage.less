.crm-s-rolemanage {
	.crm-w-tabs {
		.navbar a{
			display: inline-block;
   			height: 100%;
			color: #999;
			&:hover,
			&.active {
				text-decoration: none;  
				color: #333;
			}
		}
	}
	
	.import-role-set {
		color: #3487E2;
		cursor: pointer;
		position: absolute;
		right: 20px;
	}
	.disabled-selected {
		cursor: not-allowed;
	}
	.crm-ico-play{
		margin-left: 12px;
		vertical-align: 2px;
	}
	.crm-loading {
		height: 100%;
	}
	&.crm-c-dialog {
		background-color: var(--color-neutrals01);
	}
    .batch-c:last-child .term-tit {
    	display: none;
    }
    .btn-to-add-employee {
    	height: 26px;
    	line-height: 26px;
    }
    .examine-permissions {
        .crm-warn-bar {
            position: relative;
            margin-top: -1px;
            z-index: 10;
        }
    }

	.leftnav{
		width: 179px;
		border-right: 1px solid #eee;
		overflow-y: auto;
		display: flex;
		flex-direction: column;
		.item-group{
			display: flex;
		    align-items: center;
		    justify-content: flex-start;
			font-size: 14px;
			color: #999;
			padding: 20px 0 5px 10px;
			cursor: pointer;
			&.current .ico-arrow{
				background-position: -20px -20px;
			}
			.ico-arrow {
				width: 20px;
				height: 20px;
				.img-retina("@{imgUrl}/<EMAIL>","@{imgUrl}/<EMAIL>",40px,40px);
				background-repeat: no-repeat;
				background-position: 0 -20px;
				background-size: 40px 40px;    
			}
		}
		.item_box{
			display: none;
		}
		.item {
			position: relative;
			overflow: hidden;
			padding: 8px 0;
			padding-left: 30px;
			padding-right: 20px;
			cursor: pointer;
			text-overflow: ellipsis;
			white-space: nowrap;
			&.cur{
				background-color: #f2f2f2;
				cursor: auto;
			}
			&:hover{
				background-color: #f2f2f2;
				transition: background-color .35s;
			}
		}
		.quota, 
		.total {
			color: #aaa;
		}
		.line {
			width: 140px;
		    height: 1px;
		    background: #eee;
		    margin-left: 20px;
    	}
    	.crm-rolelist{
    		margin: 12px 0;
    		.item {
    			display: flex;
    			justify-content: space-between;
    			padding-left: 20px;
    		}
    	}
		.customize-rolelist {
			padding-bottom: 25px;
			.item {
				display: flex;
				padding-right: 0;
				&.cur{
					cursor: pointer;
				};
				&:hover {
					.ops {
						display: block;
					}
				}
			}
			.name {
				flex:1;
				overflow: hidden;
				width: 100px;
				padding-right: 10px;
				text-overflow: ellipsis;
			}
			.ops {
				display: none;
			}
		}
		.add-btn {
			margin-right: 20px;
			font-size: 12px;
			cursor: pointer;
			color: #3487e2;
		}
		.item-name {
		    flex: 1;
		    width: 70px;
		    overflow: hidden;
		    margin-right: 10px;
		    text-overflow: ellipsis;
		}
		.crm-ico-edite,
		.crm-ico-del {
			display: inline-block;
			width: 14px;
			height: 14px;
    		margin-right: 10px;
		}
		.crm-ico-del {
    		margin-right: 20px;
		}
		// left搜索
		.search-wrap .b-g-ipt{
			width: 135px;
			margin-left: 0;
		}
		.search-rolelist-box{
			flex: 1;
			overflow: hidden;
			.search-rolelist.customize-rolelist {
				padding-bottom: 0;
			}
			.no-data {
				width: 100%;
				height: 100%;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
	}
	.right-wrapper{
		position: relative;
		overflow-y: hidden;
		flex: 1;
		.crm-w-table .dt-term-batch .batch-item {
			transform: translateY(6px);
		}
	}
	.refresh-btn {
        position: absolute;
	    top: 50%;
	    left: 50%;
	}

	.permissions-set,
	.role-asign,
	.examine-permissions {
		display: flex;
		flex-direction: row;
		position: absolute;
		left: 0;
		top: 50px;
		right: 0;
		bottom: 0;
	}
	.permissions-set {
		.leftnav,
		.right-wrapper {
			//margin-top: 48px;
		}
		.checkbox-btn{
			position: absolute;
		    left: 1px;
		    right: 0;
		    bottom: 0;
		    padding: 0px 20px;
		    height: 60px;
		    line-height: 60px;
		    overflow: hidden;
		    background: var(--color-neutrals01);
		    box-shadow: inset 0 1px 0 0 #eee;
		    .reset-btn {
		    	display: inline-block;
			    height: 32px;
			    line-height: 32px;
			    padding: 0 15px;
			    border: 0 !important;
			    background-color: var(--color-neutrals01)!important;
			    color: #3487e2;
			    text-align: center;
			    text-decoration: none;
			    cursor: pointer;
			    font-family: inherit;
			    vertical-align: middle;
		    }
		}
	}

	// table
	.selector {
		width: 200px;
	    border: 0;
	    padding: 0;
	}
	.table-ops {
		a {
			cursor: pointer;
			margin-right: 5px;
		}
		.disable {
			cursor: inherit;
			color: #eee;
		}
	}
	.rolemag-ico-box{
		text-align: left;
		.rolemag-ico-isrole{
			width: 16px;
			height: 16px;
			display: inline-block;
			.img-retina("@{imgUrl}/ico-trade.png", "@{imgUrl}/ico-trade_2x.png", 80px, 16px);
			background-position: -16px 0px;
            margin-left: 20px;
		}
	}

	.radio-item {
		display: inline-block;
		width: 100px;
		&:last-child{
			width: 80px;
		}
	}
	.mn-radio-item {
		margin-right: 8px;
	}

	// 编辑员工弹窗
	.edit-power-box{
		.form-item {
			display: flex;
			margin-bottom: 10px;
		}
		.fm-lb {
			width: 82px;
		}
		.fm-wrap{
			float: left;
			width: 440px;
			position: relative;
			.disabled-widget{
				width: 308px;
				border: 1px solid #ddd;
				border-radius: 2px;
				line-height: 34px;
				background-color: #f7f7f7;
				height: 32px;
				color: #ccc;
				.disabled-add{
					font-size: 18px;
					padding-left: 5px; 
				}
			}
		}
		.checkbox-item {
			display: inline-block;
			width: 180px;
			padding-right: 3px;
		    overflow: hidden;
		    white-space: nowrap;
		    text-overflow: ellipsis;
		}
		.mn-checkbox-item {
			margin-right: 10px;
			margin-top: -1px;
		}
		.role-name{
			margin-left: 10px;
			color: #333;
		}
		.main-role-select {
			margin-bottom: 15px;
		}
		.main-role-checkbox {
			display: flex;
		    align-items: center;
		    height: 17px;
		}
	}

	.select-box {
		.select-list {
			display: flex;
		    align-items: center;
		    margin: 15px 0;
		}
		.select {
			width: 240px;
		}
		.f-g-options-wrapper {
			width: 240px;
		}
	}
}
