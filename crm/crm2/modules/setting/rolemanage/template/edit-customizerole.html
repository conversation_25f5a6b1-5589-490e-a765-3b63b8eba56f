<div class="crm-g-form crm-s-customizerole">
	<div class="fm-item">
		<label class="fm-lb">
			<em>*</em><span>{{$t("角色名称")}}</span>
		</label>
		<input class="b-g-ipt fm-ipt rolename-ipt" maxlength="20" ##if(datas){##value="{{datas.rolename}}"##}else{##placeholder="{{$t("最多20个中文字符")}}"##}##>
	</div>

	<div class="fm-item">
		<label class="fm-lb">
			<em>*</em><span>{{$t("角色描述")}}</span>
		</label>
		<textarea maxlength="1000" class="b-g-ipt fm-ipt roledes-ipt">##if(datas){##{{datas.description}}##}##</textarea>
	</div>
	##if(type === 'add'){##
	<div class="fm-item mn-checkbox-box">
		<span class="mn-checkbox-item j-toggle"></span><span class="check-lb">{{$t("复制")}}</span><span class='j-select-box'></span>{{$t("的角色权限")}}</div>
	##}##
</div>