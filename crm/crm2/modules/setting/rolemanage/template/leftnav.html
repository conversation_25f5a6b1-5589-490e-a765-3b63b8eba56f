<div class="search-wrap">
	<input class="b-g-ipt j-s-field" placeholder="{{$t('搜索角色名')}}"/>
</div>
##if(roles && roles.length > 0){##
	<div class="rolelist-box">
	##if(!isPermissionsSet){##
		<ul class="crm-rolelist">
			<li class="item j-all ##if(roleCode === 'all'){## cur##}##">
				<div class="name">{{$t("全部CRM用户")}}</div>
				<span class="quota"></span>
			</li>
			<li class="item j-forbidden ##if(roleCode === 'forbidden'){## cur##}##">
				<div class="name">{{$t("禁用CRM用户")}}</div>
				<span class="total"></span>
			</li>
		</ul>
		<div class="line"></div>
	##}##
	##_.each(roles, function(item){## 
	<ul class="{{item.groupType === 1 ? 'customize-rolelist' : 'default-rolelist'}}">
		<div class="item-group checkbox-head-toggle">
			<span class="ico-arrow"></span>
			{{item.groupName}}##if(item.groupType === 1){##
				<span class="add-btn j-add">+{{$t("新建")}}</span>
			##}##
		</div>
		<div class="item_box">
			##_.each(item.roleInfoList, function(ite, idx){##
				<li class="item ##if(ite.roleCode === roleCode){## cur##}##" data-grouptype="{{item.groupType}}" data-id="{{ite.roleCode}}"  data-type="{{ite.roleType}}" data-rolename="{{ite.roleName}}" data-description="{{ite.description}}">			
					##if(item.groupType === 1 && isPermissionsSet){##
					<div class="name" title="{{ite.roleName}}">{{ite.roleName}}</div> 
					<div class="ops">
						<span class="crm-ico-edite" action-type="reject"></span><span class="crm-ico-del" action-type="reject"></span>
					</div>
					##}else{##
					<div class="name" title="{{ite.roleName}}">{{ite.roleName}}</div> 
					##}##
				</li>
			##})##
		</div>
	</ul>
	##})##
	</div>
##}##

##if(searchRoleList){##
	<div class="search-rolelist-box">
	##if(searchRoleList.length > 0){##
		##_.each(searchRoleList, function(ite, idx){##
			<ul class="search-rolelist {{ite.grouptype === 1 ? 'customize-rolelist' : 'default-rolelist'}}">
				<div>
					<li class="item ##if(ite.id === roleCode){## cur##}##" data-id="{{ite.id}}" data-type="{{ite.type}}"
						data-rolename="{{ite.rolename}}" data-description="{{ite.description}}">
						##if(ite.grouptype === 1 && isPermissionsSet){##
							<div class="name" title="{{ite.rolename}}">{{ite.rolename}}</div> 
							<div class="ops">
								<span class="crm-ico-edite" action-type="reject"></span><span class="crm-ico-del" action-type="reject"></span>
							</div>
							##}else{##
							<div class="name" title="{{ite.rolename}}">{{ite.rolename}}</div> 
						##}##
					</li>
				</div>
			</ul>
		##})##
	##}else{##
		<div class="no-data">{{$t("暂无数据")}}</div>
	##}##
	</div>
##}##