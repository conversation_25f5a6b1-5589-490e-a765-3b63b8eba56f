define(function (require, exports, module){
    var util = require('crm-modules/common/util');
    var Selector = require('crm-widget/selector/selector');
    var Dialog = require("crm-widget/dialog/dialog");
    var manageUtils = require('manage-modules/manage-utils/manage-utils');

    return {
        /**
         * 员工角色分配（查看）permission_business_role_query
         * 角色权限设置       permission_bussiness_function_role_setting
         * 审批权限设置       permission_bussiness_function_approve_setting
         * 添加员工（分配）   permission_business_role_manage
         * 导出              permission_business_role_export
         * 基础数据权限       data_permission_base_data
         * 数据共享           data_permission_data_share
         * 相关团队数据权限   data_permission_related_team
         * 临时权限           data_permission_temporary
         * 其他              data_permission_others
         */
        getFunctionCodes: function() {
            return new Promise(function(resolve, reject) {
                manageUtils.FHHApi({
                    url: '/EM2HORG/Management/Permission/GetFunctionCodesByEmployee',
                    data: {
                        appId: 'facishare-system'
                    },
                    success: function(res){
                        if( res.Result.StatusCode == 0 ){
                            CRM.control.functionCodes = _.map(res.Value.functionCodeVos, function(item) {
                                return item.functionCode;
                            });
                            resolve(CRM.control.functionCodes);
                        } else {
                            util.alert(res.Result.FailureMessage);
                        }
                    }
                }, {
                    errorAlertModel: 1
                })
            })
        },
        
        // functionCode
        // 查看 permission_business_role_query
        // 添加员工 permission_business_role_manage
        getEmployeesAndCircles: function(functionCode) { 
            return new Promise(function(resolve, reject) {
                manageUtils.FHHApi({
                    url: '/EM2HORG/Management/Permission/GetOptionByFunctionCodeAndAppId',
                    data: {
                        appId: 'facishare-system',
                        functionCode: functionCode
                    },
                    success: function(res){
                        if( res.Result.StatusCode == 0 ){
                            // var empIds = _.map(res.Value.employeeOptions, function(item) {
                            //     return item.id;
                            // });
                            // var circleIds = _.map(res.Value.departmentOptions, function(item) {
                            //     return item.id;
                            // });
                            var contacts = FS.contacts;
                            // var employees = contacts.getEmployeesByIds(empIds);
							// var circles = contacts.getCirclesByIds(circleIds);
							
							var employees = res.Value.employeeOptions;
							var circles = res.Value.departmentOptions;

                            resolve({
                                employees: contacts.sortEmployeesByLetter(employees), 
                                circles: contacts.buildTreeOfCircles(circles)
                            });
                        } else {
                            util.alert(res.Result.FailureMessage);
                        }
                    }
                }, {
                    errorAlertModel: 1
                })
            })
        },
        
        // 初始化表格查找范围
        initSelector: function($wrap, callback) {
			// this.getEmployeesAndCircles('permission_business_role_query').then(function(res) {
                var selector = new Selector({
                    $wrap: $wrap,
                    zIndex: 1000,
					group: true,
					// tabs: [{
					// id: 'group',
					// type: 'tree',
					// title: '树状结构',
					// data: res.circles
					// }],
                    single: true,
                    size:   1,
                    label: $t("crm.选择部门")
                });
                selector.on('addItem', function() {
                    util.hideErrmsg(this.$el.closest('.v-box'));
                })
                selector.on('change',function (data) {
                    var _selectData = data.group || [];
                    var circleIds = [];
                    _.each(_selectData, function (item, index){
                        circleIds.push( item * 1 );
                    })

                    callback(circleIds, selector);
                });
			// })
        },

        findIndex: function(array, predicate, context){
              for (var i = 0; i < array.length; i++) {
                  if (predicate.call(context, array[i], i, array)) return i;
              }
              return -1;
        },

        // 获取职位
		getPostByEmployeeId: function (info) {
			if (!info.post) return '--';
			return info.post;
        },

        // 获取主部门
		getMainDepartment: function (info) {
			if (!info.mainDepartment) return '--';
			if (!info.mainDepartment.departmentName) return '--';
			return info.mainDepartment.departmentName;
        },

        showExportDialog: function(url, title) {
            var tpl = url ? ['<div class="con-box" style="height: 150px;">',
                                '<div class="con">',
                                '</div>',
                                '<div class="single-down">',
                                    '<div class="inner">',
                                        '<p><em class="crm-ico-importsuc"></em><em>'+$t('生成成功(不打印外部联系人数据)')+'</em></p>',
                                        '<a href=' + url + ' target="_blank">'+ $t("点击下载 Excel 表") +'</a>',
                                    '</div>',
                                '</div>',
                            '</div>'] : ['<div class="con-box">',
                                '<div class="con">',
                                '</div>',
                                '<div class="fail">',
                                    '<div class="inner">',
                                        '<p><em class="crm-ico-importerror"></em><em class="j-error-text">'+ $t("生成失败") +'</em></p>',
                                    '</div>',
                                '</div>',        
                            '</div>'];
                            
            var _confirm = new Dialog({
                classPrefix: 'crm-c-dialog crm-c-dialog-confirm crm-c-importout',
                title: $t("导出") + title,
                showScroll: false,
                showBtns: false,
                content: tpl.join(''),
                zIndex: 2000
            });
            _confirm.on('hide', function(evt) {
                _confirm.destroy();
            });
            _confirm.show();
            return _confirm;
        },
    }
})
