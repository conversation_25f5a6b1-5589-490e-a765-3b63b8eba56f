<div class="rolemanage-dialog-info">{{$t("crm.设置字段权限提示")}}</div>
<div class="search-wrap"><span>{{$t('搜索')}}:</span><input class="b-g-ipt j-s-field" placeholder="{{$t('搜索字段名')}}"/></div>
<dl class="rolemanage-dialog-head crm-g-form">
   <dt class="fm-lb"><em></em>{{$t("全选")}}</dt>
   <dd class="mn-radio-box radio-box-head">
        <span class="radio-item">
            <span data-permissiontype="2" class="mn-radio-item {{disabled ? 'disabled-selected' : ''}}"></span><span class="radio-lb">{{$t("读写")}}</span>
        </span>
        <span class="radio-item">
            <span data-permissiontype="1" class="mn-radio-item {{disabled ? 'disabled-selected' : ''}}"></span><span class="radio-lb">{{$t("只读")}}</span>
        </span>
        <span class="radio-item">
            <span data-permissiontype="0" class="mn-radio-item {{disabled ? 'disabled-selected' : ''}}"></span><span class="radio-lb">{{$t("不可见")}}</span>
        </span>
   </dd>
</dl>
<div class="rolemanage-dialog-body crm-g-form">
	##_.each(fieldInfoList,function(item,index){##
	##var fieldName = item.fieldName;##
	<dl>
	   <dt class="fm-lb" title="{{item.fieldCaption}}"><em>##if(item.isRequire){##*##}##</em><div class="filed-caption">{{{{-item.fieldCaption}}}}</div></dt>
	   <dd class="mn-radio-box radio-box-body ##if(['product_amount', 'order_amount', 'discount'].indexOf(fieldName) > -1){##saleitem##}## ##if(['ProductPrice',  'Discount', 'Price'].indexOf(fieldName) > -1){##orderitem##}##" data-fieldname={{fieldName}}>
	        <span class="radio-item">	
	        	<span class="mn-radio-item ##if(item.status === 2){##mn-selected##}## ##if(!item.isEditable || !item.isEditableOfWrite){##disabled-selected##}##" data-permissiontype="2"></span><span class="radio-lb">{{$t("读写")}}</span>
	        </span>
	        <span class="radio-item">
	        	<span class="mn-radio-item ##if(item.status === 1){##mn-selected##}## ##if(!item.isEditable || !item.isEditableOfReadOnly || (item.isRequire && readonly)){##disabled-selected##}##" data-permissiontype="1"></span><span class="radio-lb">{{$t("只读")}}</span>
	        </span>
	        <span class="radio-item">
	        	<span class="mn-radio-item ##if(item.status === 0){##mn-selected##}## ##if(!item.isEditable || !item.isEditableOfInvisible || (item.isRequire && readonly)){##disabled-selected##}##" data-permissiontype="0"></span><span class="radio-lb">{{$t("不可见")}}</span>
	        </span>
	   </dd>
	</dl>
	##})##
</div>

