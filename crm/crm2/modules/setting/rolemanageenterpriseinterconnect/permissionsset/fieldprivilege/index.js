/**
 * 设置字段权限
 * params
 *   roleCode: 角色code
 *   fieldInfoList: 字段权限列表
 *   disabled: 是否可设置
 *   readonly: 必填字段是否编辑
 * events
 *   submit(data)  提交数据事件
 */

define(function (require, exports, module){
	var util = require('crm-modules/common/util');
	var Dialog = require("crm-widget/dialog/dialog");
    var SELECTED_CLASSNAME = 'mn-selected';
    var DISABLED_SELECTED_CLASSNAME = 'disabled-selected';
    var SELECTED_CLASS = '.' + SELECTED_CLASSNAME;

	var FieldPrivilege = Dialog.extend({
		attrs: {
            title: $t("字段权限"),
            content: '<div class="crm-loading "></div>',
            showScroll: true,
            showBtns:  true,
            datas: null,
            className: 'crm-s-rolemanage'
        },
        events: {
            'click .radio-box-body .mn-radio-item': 'onCheck',
            'click .radio-box-head .mn-radio-item': 'onCheckAll',
            'click .b-g-btn': 'onSubmit',
            'click .b-g-btn-cancel': 'destroy',
			'input .j-s-field':      'onInput',
			'keypress .j-s-field':   'onKeyPress'
        },
		
		/**
         * @desc 替换字符串中的非法值
         */
        replaceRegExp: function(str) {
            str = str + '';
            return str.replace(/\(/g, '\\(')
                .replace(/\-/g, '\\-')
                .replace(/\)/g, '\\)')
                .replace(/\[/g, '\\[')
                .replace(/\]/g, '\\]')
                .replace(/\{/g, '\\{')
                .replace(/\}/g, '\\}')
                .replace(/\+/g, '\\+')
                .replace(/\./g, '\\.');
        },

        /**
         * @desc 搜搜高亮处理数据
         */
        highligh: function(data, key) {
            if(!key) return data;
            var me = this,
                keyWord = _.escape(key),
                reg = new RegExp(me.replaceRegExp(keyWord), 'gi');
            if (keyWord) {
                data = data + '';
                return data.replace(reg, function(item) {
                    return '<span class="dt-mark">' + item + '</span>';
                });
            }
            return data;
        }, 
		
		onInput: function() {
			var me = this;
			if (me.searchTimer) {clearTimeout(me.searchTimer); me.searchTimer = null;}
			me.searchTimer = setTimeout(function() {
				var key = me.$('.j-s-field').val();
				me.$('.filed-caption').each(function(index, item) {
					var text = $(item).text();
					$(item).html(me.highligh(text, key));
				})
			}, 100)
		},
		
		onKeyPress: function(e) {
			var me = this;
			if (e.which == 13) {
				if (me.$('.dt-mark').length > 0 ) {
					var top = me.$('.dt-mark').offset().top - me.$('.dialog-scroll').offset().top;
					console.log(top);
					me.$('.dialog-scroll-el').scrollTop(top - 30)
				}
			}
		},
		
        render: function(){
            return FieldPrivilege.superclass.render.call(this);
        },
        show: function(data){
        	return FieldPrivilege.superclass.show.call(this);
        },
        
        onCheck: function(e) {
            var $target = $(e.currentTarget);
            var $allSelecteditems = this.$('.radio-box-head .mn-radio-item');
            var permissiontypeIndex;
            var allSelected;
            var $tmpItem;

            if($target.hasClass(DISABLED_SELECTED_CLASSNAME)) {
                e.stopPropagation();
                return false;
            }

            // 已选中项不可编辑 直接返回
            if ($target.closest('.radio-box-body').find(SELECTED_CLASS).hasClass(DISABLED_SELECTED_CLASSNAME)) {
                util.remind(3, $t("crm.已选中项不可编辑"));
                e.stopPropagation();
                return;
            };

            $target.closest('.radio-box-body').find('.mn-radio-item').each(function (index, item){
                $(item).removeClass(SELECTED_CLASSNAME);
            });

            $target.toggleClass(SELECTED_CLASSNAME);

            permissiontypeIndex = $target.data('permissiontype');

            //【产品合计】【整单折扣】【销售订单金额】3个字段的权限联动，如果某字段存在不可编辑项，该字段不参与联动
            if ($target.closest('.radio-box-body').hasClass('saleitem')) {
                this.$('.saleitem').each(function(index, item) {
                    $tmpItem = $(item).find('.mn-radio-item');
                    if (!$tmpItem.hasClass(DISABLED_SELECTED_CLASSNAME)) {
                        $(item).find('.mn-radio-item').removeClass(SELECTED_CLASSNAME);
                        $tmpItem.eq(2 - permissiontypeIndex).addClass(SELECTED_CLASSNAME);
                    }
                });
            }

            //【单价】【折扣】【销售单价】3个字段的权限联动，如果某字段存在不可编辑项，该字段不参与联动
            if ($target.closest('.radio-box-body').hasClass('orderitem')) {
                this.$('.orderitem').each(function(index, item) {
                    $tmpItem = $(item).find('.mn-radio-item');
                    if (!$tmpItem.hasClass(DISABLED_SELECTED_CLASSNAME)) {
                        $(item).find('.mn-radio-item').removeClass(SELECTED_CLASSNAME);
                        $tmpItem.eq(2 - permissiontypeIndex).addClass(SELECTED_CLASSNAME);
                    }
                });
            }
               
            // 全选 
            allSelected = _.every(this.$('.radio-box-body'), function(item, index) {
                return $(item).find(SELECTED_CLASS).data('permissiontype') === permissiontypeIndex;
            });
            $allSelecteditems.removeClass(SELECTED_CLASSNAME);
            allSelected && $allSelecteditems.eq(2 - permissiontypeIndex).addClass(SELECTED_CLASSNAME);

            return false;
        },

        onCheckAll: function(e) {
            var $target = $(e.currentTarget);
            var permissiontypeIndex = $target.data('permissiontype');
            var $radioItem;

            if( $target.hasClass(DISABLED_SELECTED_CLASSNAME) ) {
                e.stopPropagation();
                return false;
            }
            _.each(this.$('.radio-box-body'), function(item, index){
                $radioItem = $(item).find('.mn-radio-item');

                // 可编辑字段才进行编辑，不可见按钮可能是不可编辑，故用读写标签（第一个标签）去判断
                if ( !$radioItem.eq(2 - permissiontypeIndex).hasClass(DISABLED_SELECTED_CLASSNAME) ) {
                    $radioItem.removeClass(SELECTED_CLASSNAME);
                    $radioItem.eq(2 - permissiontypeIndex).addClass(SELECTED_CLASSNAME);
                }
            });
        },

        onSubmit: function(){
            var fieldPermission = {};

            _.each(this.$('.radio-box-body'), function(item, index){
                fieldPermission[$(item).data('fieldname')] = $(item).find(SELECTED_CLASS).data('permissiontype');
            });
            
            this.trigger('submit', fieldPermission);
        },
        destroy: function(){
            return FieldPrivilege.superclass.destroy.call(this);
        }
	});

	module.exports = FieldPrivilege;
})