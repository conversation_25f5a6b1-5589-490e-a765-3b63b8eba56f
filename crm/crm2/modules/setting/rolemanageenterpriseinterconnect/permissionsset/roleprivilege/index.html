<!--
 * @Descripttion: 
 * @Author: LiAng
 * @Date: 2020-05-14 23:41:23
 * @LastEditors: LiAng
 * @LastEditTime: 2020-05-14 23:49:21
--> 
##_.each(list, function(item, index){##
<div class="lazyload-item" ##if(_.contains(['AccountObj', 'AccountAddrObj'], item.descApiName)){## data-functionnumber="{{item.descApiName}}" ##}##
	data-descapiname="{{item.descApiName}}" data-descapidisplayname="{{item.descApiDisplayName}}">
	<!-- <script type="text/lazyload"> -->
		<h3 class="checkbox-head checkbox-head-toggle current">
			<span class="ico-arrow"></span>
			<span class='desc-api-display-name'>{{item.descApiDisplayName}}</span>
			## var titleDesc = '';
			if(item.descApiName === 'BIPreConfigedReport'){
			titleDesc = $t('系统预置报表全员可查看，导出功能走此处配置，数据权限走预置报表的主业务模块的数据权限.');
			}else if(item.descApiName === 'BIBlankTemplate'){
			titleDesc = $t('基于自选主题建立的图表的功能权限走此处配置，数据权限走主业务模块的数据权限.');
			}else if(item.descApiName === 'BIProduct'){
				titleDesc = $t('基于产品分析主题建立的图表的功能权限走此处配置，数据权限按主业务模块产品的数据权限进行控制。产品模块通常为公开只读，请谨慎设置.');
			}else if(['BICustomer','BIOrder','BIOpportunity','BISalesClue','BIContractAchievement','BIEmployeeAchievement','BICheckinsAnalyse','BIGoalAnalysis'].indexOf(item.descApiName) != -1){
			titleDesc = $t('基于{{descApiDisplayName}}主题建立的图表的功能权限走此处配置，数据权限按主业务模块{{descApiDisplayName}}的数据权限进行控制.', {
			descApiDisplayName: item.descApiDisplayName
			});
			}; ##
			##if(titleDesc){##
			<span class="crm-ui-title bi-object-title-desc" data-title="{{titleDesc}}" data-pos="top"></span>
			##}##

			##var roleFunctionInfos = item.roleFunctionInfos;
			var allEnabled = _.every(roleFunctionInfos, function(ite) {
				return ite.enabled;
			});
			var anyEnabled = _.some(roleFunctionInfos, function(ite) {
				return ite.enabled;
			});
			var isNoEditable = _.every(roleFunctionInfos, function(ite) {
				return ite.isEditable === false;
			});##
			##if(item.isHaveFieldPrivilege){##
				##if(item.descApiName === 'SalesOrderObj'){##
					<span class="j-set set-btn 
						##if(!anyEnabled){## disabled-btn##}##" 
						data-objectid="SalesOrderObj" 
						data-name="{{$t('crm.销售订单')}}" 
						data-functionnumber={{item.viewListFuncCode}}>{{$t("crm.设置销售订单字段权限")}}</span>
					<span class="j-set set-btn 
						##if(!anyEnabled){## disabled-btn##}##" 
						data-objectid="SalesOrderProductObj" 
						data-name="{{$t('crm.订单产品')}}" 
						data-functionnumber={{item.viewListFuncCode}}>{{$t("crm.设置订单产品字段权限")}}</span>
				##}else if(item.descApiName === 'ReturnedGoodsInvoiceObj'){##
					<span class="j-set set-btn 
						##if(!anyEnabled){## disabled-btn##}##" 
						data-objectid="ReturnedGoodsInvoiceObj" 
						data-name="{{$t('crm.退货单')}}" 
						data-functionnumber={{item.viewListFuncCode}}>{{$t("crm.设置退货单字段权限")}}</span>
					<span class="j-set set-btn 
						##if(!anyEnabled){## disabled-btn##}##" 
						data-objectid="ReturnedGoodsInvoiceProductObj" 
						data-name="{{$t('crm.退货单产品')}}" 
						data-functionnumber={{item.viewListFuncCode}}>{{$t("crm.设置退货单产品字段权限")}}</span>
				##}else if(item.descApiName === 'Inventory'){##
				##}else{##
					<span class="j-set set-btn 
						##if(!anyEnabled){## disabled-btn##}##"
						data-objectid="{{item.descApiName}}" 
						data-name="{{item.descApiDisplayName}}" 
						data-functionnumber={{item.viewListFuncCode}}>{{$t("设置字段权限")}}</span>
				##}##
			##}else{##
				<span class="j-set set-btn" data-functionnumber={{item.viewListFuncCode}}></span>
			##}##
		</h3>
		<ul class="mn-checkbox-box b-g-clear {{item.descApiName}}">
			##if(item.descApiName !== 'DuplicateCheckObj'){##
			<li class="checkbox-item">
				<span class="mn-checkbox-item j-check-all ##if(!item.isEditable || isNoEditable){##disabled-selected##}## ##if(allEnabled){##mn-selected##}##"></span>{{$t("全选")}}</li>
			##}##
			##_.each(roleFunctionInfos, function(ite, ind){##
			<li class="checkbox-item">
				<span class="mn-checkbox-item 
					##if(ite.isFiledReadOnlyRequired){## readonly-item##}## 
					##if(!ite.isEditable){## disabled-selected##}## 
					##if(ite.enabled){## mn-selected##}## 
					##if(item.viewListFuncCode === ite.functionNumber){## look-item##}## 
					##if(item.isBIObject){## bi-item##}## 
					##if(ite.functionNumber === 'OpportunityObj||BeforeSaleAction'){## edit-presale##}## 
					##if(ite.functionNumber === 'OpportunityObj||AfterSaleAction'){## edit-aftersale##}## 
					##if(ite.functionNumber === 'OpportunityObj||ViewAfterSaleAction'){## view-presale##}## 
					##if(ite.functionNumber === 'OpportunityObj||ViewBeforeSaleAction'){## view-aftersale##}## 
					##if(ite.functionNumber === 'ContactObj||ImportFromAddressBook'){## import-contacts##}## 
					##if(ite.functionNumber === 'PriceBookObj||Abolish'){## pricebook-abolish##}## 
					##if(ite.functionNumber === 'PriceBookObj||Delete'){## pricebook-delete##}## 
					##if(ite.isIntelligentForm){## smartform-item##}## 
					##if(ite.isClone){## clone-item##}## 
					##if(ite.isAdd){## add-item##}##" 
					data-functionnumber={{ite.functionNumber}}></span>
					{{ite.displayName}}
			</li>
			##})##
		</ul>
	<!-- </script> -->
</div>
##})##
