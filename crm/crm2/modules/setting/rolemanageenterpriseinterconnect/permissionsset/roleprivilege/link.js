define(function (require, exports, module) {
	var SELECTED_CLASS = 'mn-selected';

	function Link(linkData) {
		this.global = _.extend({}, linkData.allPositiveLinkage, this.reverseLinkData(linkData.allReverseLinkage));
		this.data = _.extend({}, linkData.specialPositive, this.reverseLinkData(linkData.specialReverse));
	}

	Link.prototype.reverseLinkData = function (list) {
		var data = {};
		_.each(list, function (item, key) {
			item = _.map(item, function (citem) { return '!'+ citem });
			key = '!'+ key;
			data[key] = item;
		});
		return data;
	}

	Link.prototype.getObjectName = function ($target) {
		var $itembox = $target.closest('.mn-checkbox-box');
		return $itembox.prev().find('.j-set').data('functionnumber')
	}

	Link.prototype.unCheckedAll = function ($el, $target) {
		var $itembox = $target.closest('.mn-checkbox-box');
		// var $items = $('.mn-checkbox-item', $itembox).not('.disabled-selected');
		var $items = $itembox.find('.mn-checkbox-item');
		$items.removeClass(SELECTED_CLASS);

		var name = this.getObjectName($target);
		var value = this.data['!' + name];

		if (value) {
			for (let i = 0; i < value.length; i++) {
				let item = value[i];
				if (/^\!/.test(item)) {
					item = item.replace('!', '')
				}
				var $item = $el.find('span[data-functionnumber="' + item + '"].mn-checkbox-item');
				this.unCheckedAll($el, $item);
			}
		}
	}

	Link.prototype.update = function ($el, $items) {
		for (var i = 0; i < $items.length; i++) {
			var $target = $items.eq(i);
			var fn = $target.data('functionnumber');
			var isChecked = $target.hasClass(SELECTED_CLASS);

			var chart = (isChecked ? '' : '!')
			// 有规则配置的才会联动, 没有的不处理
			if (this.data[chart + fn]) {
				this.active($el, this.data[chart + fn]);
			}
		}
	}

	Link.prototype.active = function ($el, value) {
		for (var i = 0; i < value.length; i++) {
			var item = value[i];
			var chart = /^\!/.test(item)
			if (chart) {
				item = item.replace('!', '')
			}
			var $dom = $el.find('span[data-functionnumber="' + item + '"]');
			// if ($dom.hasClass('disabled-selected')) {
			// return;
			// }

			!chart ? $dom.addClass(SELECTED_CLASS) : $dom.removeClass(SELECTED_CLASS);

			var $itembox = $dom.closest('.mn-checkbox-box')
			/** 更新全选按钮的状态 */
			this.updateAllStatus($itembox);
			/** 更新设置字段权限按钮的状态 */
			this.updateSetDisabledStatus($itembox);
			/** 更新查看列表按钮的状态 */
			this.updateLookStatus($itembox)

			this.update($el, $dom);
		}
	}

	Link.prototype.init = function (data) {
		for (var i = 0; i < data.length; i++) {
			// var apiname = data[i].descApiName;
			var apiname = data[i].roleFunctionInfos[0].functionNumber.replace(/\*||/, '');
			for (var k in this.global) {
				var key = this.replace(apiname, k);
				if (this.data[key]) {
					this.data[key] = this.data[key].concat(this.replace(apiname, this.global[k]))
				} else {
					this.data[key] = this.replace(apiname, this.global[k])
				}
			}
		}
		console.log(this.data);
	}

	Link.prototype.replace = function (apiname, value) {
		if (_.isArray(value)) {
			return value.map(function (item) {
				return item.replace('{obj}', apiname);
			})
		}
		if (_.isString(value)) {
			return value.replace('{obj}', apiname);
		}
	}

	Link.prototype.updateAllStatus = function ($itembox) {
		var children = $itembox.find('span:not(.j-check-all)')
		var allLength = children.filter('span:not(.disabled-selected)').length;
		var checkedLength = children.filter('span:not(.disabled-selected)').filter('.mn-selected').length;
		if (checkedLength == allLength) {
			$itembox.find('.j-check-all').addClass(SELECTED_CLASS)
		} else {
			$itembox.find('.j-check-all').removeClass(SELECTED_CLASS)
		}
	}

	Link.prototype.updateSetDisabledStatus = function ($itembox) {
		var $btn = $itembox.prev().find('.j-set');
		var isHasCheckedClass = _.some($('.mn-checkbox-item', $itembox), function (item) {
			return $(item).hasClass(SELECTED_CLASS);
		})
		isHasCheckedClass ? $btn.removeClass('disabled-btn') : $btn.addClass('disabled-btn')
	}

	Link.prototype.updateLookStatus = function ($itembox) {
		var $lookItem = $itembox.find('.look-item');
		// 判断查看列表后面的选项有没有选中的
		if ($itembox.find('span:not(.look-item)').filter('.mn-selected').length) {
			$lookItem.addClass(SELECTED_CLASS);
		}
	}

	module.exports = Link;
})
