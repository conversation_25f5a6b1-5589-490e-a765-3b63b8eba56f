/*
 *  功能权限管理
 *  <AUTHOR>
 */

define(function (require, exports, module){
    var FieldPrivilege = require('./permissionsset/fieldprivilege/index');
    var FieldPrivilegeTpl = require('./permissionsset/fieldprivilege/index-html');
    var RolePrivilege = require('./permissionsset/roleprivilege/index');
	var RoleManage = {};

    RoleManage.FieldPrivilege = FieldPrivilege;
    RoleManage.RolePrivilege = RolePrivilege;
    RoleManage.FieldPrivilegeTpl = FieldPrivilegeTpl;

    module.exports = RoleManage;
})
