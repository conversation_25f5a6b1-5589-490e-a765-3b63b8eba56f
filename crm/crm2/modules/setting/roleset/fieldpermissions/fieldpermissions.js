/**
 * 员工角色分配----设置字段权限
 */

define(function (require, exports, module){
    var mainHtml = require('../template/edit-fieldpermissions-html');
    var Api = require('paas-object-modules/api');

    var FieldPermissions = Backbone.View.extend({
        events: {
            'change .radio-box-body input': 'radioHandle',
            'change .radio-box-head input': 'allSelectHandle'
        },
        initialize() {
            this.render();
        },
        render: function(){
            var datas = this.options.datas;
            this.$el.html(mainHtml({
                fieldInfoList: datas.fieldInfoList, 
                objectId: datas.objectId, 
                disabled: datas.disabled,
                readonly: datas.readonly
            }));
        },
        
        radioHandle: function(e) {
            var me = this;
            var $target = $(e.currentTarget);
            var $items = $('.radio-box-body', me.$el);
            var $allSelecteditems = $('.radio-box-head input', me.$el);
            var permissiontypeIndex = $target.data('permissiontype');
            var allSelected;
            /**
             * 销售订单 产品合计，销售订单金额，整单折扣，字段权限联动
             *  */
            var fieldLabel = $target.parents('.radio-box-body').data("fieldname"); 
            if (this.options.datas.descApiName == "SalesOrderObj" && _.contains(['product_amount', 'order_amount', 'discount'], fieldLabel)) {
                _.each($items, function(item/*, index*/){
                    var fieldname = $(item).data('fieldname'), $radioItem = $(item).find('input');
                    // 可编辑字段才进行编辑，不可见按钮可能是不可编辑，故用读写标签（第一个标签）去判断
                    if (_.contains(['product_amount', 'order_amount', 'discount'], fieldname) && !$radioItem.eq(2 - permissiontypeIndex).attr('disabled')) {
                        $radioItem.eq(2 - permissiontypeIndex).attr({checked: true});
                    }
                });
            }
            allSelected = _.every($items, function(item/*, index*/) {
                return $(item).find('input:checked').data('permissiontype') === permissiontypeIndex;
            });
            $allSelecteditems.removeAttr('checked');

            allSelected && $allSelecteditems.eq(2 - permissiontypeIndex).attr({checked: false});
                
            return false;
        },

        allSelectHandle: function(e) {
            var me = this;
            var $target = $(e.currentTarget);
            var permissiontypeIndex = $target.data('permissiontype');
            var $items = $('.radio-box-body', me.$el);
            var $radioItem;

            _.each($items, function(item/*, index*/){
                $radioItem = $(item).find('input');

                // 可编辑字段才进行编辑，不可见按钮可能是不可编辑，故用读写标签（第一个标签）去判断
                if ( !$radioItem.eq(2 - permissiontypeIndex).attr('disabled') ) {
                    $radioItem.eq(2 - permissiontypeIndex).attr({checked: true});
                }
            });
        },
        submit: function(callback){
            var me = this;
            var $items = $('.radio-box-body', me.$el);
            var fieldPermission = {};

            _.each($items, function(item/*, index*/){
                //delete
                fieldPermission[$(item).data('fieldname')] = $(item).find('input:checked').data('permissiontype');
            });

            Api.updateRoleObjectFieldPrivilege({
                descApiName: me.options.datas.descApiName,
                roleCode: me.options.datas.roleCode,
                fieldPermission: fieldPermission,
            }, (tag, res) => {
                if( tag === 'success' ){
                    FS.util.remind(1, $t("操作成功"));
                    callback();
                } else {
                    FS.util.alert(res.FailureMessage);
                    callback();
                }
            });
        },
        destroy: function(){
            this.remove();
        }
    });
    module.exports = FieldPermissions;
})