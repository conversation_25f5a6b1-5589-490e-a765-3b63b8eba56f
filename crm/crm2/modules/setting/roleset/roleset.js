/**
 * @file   自定义对象角色设置
 * <AUTHOR>
 */
define(function (require, exports, module) {
    var tpl = require('./template/tpl-html');
    var RoleAuthorityManage = require('manage-modules/vues/objectauthority/index').default;

    var Layout = Backbone.View.extend({

        options: {
            className: 'paaso-role-setting',  //  全屏布局
            tagName: 'div',
            $wrap: null,                     // 最外层
            data: null                      // 传入对象数据
        },

        events: {},

        initialize: function () { },

        render: function () {
            this.$el.html(tpl());
            var me = this;
            // require.async(['crm-modules/setting/rolemanage/rolemanage',], function (Module) {
            //     require.async(['crm-modules/setting/rolemanage/permissionsset/index',], function (Permissionsset) {
            //         me.permissionsset = new Permissionsset({
            //             el: me.$('.permissions-set'),
            //             data: me.model.getDescribeProperty('api_name')
            //         })
            //     })
            // })

            me.permissionsset = new RoleAuthorityManage({
                el: me.$('.permissions-set'),
                api_name: me.model.getDescribeProperty('api_name')
            })
            me.permissionsset.render();
        },

        destroy: function () {
            var me = this;
            me.undelegateEvents();
            me.$el.remove();

            if (me.permissionsset) {
                me.permissionsset.destroy()
                me.permissionsset = null
            }
        }
    });

    module.exports = Layout;
});
