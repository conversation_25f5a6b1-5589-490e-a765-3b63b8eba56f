<div class="rolemanage-dialog-info">
  {{$t("说明如果角色对当前对象设置了新建或导入权限")}}</div>
<dl class="rolemanage-dialog-head">
   <dt class="fm-lb"><em></em>{{$t("全选")}}</dt>
   <dd class="radio-box-head paaso-radio">
        <label class="paaso-radio">
            <input type="radio" name="paas-role-head" data-permissiontype="2" class="" ##if(disabled){## disabled ##}##>
						<i></i>
            <span class="radio-lb">{{$t("读写")}}</span>
        </label>
        <label class="paaso-radio">
            <input type="radio" name="paas-role-head" data-permissiontype="1" class="" ##if(disabled){## disabled ##}##>
						<i></i>
            <span class="radio-lb">{{$t("只读")}}</span>
        </label>
        <label class="paaso-radio">
            <input type="radio" name="paas-role-head" data-permissiontype="0" class="" ##if(disabled){## disabled ##}##>
						<i></i>
            <span class="radio-lb">{{$t("不可见")}}</span>
        </label>
   </dd>
</dl>
<div class="rolemanage-dialog-body">
	##_.each(fieldInfoList,function(item,index){##
	##var fieldName = item.fieldName;##
	<dl>
	   <dt class="fm-lb" title="{{item.fieldCaption}}"><em>##if(item.isRequire){##*##}##</em>{{{{-item.fieldCaption}}}}</dt>
	   <dd class="radio-box-body ##if(['TotalMoney', 'Discount', 'TradeMoney'].indexOf(fieldName) > -1){##saleitem##}## ##if(['ProductPrice',  'Discount', 'Price'].indexOf(fieldName) > -1){##orderitem##}##" data-fieldname={{fieldName}}>
	        <label class="paaso-radio">
	        	<input type="radio" name="paas-role-item-{{index}}" ##if(item.status === 2){## checked="checked" ##}## ##if(!item.isEditable || !item.isEditableOfWrite){## disabled ##}## data-permissiontype="2">
	        	<i></i>
	        	<span class="radio-lb">{{$t("读写")}}</span>
	        </label>
	        <label class="paaso-radio">
	        	<input type="radio" name="paas-role-item-{{index}}" ##if(item.status === 1){## checked="checked" ##}## ##if(!item.isEditable || !item.isEditableOfReadOnly || (item.isRequire && readonly)){## disabled ##}## data-permissiontype="1"></span>
						<i></i>
	        	<span class="radio-lb">{{$t("只读")}}</span>
	        </label>
	        <label class="paaso-radio">
	        	<input type="radio" name="paas-role-item-{{index}}" ##if(item.status === 0){## checked="checked" ##}## ##if(!item.isEditable || !item.isEditableOfInvisible || (item.isRequire && readonly)){## disabled ##}## data-permissiontype="0">
						<i></i>
	        	</span><span class="radio-lb">{{$t("不可见")}}</span>
	        </label>
	   </dd>
	</dl>
	##})##
</div>

