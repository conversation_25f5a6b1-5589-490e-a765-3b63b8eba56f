<div class="checkbox-scroll crm-scroll">
	<div style="max-width: 1030px;">
		<div class="info-words">
			<h3>{{rolename}}</h3>
			<p>{{description || '--'}}</p>
		</div>
		##_.each(list, function(item,index){##
		<h3 class="checkbox-head">
			{{descApiDisplayName}}
			##if(item.isHaveFieldPrivilege){##
				##if(item.descApiDisplayName === 'SalesOrderObj'){##
					<span class="btn-to-set-field ##if(!item.isHaveViewListPermiss){##disabled-btn##}##"
						data-objectid="SalesOrderObj"
						data-name="{{$t('crm.销售订单')}}"
						data-functionnumber={{item.viewListFuncCode}}>{{$t("设置销售订单字段权限")}}</span>
					<span class="btn-to-set-field ##if(!item.isHaveViewListPermiss){##disabled-btn##}##"
						data-objectid="SalesOrderProductObj"
						data-name="{{$t('crm.订单产品')}}"
						data-functionnumber={{item.viewListFuncCode}}>{{$t("设置订单产品字段权限")}}</span>
				##}else if(item.descApiDisplayName === 'ReturnedGoodsInvoiceObj'){##
					<span class="btn-to-set-field ##if(!item.isHaveViewListPermiss){##disabled-btn##}##"
						data-objectid="ReturnedGoodsInvoiceObj"
						data-name="{{$t('crm.退货单')}}"
						data-functionnumber={{item.viewListFuncCode}}>{{$t("设置退货单字段权限")}}</span>
					<span class="btn-to-set-field ##if(!item.isHaveViewListPermiss){##disabled-btn##}##"
						data-objectid="ReturnedGoodsInvoiceProductObj"
						data-name="{{$t('crm.退货单产品')}}" data-functionnumber={{item.viewListFuncCode}}>{{$t("设置退货单产品字段权限")}}</span>
				##}else if(item.descApiDisplayName === 'Inventory'){##
				##}else{##
					<span class="btn-to-set-field ##if(!item.isHaveViewListPermiss){##disabled-btn##}##"
						data-objectid="{{item.descApiDisplayName}}"
						data-name="{{descApiDisplayName}}"
						data-functionnumber={{item.viewListFuncCode}}>{{$t("设置字段权限")}}</span>
				##}##
			##}else{##
				<span class="btn-to-set-field" data-functionnumber={{item.viewListFuncCode}}></span>
			##}##
		</h3>
		<dl class="mn-checkbox-box checkbox-group b-g-clear">
			<dd class="checkbox-list">
				##var enabled = _.every(item.roleFunctionInfos, function(item) {
					return item.enabled;
				});
				var isNoEditable = _.every(item.roleFunctionInfos, function(item) {
					return item.isEditable === false;
				});##
				<label class="paaso-checkbox">
					<input type="checkbox" class="j-check-all" ##if(!item.isEditable || isNoEditable){## disabled ##}## ##if(enabled){## checked ##}##>
					<i></i>
					<span class="check-lb">{{$t("全选")}}</span>
				</label>
				##_.each(item.roleFunctionInfos, function(ite, ind){##
				<label class="paaso-checkbox">
					<input type="checkbox" class="mn-checkbox-item
						##if(ite.isFiledReadOnlyRequired){## readonly-item##}##
						##if(ite.functionNumber === 'PriceBookObj||Abolish'){## pricebook-abolish##}##
						##if(ite.functionNumber === 'PriceBookObj||Delete'){## pricebook-delete##}##
						##if(item.viewListFuncCode===ite.functionNumber){## look-item##}##
						##if(ite.isClone || ite.functionNumber.indexOf('||Clone')!=-1){## clone-item##}## 
						##if(ite.isAdd){## add-item##}## 
						##if(item.isBIObject){## bi-item##}##"
						##if(ite.enabled){## checked ##}##
						##if(!ite.isEditable){## disabled ##}##
						data-defaultstatus={{ite.defaultStatus}}
						data-functionnumber={{ite.functionNumber}} >
					<i></i>
					<span class="check-lb">{{ite.displayName}}</span>
				</label>
				##})##
			</dd>
		</dl>
		##})##
	</div>
</div>
<div class="checkbox-btn">
	<div class="b-g-btn btn-to-save b-g-btn-disabled">{{$t("保 存")}}</div>
</div>
