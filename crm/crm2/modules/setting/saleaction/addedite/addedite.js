/**
 * @desc 销售流程设置
 * author qigb
 */
define(function(require, exports, module) {

    var util = require('crm-modules/common/util'),
        ScrollBar = require('base-modules/ui/scrollbar/scrollbar'),
        Selector = require('crm-widget/selector/selector'),
        Select  = require('crm-widget/select/select'),
		Stage   = require('./stage/stage'),
        tpl     = require('./template/tpl-html'),
		stepTpl = require('./template/step-html'),
		navTpl  = require('./template/nav-html'),
        migrationUtils = require('../utils/migrationdatatransfer'),
        saveConfirmTpl  = require('./template/saveconfirm-html');

    var AddEdite = Backbone.View.extend({

        options: {
            className: 'b-g-crm crm-s-saleaction',
            tagName:   'div'
        },

        /**
         * @type {{String}}
         * 标记状态
         */
        status: 'edit',

        initialize: function() {
            var me = this;
            $('body').append(me.$el);
			me._bindEvents();
			me.delStages = [];
            me._postid = util.getTraceId();  // migration 迁移接口所需
            me._getDescribeLayoutMigration() // migration 迁移获取对象描述
            me.requestId = util.getUUIdAsMiniProgram(); // migration 迁移接口所需
			me.data = {
                isNotYunZhiJia: CRM.control.isNotYunZhiJia,
				SaleActionID:       '',     // 动作ID
				Name:               '',     // 名称
				Description:        '',     // 流程描述
				CircleIDs: [],              // 应用部门
				IsAllowOldData:     true,   // 是否更新数据
				IsAllowTradeIfWin:  false,  // 必须新建订单方可赢单
				CheckFieldIfWin:  false,    // 赢单时校验之前所有阶段必填项
                IsAllowTrade:       false,  // 是否允许完成第几阶段方可新建订单
                AllowTradeStageOrder: 0,    // 完成第几阶段方可新建订单
				Stages:    [],              // 售前售后阶段
				PreStages: [],              // 售前所有阶段
                EndStages: [],              // 默认阶段结束状态的阶段
				NextStages: [],             // 售后所有阶段
				changeNum: 0,               // 修改数量，判断编辑时有误修改
			}

			me.stageData = {
				SaleActionStageID: '',     // 流程id
				Description: '',           // 阶段要求
				Name: '',                  // 流程名称
                StageFlag: 1,              // 状态 1.进行中 2.赢单；4.输单
				CustomerFieldInfos: [],    // 关联客户字段
                OppoFieldInfos: [],        // 关联商机字段
				UDFieldInfos: [],          // 自定义字段
				AsIsThatDayRemind:  true,  // 售后阶段停留控制
                AsThatRemainDays:   10,    // 售后阶段停留天数
				AsIsTimeoutRemind:  false, // 售后阶段 是否控制超时
				AsRemainDays: 15,          // 售后阶段 超时天数

                IsLeaderConfirm:    false,  // 售前阶段确认
                IsFinishByContacts: false,  // 售前阶段联系人
				ContactCount: 1,            // 售前阶段联系人
                IsTimeoutRemind: true,      // 售前阶段是否开启停留天数
				RemainDays: 10,             // 售前阶段停留时超过天数
                Weight: '',                 // 赢率
				changeNum:  0,              // 编辑更新的数量
				isNew: true,                // 是否为新建阶段
				isValid: false              // 
			};

            // 默认售前阶段
            me.preStageData = [
                $.extend(true, {}, me.stageData,  {Type: 1, Name: $t("验证客户"), Weight: 10}),
                $.extend(true, {}, me.stageData,  {Type: 1, Name: $t("需求确定"),  Weight: 30}),
                $.extend(true, {}, me.stageData,  {Type: 1, Name: $t("方案")+'/'+$t("报价"), Weight: 60}),
                $.extend(true, {}, me.stageData,  {Type: 1, Name: $t("谈判审核"),  Weight: 80})
            ];

            me.endStageData = [             // 默认结束状态
                $.extend(true, {}, me.stageData,  {Type: 1, StageFlag: 2, Name: $t("赢单"), Weight: 100}),
                $.extend(true, {}, me.stageData,  {Type: 1, StageFlag: 4, Name: $t("输单"), Weight: 0}),
                $.extend(true, {}, me.stageData,  {Type: 1, StageFlag: 3, Name: $t("无效"), Weight: 0})
            ];
        },

		// 显示对外接口
		show: function(data) {
			var me = this;
			me.status = data ? 'edit' : 'add';
            me._setData(data);
            me._defaultName = me.data.Name; // 默认的值
            me.data.status = me.status;
			me.$el.html(tpl(me.data));
			me._renderStep();
			me._renderNav();
			me._renderCircle();
            me._renderCompleteSelect();
            me._scrollbar = new ScrollBar(me.$('.scroll-el'));
		},

		/**
		 * @desc 设置默认数据
		 * 区分售前 售后阶段
		 */
		_setData: function(data) {
			var me = this;
			if (me.status == 'edit') {
				me.data = _.extend({
					PreStages: [],
					NextStages: [],
                    EndStages: [],
					changeNum: 0,
                    IsAllowTrade: false,
                    AllowTradeStageOrder: 0,
                    isNotYunZhiJia: CRM.control.isNotYunZhiJia
				}, data || {}, {
					IsAllowOldData: true
				});
			} else {
                me.data.Stages = me.data.Stages.concat(me.preStageData);
                me.data.Stages = me.data.Stages.concat(me.endStageData);
			}
			_.each(me.data.Stages, function(item) {
                item.Weight = item.Weight;
				item.changeNum = 0;
                item.StageFlag = item.StageFlag || 1;
                //格式化数据
                item.CustomerFieldInfos=me._parseRelateData(item.CustomerDescribe,item.CustomerIsRequiredDescribe);
                item.OppoFieldInfos=me._parseRelateData(item.OpportunityDescribe,item.OpportunityIsRequiredDescribe)
                // 
				if (item.Type == 1) {
                    if (!item.StageFlag || item.StageFlag == 1) {
                        me.data.PreStages.push(item);
                    } else {
                        me.data.EndStages.push(item);
                    }
				} else {
					me.data.NextStages.push(item);
				}
			});

			// 兼容老数据
			if (me.data.PreStages.length == 0) {
				me.data.PreStages.push($.extend(true, {Type: 1, StageFlag: 1}, me.stageData));
			}
            // 老数据不存在默认终结状态
            if (me.data.EndStages.length == 0) {
                me.data.EndStages = me.data.EndStages.concat(me.endStageData);
            }
		},

		/**
		 * @desc 渲染基础信息中阶段设置
		 */
		_renderStep: function() {
			var me = this,
				$pre = me.$('.pre-step'),
				$next = me.$('.next-step'),
                $end  = me.$('.end-step');
			me._renderStepByParam(me.data.PreStages,     $pre,  1);
			me._renderStepByParam(me.data.NextStages,    $next, 2);
            me._renderStepByParam(me.data.EndStages,     $end,  3);
		},

		// 根据参数渲染阶段
		// num 删除按钮隐藏条件
		_renderStepByParam: function(data, $box, type) {
			var me = this, html = '';
            if (type == 3) {
                console.log(data)
            }
            if (data.length > 0) {
                _.each(data, function(item) {
                    html += me._createStep(item);
                });
            } else {
                $box.closest('.step-box').find('h4').hide();
                html = '<span class="add-step-btn">' + $t("添加售后阶段") + '</span>';
            }
			$box.html(html);
			me._setStepNum($box, type);
			me._toggleBtn($box, type);
            if (type == 3) {  // 渲染状态
                $('.step-status-select', $box).each(function(index, item) {
                    me._initEndStatus($(item), data[index]);
                });
            }
		},

        /**
         * @desc 初始化终结状态的select选择
         */
        _initEndStatus: function($el, data) {
            var me = this;
            var select = new Select({
                $wrap: $el,
                options: [{ name: $t("赢单"), value: 2},{
                            name: $t("输单"), value: 4},{
                            name: $t("无效"), value: 3}],
                defaultValue: data.StageFlag,
                disabled: data.SaleActionStageID != '',
                zIndex: 2000
            });
            select.on('change', function(v, data, select) {
                var $el   = select.get('$wrap'),
                    val   = $el.attr('data-val'),
                    $item = $el.closest('.b-item'),
                    $step = me.$('.step-status-select[data-val='+ val +']');
                util.hideErrmsg($step);
                $step.removeClass('error');
                $el.attr('data-val', data.value);
                $('.step-may', $item).val(data.value == 2 ? 100 : 0);
				me._changeData($item.index(), 3, {
					StageFlag: data.value
				});
            });
            $el[0].select = select;
        },

		/**
		 * @desc 渲染阶段导航
		 */
		_renderNav: function() {
			var me = this,
				$pre = me.$('.pre-nav'),
				$next = me.$('.next-nav');
			me._renderNavByParam(me.data.PreStages,  $pre);
			me._renderNavByParam(me.data.NextStages, $next);
		},

		// 根据参数渲染导航
		_renderNavByParam: function(data, $box) {
			var me = this, html = '';
			_.each(data, function(item) {
				html += me._createNav(item);
			});
			$box.html(html);
			me._setStepNum($box);
			me._toggleNav($box);
		},

		/**
		 * @desc   创建一个阶段
		 * @return 返回阶段字符串
		 */
		_createStep: function(data) {
			return stepTpl(data);
		},

		/**
		 * @desc   导航上面创建一个阶段
		 * @return 返回阶段字符串
		 */
		_createNav: function(data) {
			return navTpl(data);
		},

		/**
		 * @desc 设置序号
		 */
		_setStepNum: function(context, type) {
			var me = this;
			context = context || me.$el;
			$('.step-num', context).each(function(index, item) {
				$(item).html(index + 1);
			});
		},

		/**
		 * @desc 隐藏显示添加删除按钮
		 */
		_toggleBtn: function (context, type) {
			var $dels = $('.del-step', context),
				$adds = $('.add-step', context),
                maxNum = type == 3 ? 3 : 20,
				num = type == 1 || type == 3 ? 1 : 0;
			$dels.toggle($dels.length > num);
            $adds.hide();
            if ($adds.length < maxNum) {
                $adds.eq($adds.length - 1).show();
            }
		},

		/**
		 * @desc 显示隐藏左侧导航
		 */
		_toggleNav: function(context) {
			var $hides = $('.no-name', context),
				$all   = $('.n-item', context);
			context.toggle($hides.length < $all.length);
		},

		/**
		 * @desc 渲染应用部门
		 * 获取默认数据
		 */
		_renderCircle: function() {
			var me = this,
				circleIds  = me.data.CircleIDs;

			me._circle = new Selector({
				$wrap: me.$('.circle-select'),
				group: true,
				zIndex: 1300,
				label: $t("crm.添加部门"),
				defaultSelectedItems: {
                    group: circleIds
                }
			});
			me._circle.on('change', function() {
				var circleIds = me._circle.getValue('group') || [];
				me.data.CircleIDs = circleIds;
				me.data.changeNum += 1;
			});
		},


        /**
         * @desc 完成第几阶段方可新建订单
         */
        _renderCompleteSelect: function() {
            var me = this;
            me._select = new Select({
                $wrap: me.$('.j-complete'),
                options: [],
                zIndex: 2000
            });
            me._select.on('change', function(v, item) {
                if (item) {
                    me.data.AllowTradeStageOrder = item.value;
                    me._allowTradeId = item.id;
                    me.data.changeNum += 1;
                }
            });
            if (me.status == 'edit' && me.data.IsAllowTrade) { // 编辑状态允许设置 记录阶段id
                me._allowTradeId = me.data.PreStages[(me.data.AllowTradeStageOrder - 1) || 0].SaleActionStageID;
            }
            me._resetComplateSelect();
        },

        /**
         * @desc 重置完成阶段新建成交
         */
        _resetComplateSelect: function() {
            var me = this, options = [], curOpt = null;
            _.each(me.data.PreStages, function(item, index) {
                var order = index + 1;
                options.push({
                    name: $t('第{{order}}阶段', {order: order}),
                    value: order,
                    id: item.SaleActionStageID || ''
                });
            });
            me._select.resetOptions(options);
            if (me._allowTradeId) {
                curOpt = _.findWhere(options, {id: me._allowTradeId});
            } else {
                curOpt = _.findWhere(options, {value: me.data.AllowTradeStageOrder});
            }
            me.data.AllowTradeStageOrder = curOpt ? curOpt.value : 1;
            me._select.setValue(curOpt ? curOpt.value : 1, true);
        },

        /**
		 * @desc 删除阶段时检查销售阶段是否正在使用
		 */
         _checkSaleActionMigration: function(id, callBack) {
            var me = this;
            if (!id || id == '') {
                callBack && callBack();
                return;
            }
            util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/sale_action/service/checkSaleActionStageIsApply',
                data: {
                    saleActionStageIds: [id]
                },
                success: function(data) {
                    if (data.Result.StatusCode == 0) {
                        if (!data.Value.IsApply) {
                            callBack && callBack();
                        } else {
                            util.alert($t("有商机正在使用该阶段无法删除"));
                        }
                        return;
                    }
                    util.alert(data.Result.FailureMessage);
                }
            }, {
                errorAlertModel: 1
            });
        },

        

        /**
         * @desc 验证 基础信息页面的信息
         * @return 是否通过验证
         */
        _validBase: function() {
            var me = this, num = 0,
                $baseName = me.$('.j-base-name');
            if ($.trim($baseName.val()) == '') {
                num += 1;
                util.showErrmsg($baseName, $t("请填写销售流程名称"));
            }
            me.$('.step-box-pre .step-name').each(function(index, item) {
                var $item = $(item), msg = '', $bItem = $item.closest('.b-item'),
                    statusVal = $bItem.find('.step-status-select').attr('data-val'),
                    statusText = [$t("赢单"),$t("无效"), $t("输单")][statusVal - 2],
                    $stepStatus = me.$('.step-status-select[data-val="'+ statusVal +'"]'),
                    mayVal = $.trim($bItem.find('.step-may').val());
                if ($.trim($item.val()) == '') {
                    msg += $t("请填写名称");
                }
                if (mayVal == '') {
                    msg = msg ? msg + $t("与赢率") : $t("请填写赢率");
                }
                if (mayVal < 0 || mayVal > 100) {
                    msg += $t("赢率为0-100之间的整数。");
                }
                if ($stepStatus.length > 1) {
                    $stepStatus.addClass('error');
                    msg = msg ? msg + statusText + $t("状态不能重复") : statusText + $t("状态不能重复");
                }
                if (msg != '') {
                    num += 1;
                    util.showErrmsg($(item), msg);
                }
            });
            me.$('.next-step .step-name').each(function(index, item) {
                var $item = $(item), msg = '';
                if ($.trim($item.val()) == '') {
                    msg += $t("请填写名称");
                }
                if (msg != '') {
                    num += 1;
                    util.showErrmsg($(item), msg);
                }
            });
            return num == 0;
        },

        /**
         * @desc 隐藏所有错误信息
         */
        _hideError: function(e) {
            var $target = $(e.target);
            util.hideErrmsg($target);
        },

        events: {
            'click .j-nav .item':        '_onNav',         // 左侧导航点击
			'click .add-step':           '_onAddStep',     // 添加阶段
			'click .del-step':           '_onDelStep',     // 删除阶段
            'click .add-step-btn':       '_onAddNextStep', // 添加售后阶段
			'click .nav-box .n-item':    '_onStepNav',     // 阶段导航点击
            'click .default-nav .n-item':'_onStopPropagation',
			'click .b-g-btn-cancel':     '_onOut',         // 退出
            'click .add-tit .b-g-btn':   '_saveHandle',    // 保存
            'click .r-item .mn-checkbox-item': '_onCheckRule',             // 改变规则
            'focus .base-item .b-g-ipt': '_hideError',                    // 隐藏所有错误信息
            'click .complete-step-box .mn-checkbox-item': '_onAllowTrade' // 允许完成阶段新建订单
        },

        _onStopPropagation: function(e) {
            return false;
        },

		// 绑定input事件
		_bindEvents: function() {
			var me = this;
			util.fixInputEvent('.step-name',   $.proxy(me._onChangeStepName, me), me.$el);
            util.fixInputEvent('.j-base-name', $.proxy(me._onChangeName, me), me.$el);
            // 限制可能性只能输入0-100数 并更改数据
            util.onlyAllowNum('.step-may', me.$el,3,2,false);
            util.fixInputEvent('.step-may',$.proxy(me._onChangeStepWeight, me) , me.$el);
		},

        /**
         * @desc 点击左侧导航
         */
        _onNav: function(e) {
            var me = this,
                $item = $(e.currentTarget),
                index = $item.index();

            if (me._validBase()) {
                if ($('.n-item', $item).length == 0 && $item.hasClass('next-step-nav')) {
                    util.alert($t("暂未设置售后阶段"));
                    return false;
                }
                if (me._validStage()) {
                    me.$('.n-item').removeClass('active');
                    if ($item.hasClass('step-nav')) {
                        $('.n-item', $item).eq(0).trigger('click');
                    } else {
                        $item.addClass('active').siblings().removeClass('active');
                        me.$('.j-con .item').eq(index).show().siblings().hide();
                    }
                }
            }
        },


		/**
		 * @desc 添加阶段
		 */
		_onAddStep: function(e) {
			var me = this,
                $item =  $(e.target).closest('.b-item'),
				$context = $item.closest('.j-step'),
				type  =  $context.attr('data-type'),
				index =  $item.index(),
                data  =  $.extend(true, {}, me.stageData, {Type: type}),
                $html = null;
            if (type == 3) {data = _.extend(data, {Type: 1, StageFlag: 2});}
            console.log(me.stageData);
            $html = $(me._createStep(data));
			$item.after($html);
            type == 3 && me._initEndStatus($html.find('.step-status-select'), data);
			me._setStepNum($context, type);
			me._toggleBtn($context, type);
			me._addNav(index, type);
			me._addData(index, type);
		},

        /**
         * @desc 添加售后阶段
         */
		_onAddNextStep: function(e) {
            var me = this,
                $box = $(e.target).closest('.j-step'),
                type  =  2;
            $box.html(me._createStep($.extend(true, {Type: type}, me.stageData)));
            me._setStepNum($box);
            me._toggleBtn($box);
            me._addNav(0,  type);
            me._addData(0, type);
            $box.closest('.step-box').find('h4').show();
        },

		/**
		 * @desc 删除阶段
		 */
		_onDelStep: function(e) {
			var me = this, $target = $(e.target),
				$item = $target.closest('.b-item'),
				$context = $item.closest('.j-step'),
				type = $context.attr('data-type'),
				index = $item.index();
			// me._checkSaleAction($item.attr('data-id'), function() {
			me._checkSaleActionMigration($item.attr('data-id'), function() {
                if (type == 3) {
                    var selectEl = $item.find('.step-status-select');
                    selectEl[0].select && selectEl[0].select.destroy();
                }
				$item.remove();
				me._setStepNum($context, type);
				me._toggleBtn($context, type);
				me._delNav(index, type);
				me._delData(index, type);
                if ($('.b-item', $context).length == 0 ) {
                     $context.html('<span class="add-step-btn">' + $t('添加售后阶段') + '</span>');
					 $context.closest('.step-box').find('h4').hide();
                }
				me.data.changeNum += 1;
			});
		},

		/**
		 * @desc 增加数据
		 */
		_addData: function(index, type) {
			var me = this,
				key = ['PreStages', 'NextStages', 'EndStages'][type - 1],
                data = $.extend(true, {Type: type == 3 ? 1 : type}, me.stageData);
            if (type == 3) {
                data = _.extend(data, {Type: 1, StageFlag: 2});
            }
			me.data[key].splice(index + 1, 0, data);
			me.data.changeNum += 1;
            if (key == 'PreStages') { // 目的只是为了 改变终结态StageOrder
                _.each(me.data.EndStages, function(item) {
                   item.changeNum += 1;
                });
            }
            if (type == 1) { // 重新设置完成阶段新建成交
                me._resetComplateSelect();
            }
		},

		/**
		 * @desc 删除数据
		 */
		_delData: function(index, type) {
			var me = this,
				key = ['PreStages', 'NextStages', 'EndStages'][type - 1],
                data = me.data[key][index];
			if (data.SaleActionStageID) {
				me.delStages.push({
					EditFlag: 3,
					SaleActionStageID: data.SaleActionStageID
				});
			}
			me.data[key].splice(index, 1);

            // 编辑状态下 删除一个其他阶段状态全部变为已修改该  目的只是为了 改变StageOrder
            if (me.status == 'edit') {
                _.each(me.data[key], function(item) {
                   item.changeNum += 1;
                });
                if (key == 'PreStages') {
                    _.each(me.data.EndStages, function(item) {
                       item.changeNum += 1;
                    });
                }
            }

            if (type == 1) { // 重新设置完成阶段新建成交
                me._resetComplateSelect();
            }
		},

		/**
		 * @desc 添加左侧导航
		 */
		_addNav: function(index, type) {
			var selector = ['.pre-nav', '.next-nav', '.default-nav'][type - 1],
				$item = $('.n-item', this.$(selector)).eq(index),
                data = type == 3 ? {Type: 1, StageFlag: 2} : {};
			if ($item.length > 0) {
				$item.after(navTpl($.extend(true, {}, this.stageData, data)));
			} else {
				this.$(selector).html(navTpl($.extend(true, {}, this.stageData, data)));
			}
			this._setStepNum(this.$(selector));
		},

		/**
		 * @desc 删除左侧导航
		 */
		_delNav: function(index, type) {
			var selector = ['.pre-nav', '.next-nav', '.default-nav'][type - 1];
			$('.n-item', this.$(selector)).eq(index).remove();
			this._setStepNum($(selector));
            this._toggleNav($(selector));
		},

		/**
		 * @desc 阶段导航点击
		 */
		_onStepNav: function(e) {
			var $target = $(e.currentTarget),
				$nav     = $target.closest('.nav-box'),
				$item    = $nav.closest('.item'),
				$con     = this.$('.j-con .item').eq($item.index()),
				key      = $nav.hasClass('pre-nav') ? 'PreStages' : 'NextStages';
			if (this._validBase() && this._validStage()) {
                this._stageKey = key;
                this._initStage($con, this.data[key][$target.index()]);
                this.$('.n-item').removeClass('active');
                $target.addClass('active');
                $item.addClass('active').siblings().removeClass('active');
                $con.show().siblings().hide();
            }
            e.stopPropagation();
		},

		/**
		 * @desc 初始化销售阶段
		 */
		_initStage: function($wrap, data) {
			var me = this;
            console.log(data);
            me._stage && me._stage.destroy();
			me._stage = new Stage({
				$el:  $wrap,
				data: data
			});
		},


        /**
         * @desc 销毁阶段 并设置数据
		 * @param flag 为真时 不做销毁
         * 触发点 不好
         */
        _validStage: function(flag) {
            var me = this;
            if (me._stage) {
                if (me._stage.valid()) {
                    me._setStageData();
					if (!flag) {
						me._stage.destroy();
						me._stage = null;
					}
                    return true;
                }
                return false;
			}
            return true;
        },


		/**
		 * @desc 设置阶段数据
		 */
		_setStageData: function() {
            console.log(this.data.Stages[0].UDFieldInfos);
			var me = this,
				$item = me.$('.n-item.active'),
				index = $item.index(),
                stageData = me._stage.getValue();
            if (stageData.changeNum > 0) {
                me.data.changeNum += 1;
            }
			me.data[me._stageKey][index] = stageData;
		},

		/**
		 * @desc 阶段名称变更
		 */
		_onChangeStepName: function(e) {
			var me = this;
			if (me._timer) {
				clearTimeout(me._timer);
				me._timer = null;
			}
			me._timer = setTimeout( function() {
				var $target = $(e.target),
					$item = $target.closest('.b-item'),
					$context = $item.closest('.j-step'),
					type  =  $context.attr('data-type'),
					index = $item.index(),
					name  = $.trim($target.val());

				me._changeNav(index, type, name);
				me._changeData(index, type, {
					Name: name
				});
			}, 80);
		},
        /**
         * @desc 阶段赢率变更
         */
         _onChangeStepWeight: function(e) {
 			var me = this;
 			if (me._timer) {
 				clearTimeout(me._timer);
 				me._timer = null;
 			}
 			me._timer = setTimeout( function() {
 				var $target = $(e.target),
 					$item = $target.closest('.b-item'),
 					$context = $item.closest('.j-step'),
 					type  =  $context.attr('data-type'),
 					index = $item.index(),
 					val  = $target.val();


 				me._changeData(index, type, {
 					Weight: val
 				});
 			}, 80);
 		},

        /**
         * @desc 流程名称变更
         */
        _onChangeName: function(e) {
			var me = this;
			if (me._timer) {
				clearTimeout(me._timer);
				me._timer = null;
			}
			me._timer = setTimeout( function() {
				var $target = $(e.target),
                    defaultName = $target.attr('data-default'),
                    name = $.trim($target.val());
                me.data.Name = name;
                if (defaultName != name) {
                    if (!$target[0].hasChange) {
                        me.data.changeNum += 1;
                        $target[0].hasChange = true;
                    }
                } else {
                    me.data.changeNum -= 1;
                    $target[0].hasChange = false;
                }
			}, 80);
        },

		/**
		 * @desc 变更导航
		 */
		_changeNav: function(index, type, name) {
			var selector = ['.pre-nav', '.next-nav', '.default-nav'][type - 1],
				$nav     = this.$(selector),
				$item    = $('.n-item', $nav).eq(index);

			$('.nav-name', $item).html(name);
			$item.toggleClass('no-name', (name == ''));
			this._toggleNav($nav);
		},

		/**
		 * @desc 改变数据
		 */
		_changeData: function(index, type, data) {
			var me = this,
				key = ['PreStages', 'NextStages', 'EndStages'][type - 1],
				oData = me.data[key][index];
			if (oData.SaleActionStageID) {
				oData.changeNum += 1;
			}
			_.extend(oData, data);
			me.data.changeNum += 1;
		},

        /**
         * @desc 改变使用范围中的规则
         */
        _onCheckRule: function(e) {
            var me = this, $target = $(e.target),
                key = $target.attr('data-key'),
                defaultVal = $target.attr('data-default');
            $target.toggleClass('mn-selected');
            me.data[key] = $target.hasClass('mn-selected');
            if (defaultVal != ($target.hasClass('mn-selected') ? 1 : 2)) {
                me.data.changeNum += 1;
            } else {
                me.data.changeNum -= 1;
            }
            return false;
        },

        /**
         * @desc 允许完成阶段新建成交
         */
        _onAllowTrade: function(e) {
            var me = this, $target = $(e.target),
                key = $target.attr('data-key'),
                defaultVal = $target.attr('data-default');
            $target.toggleClass('mn-selected');
            me.data[key] = $target.hasClass('mn-selected');
            if (defaultVal != ($target.hasClass('mn-selected') ? 1 : 2)) {
                me.data.changeNum += 1;
            } else {
                me.data.changeNum -= 1;
            }
            me.$('.complete-step-box').toggleClass('disabled', !me.data[key]);
            return false;
        },

		// 退出
		_onOut: function() {
			var me = this, confirm = null;
			if (me.data.changeNum > 0) {
				confirm= util.confirm($t("确定舍弃已填写内容吗"), $t("提示"), function() {
                    me.trigger('refresh', 'out');
					confirm.hide();
					me.hide();
				});
				return;
			}
            me.trigger('refresh', 'out');
			me.hide();
            return false;
		},

		/**
		 * @desc 最终的所有数据验证
		 */
		_validAllData: function() {
			var me = this;
			if (me._checkPreStages() && me._checkNextStages()) {
				return true;
			}
			return false;
		},

		_checkScope: function() {
			var me = this;

			if(me.data.IsAllowTradeIfWin && me.data.IsAllowTrade) {
				if(me._select.getValue() == me.data.PreStages.length) {
					util.alert($t("crm.请勿同时选择某两项内容"));
					return false;
				}
				return true;
			}
			return true;
		},

		// 检查所有的售前阶段
		_checkPreStages: function() {
			var me = this, data = me.data,
				preIndex  = 0, preValid  = false;
            // 检查逻辑去除5.6
			// preValid = _.some(data.PreStages, function(item, index) {
				// preIndex = index;
				// return item.isNew && !item.isValid;
			// });
			// if (preValid) {
				// util.alert('售前阶段' + (preIndex + 1) + '：' + '数据未填写!');
				// return false;
			// }
			return true;
		},

		// 检查所有的售后阶段
		_checkNextStages: function() {
			var me = this, nextIndex = 0,
				nextValid = false, data = me.data.NextStages;
            if (data.length == 0) {return true;}
            if (data.length < 2) {
                util.alert($t("至少创建两个售后阶段"));
                return false;
            }
            // 检查逻辑去除5.6
			// nextValid = _.some(data, function(item, index) {
				// nextIndex = index;
				// return item.isNew && !item.isValid;
			// });
			// if (nextValid) {
				// util.alert('售后阶段' + (nextIndex + 1) + '：' + '数据未填写!');
				// return false;
			// }
			return true;
		},

		/**
		 * @desc 获取添加时的阶段信息
		 */
		_getAddStagesData: function(type) {
			var me = this, data = me.data, result = [],
                pLen   = data.PreStages.length + data.EndStages.length,
				stages = [].concat(data.PreStages, data.EndStages, data.NextStages);
            console.log(stages);
			stages = _.map(stages, function(item) {return $.extend(true, {}, item)});

			_.each(stages, function(item, order) {
                var customerFieldInfos = item.CustomerFieldInfos || [];
                item.EditFlag = 1;
                if(type == 'Add') {
                    item.SaleActionStageID="";
                }
                item.SaleStageFieldRelationRequests = _.map(customerFieldInfos, function(cItem, index) {
                    return {
                        EditFlag: 1,
                        UserDefinedFieldID: cItem.UserDefinedFieldID,
                        IsNotNull:  cItem.IsNotNull,
                        FieldOrder: cItem.FieldOrder || index*1,
                        OwnerType: 2,
                        FieldApiName:cItem.FieldApiName,
                    };
                });
                _.each(item.OppoFieldInfos || [], function(oItem, index) {
                    item.SaleStageFieldRelationRequests.push({
                        EditFlag: 1,
                        UserDefinedFieldID: oItem.UserDefinedFieldID,
                        IsNotNull:  oItem.IsNotNull,
                        FieldOrder: oItem.FieldOrder || (customerFieldInfos.length + index*1),
                        OwnerType: 8,
                        FieldApiName:oItem.FieldApiName,
                    });
                });
                item.UserDefinedFieldRequests = _.map(item.UDFieldInfos || [], function(uItem) {
                    uItem.EditFlag = 1;
                    _.each(uItem.EnumDetails, function(eItem, index) {
                        eItem.EditFlag = 1;
                        // eItem.ItemCode = index + 1;  迁移新接口不需要
                        eItem.ItemCode = eItem.ItemCode ? eItem.ItemCode : 0;
                        _.each(eItem.Children, function(ecItem, cindex) {
                            ecItem.EditFlag = 1;
                            // ecItem.ItemCode = cindex + 1; 迁移新接口不需要
                            ecItem.ItemCode = ecItem.ItemCode ? ecItem.ItemCode : 0;

                        });
                    });
                    uItem.ModifyEnums = uItem.EnumDetails;
                    return uItem;
                    
                });
                item.UDFieldInfos = item.CustomerFieldInfos = item.OppoFieldInfos = item.submitData = null;
                _.each(['ContactCount', 'AsRemainDays', 'AsThatRemainDays', 'RemainDays', 'Weight'], function(key) {
                    item[key] = item[key] * 1;
                });
                item.StageOrder = (item.Type == 1) ? order + 1 : order + 1 - pLen;
                delete item.CustomerDescribe;
                delete item.CustomerIsRequiredDescribe
                delete item.OpportunityDescribe;
                delete item.OpportunityIsRequiredDescribe
                delete item.changeNum;
                delete item.isValid;
                delete item.submitData;
                result.push(item);
			});
			return result;
		},

		/**
		 * @desc 获取编辑时的阶段信息
		 */
		_getEditStagesData: function() {
			var me = this, data = me.data, result = [],
                pLen   = data.PreStages.length + data.EndStages.length,
				stages = [].concat(data.PreStages, data.EndStages, data.NextStages);
			stages = _.map(stages, function(item) {return $.extend(true, {}, item)});

			_.each(stages, function(item, order) {
				// if (_.isUndefined(item.isValid) || item.isValid) {
                    var submitData = _.extend({
                        customer: [],
                        oppo: [],
                        fields: []
                    }, item.submitData || {})

					if (item.isNew || item.changeNum > 0) {
						item.EditFlag = item.isNew ? 1 : 2;
						item.SaleStageFieldRelationRequests = _.map(submitData.customer, function(cItem, index) {
							return {
                                EditFlag: cItem.EditFlag,
                                UserDefinedFieldID: cItem.UserDefinedFieldID,
                                IsNotNull:  cItem.IsNotNull,
                                FieldOrder: cItem.FieldOrder || index,
                                OwnerType: 2,
                                FieldApiName:cItem.FieldApiName,
                            };
						});
                        _.each(submitData.oppo || [], function(oItem, index) {
                            item.SaleStageFieldRelationRequests.push({
                                EditFlag: oItem.EditFlag,
                                UserDefinedFieldID: oItem.UserDefinedFieldID,
                                IsNotNull:  oItem.IsNotNull,
                                FieldOrder: oItem.FieldOrder || submitData.customer.length + index,
                                OwnerType: 8,
                                FieldApiName:oItem.FieldApiName,
                            });
                        });
						item.UserDefinedFieldRequests = _.map(submitData.fields, function(item) {
                            item.ModifyEnums = item.ModifyEnums2 || item.ModifyEnums;
							return item;
						});
						item.UDFieldInfos = item.CustomerFieldInfos = item.OppoFieldInfos = item.submitData = null;
                        _.each(['ContactCount', 'AsRemainDays', 'AsThatRemainDays', 'RemainDays', 'Weight'], function(key) {
                            item[key] = item[key] * 1;
                        });
                        item.StageOrder = item.Type == 1 ? order + 1 : order + 1 - pLen;
                        delete item.CustomerDescribe;
                        delete item.OpportunityDescribe;
                        delete item.changeNum;
                        delete item.isValid;
                        delete item.submitData;

						result.push(item);
					}
				// }
			});
            console.log(result.concat(me.delStages));
            var res=result.concat(me.delStages);


			return res;
		},

        // 保存
        _saveHandle: function() {
            var me = this;
			if (me._validBase() && me._validStage(true) && me._checkScope()) {
                if (me.data.changeNum == 0 && me.status == 'edit') { // 编辑时没有变化 直接退出
                    me.trigger('refresh', 'out');
					me.hide();
					return;
				}
				if (me._validAllData() && me._checkScope()) {
					if (me.status == 'add') {
						me._AddActionMigration();
					} else {
                        var stages = me._getEditStagesData();

                        if (stages.length > 0) { // 修改了阶段 弹出保存方式
                            var confirm = util.confirm(saveConfirmTpl({
                                Name: me.data.Name
                            }), $t("请选择保存数据的类型"), function() {
                                me.data.IsAllowOldData = $('.allow-olddata', confirm.element).hasClass('mn-selected');
                                if (!me.data.IsAllowOldData) {
                                    var newName = $.trim($('.new-sele-name', confirm.element).val());
                                    if (newName == '') {
                                        util.alert($t("请填写新销售流程名称")+'!');
                                        return;
                                    }
                                    if (newName == me._defaultName) {
                                        util.alert($t("销售流程名称不能重复")+','+$t("请修改销售流程名称"));
                                        return;
                                    }
                                    me.data.Name = newName;
                                }
                                if (!me.data.IsAllowOldData) { // 不更新已有数据
                                    if (!$('.isstop', confirm.element).hasClass('mn-selected')) {
                                        me.data.SaleActionID = '';
                                    } else {
                                        // 202304接口迁移之后还需要单独调用停用接口
                                        me._stopUseStatusMigration({
                                            sale_action_id: me.data.SaleActionID,
                                            status: '2'
                                        })
                                    }
                                    confirm.hide();
                                    // me._addAction(true);       // true 表示不处罚刷新动作
                                    me._AddActionMigration(true);       // true 表示不触发刷新动作

                                } else {
                                    confirm.hide();
                                    // me._editAction(stages);
                                    me._AddActionMigration(stages, 'Edit');
                                }
                            }, {stopPropagation: false, className: 'crm-s-saleaction'});

                            confirm.on('radio', function(evt) {
                                var $radio = $(evt.currentTarget);
                                $('.old-sale-change', confirm.element).toggle($radio.attr('data-val') == 2);
                                confirm.resetPosition();
                            });
                        } else { // 没修改阶段 直接编辑
                            // me._editAction(stages);
                            me._AddActionMigration(stages, 'Edit');

                        }
					}
				}
            }
            return false;
        },

        /**
         * @desc 获取对象描述信息
         */
        _getDescribeLayoutMigration: function (callback) {
            var me = this;
            me.getDescribeAjax = util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/SaleActionNewObj/controller/DescribeLayout',
                data: {
                    "include_detail_describe": false,
                    "include_layout": false,
                    "apiname": "SaleActionNewObj",
                    "layout_type": "add",
                    "recordType_apiName": "default__c"
                },
                success: function (res) {
                    if (res.Result.StatusCode == 0) {
                        me.objectValueMigration = res.Value;
                        return;
                    }
                    util.alert(res.Result.FailureMessage);
                }
            }, {
                errorAlertModel: 1
            })
        },

        _getPostId: function() {
            var id = this._postid;
            return id ? '?_postid=' + id : ''
        },

        /**
         * @desc 新建和编辑销售流程
         */
		_AddActionMigration: function(flag, type='Add') {
            var me = this, data = me.data;
            if (me.createAjax) return;
            if (!me.objectValueMigration) return;
            const addStageOriginData = me._getAddStagesData(type)
            let param = migrationUtils.addAndEditParamMigration(me.objectValueMigration, data, type, addStageOriginData, me.requestId);
            console.log(param, 'param')
            me.createAjax = util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/SaleActionNewObj/action/' + type + this._getPostId(),  // this._getPostId()
                data: param,
                success: function(data) {
                    if (data.Result.StatusCode == 0) {
                        if (!flag) {
                            me.trigger('refresh', 'add');
                        } else {
                            me.trigger('refresh', 'del');
                        }

                        util.remind($t(`${type == 'Add' ? '保存' : '编辑'}成功`)+'！');
                        me.hide();
                        return;
                    }
                    util.alert(data.Result.FailureMessage);
                },
                complete: function() {
                    me.createAjax = null;
                },
            }, {
                submitSelector: $('.add-tit .b-g-btn', me.$el),
                errorAlertModel: 1
            });
        },

        /**
         * @desc 停用销售流程接口调用
         */
        _stopUseStatusMigration: function(param) {
            var me = this;
            util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/SaleActionNewObj/action/ChangeStatus',
                data: param,
                success(res) {
                    if (res.Result.StatusCode != 0) {
                        util.alert(res.Result.FailureMessage);
                    }
                }
            }, {
                errorAlertModel: 1
            })
        },    

		// 隐藏
		hide: function() {
			this.$el.hide();
			this.destroy();
		},
        //
        //格式化相关对象（客户，商机字段）数据
        _parseRelateData:function(data,requiredConfig){

            var flag=_.isArray(data)?true:false,
                requiredObj=JSON.parse(requiredConfig||'""'),
                dataArr=[];

            if(flag){
                dataArr=data;
            }else{
                var dataObj=JSON.parse(data||'""');
                for(var i in dataObj){
                    dataArr.push(dataObj[i])
                }
            }

            _.each(dataArr,function(d){
                //第一次进页面，接口数据未格式化过

                if(!d.EditFlag){
                    //格式化是否可编辑必填数据
                    var is_allow=(d.config&&d.config.attrs&&(d.config.attrs.is_required=="false"||d.config.attrs.is_required==0))?0:1;
                    //格式化是否可编辑必填数据
                    var enumDetails=[];
                    if(d.options){
                        _.each(d.options,function(item){
        					if(!item.not_usable){
        						item=_.extend(item,{
        							ItemName:item.label
        						})
        						enumDetails.push(item);
        					}
        				})
                    }
                    if(d.api_name=='country'){
                        d.label=$t('国家/省/市/区')
                    }

                    d=_.extend(d,{
                        FieldType: d.type,
                        FieldCaption:d.label,
        				FieldApiName: d.api_name,
                        FieldName: d.api_name,
        				DefaultValue:d.default_value||'',
                        RelationField:d.relation_field||'',
                        IsAllowEditNotNull:is_allow,
                        IsAllowHide:d.is_allowhide||false,
                        IsWatermark:d.is_watermark||false,
                        IsNotNull:requiredObj[d.api_name]||d.is_required,
                        IsVisible:d.is_visible||true,
                        UserDefinedFieldID: d._id,
                        EnumDetails: enumDetails
                    })
                }
            });
            var typeObj = {
				province: 1,
				city: 1,
				district: 1
			}
            //屏蔽省市区
            dataArr=_.filter(dataArr,function(d){
                return !typeObj[d.FieldType]
            })
            return dataArr
        },

        /**
		 * @desc 删除阶段时检查销售阶段是否正在使用
		 */
        //  _checkSaleAction: function(id, callBack) {
        //     var me = this;
        //     if (!id || id == '') {
        //         callBack && callBack();
        //         return;
        //     }
        //     util.FHHApi({
        //         url: '/EM1HCRM/SaleActionSetting/CheckSaleActionStageIsApply',
        //         data: {
        //             SaleActionStageID: id
        //         },
        //         success: function(data) {
        //             if (data.Result.StatusCode == 0) {
        //                 if (!data.Value.IsApply) {
        //                     callBack && callBack();
        //                 } else {
        //                     util.alert($t("有商机正在使用该阶段无法删除"));
        //                 }
        //                 return;
        //             }
        //             util.alert(data.Result.FailureMessage);
        //         }
        //     }, {
        //         errorAlertModel: 1
        //     });
        // },

        /**
         * @desc 新建销售流程
         */
		// _addAction: function(flag) {
        //     var me = this, data = me.data;
        //     util.FHHApi({
        //         url: '/EM1HCRM/SaleActionSetting/AddSaleAction',
        //         data: {
        //             SaleActionRequest: {
        //                 SaleActionID:    data.SaleActionID,
        //                 Name:            data.Name,
        //                 Description:     data.Description,
        //                 CircleIDs:       data.CircleIDs,
        //                 IsAllowOldData:  data.IsAllowOldData,
        //                 IsAddTradeIfWin: data.IsAllowTradeIfWin,
		// 				CheckFieldIfWin: data.CheckFieldIfWin,
        //                 IsAllowTrade:    data.IsAllowTrade,
        //                 AllowTradeStageOrder: data.IsAllowTrade ? data.AllowTradeStageOrder : 0
        //             },
        //             SaleActionStageRequests: me._getAddStagesData()
        //         },
        //         success: function(data) {
        //             if (data.Result.StatusCode == 0) {
        //                 if (!flag) {
        //                     me.trigger('refresh', 'add');
        //                 } else {
        //                     me.trigger('refresh', 'del');
        //                 }
        //                 util.remind($t("保存成功")+'！');
        //                 me.hide();
        //                 return;
        //             }
        //             util.alert(data.Result.FailureMessage);
        //         }
        //     }, {
        //         submitSelector: $('.add-tit .b-g-btn', me.$el),
        //         errorAlertModel: 1
        //     });
        // },

        // /**
        //  * @desc 编辑销售流程
        //  */
        // _editAction: function(stages) {
        //     var me = this, data = me.data;
        //     util.FHHApi({
        //         url: '/EM1HCRM/SaleActionSetting/ModifySaleAction',
        //         data: {
        //             SaleActionRequest: {
        //                 SaleActionID:    data.SaleActionID,
        //                 Name:            data.Name,
        //                 Description:     data.Description,
        //                 CircleIDs:       data.CircleIDs,
        //                 IsAllowOldData:  data.IsAllowOldData,
        //                 IsAddTradeIfWin: data.IsAllowTradeIfWin,
		// 				CheckFieldIfWin: data.CheckFieldIfWin,
        //                 IsAllowTrade:    data.IsAllowTrade,
        //                 AllowTradeStageOrder: data.IsAllowTrade ? data.AllowTradeStageOrder : 0
        //             },
        //             SaleActionStageRequests: stages || me._getEditStagesData()
        //         },
        //         success: function(data) {
        //             if (data.Result.StatusCode == 0) {
        //                 me.trigger('refresh', 'edit');
        //                 util.remind($t("编辑成功"));
        //                 me.hide();
        //                 return;
        //             }
        //             util.alert(data.Result.FailureMessage);
        //         },
        //         error: function() {
        //             util.remind(3, $t("编辑失败"));
        //         }
        //     }, {
        //         submitSelector: $('.add-tit .b-g-btn', me.$el),
        //         errorAlertModel: 1
        //     });
        // },

        destroy: function() {
			this._circle.destroy();
            this.$('.step-status-select').each(function(index, item) {
               item.select &&  item.select.destroy();
            });
            this._scrollbar && this._scrollbar.destroy();
            this._select.destroy();
			this._stage && this._stage.destroy();
			this.undelegateEvents();
			this.remove();
			this.data = this.delStages = this.stageData = this.options = this.events = null;
			Backbone.View.prototype.remove.call(this);
        }
    });
    

    module.exports = AddEdite;

});
