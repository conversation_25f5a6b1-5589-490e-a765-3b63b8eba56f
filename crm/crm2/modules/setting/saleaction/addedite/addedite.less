.crm-s-saleaction {
    
    // 布局
    .add-saleaction {
        position: fixed;
            top: 0; right: 0; bottom: 0; left: 0;
        background-color: var(--color-neutrals01);    
        z-index: 1200;     
    }
    
    // 滚动条
    .scroll-el {
        width: 100%;height: 100%;
    }
    
    // 标题
    .add-tit {
        padding: 0 30px;
        height: 60px;
        background-color: #4E535E;
        .text {
            float: left;
            font-size: 20px;
            line-height: 60px;
            color: var(--color-neutrals01);
        }
        .btns-box {
            float: right;
            span {
                padding: 0 30px;
                height: 36px;
                line-height: 36px;
                margin: 10px 0 0 10px;
                font-size: 14px;
            }
        }
    }

    // 内容区域布局
    .add-con {
        width: 100%;
        position: absolute;
        left: 0; top: 60px; bottom: 0;right: 0;      
    }
    
    // 左侧
    .con-l {
        width: 200px;
        height: 100%;
        border-right: 1px solid #d8d8d8;
        position:absolute; top: 0;left: 0;
        background-color: #f4f6fc;
        .item {
            padding: 40px 25px;
            color: #999;
            cursor: pointer;
            border-top: 1px solid #eee;
            margin-top: -1px;
            &.active {
                color: #333;
                background-color: #e9ecf6;
            }
        }
        .nav-tit {
            p {
                padding-top: 5px;
                font-size: 16px;
            }
        }

		.nav-box, .default-nav {
			padding-top: 5px;
			margin-top: 15px;
			border-top: 1px solid #eee;
		}
		
		.n-item {
			position: relative;
            padding: 8px 15px 8px 0!important;;
			font-size: 13px!important;
			color: @linkColor;
			cursor: pointer;
			&:after, &:before {
				display: none;
				content: ' ';
				width: 0;
				height: 0;
				overflow: hiddne;
				border: 6px solid transparent;
				border-left-color: #ff8837;
				position: absolute;
					right: 0; top: 11px;
					z-index: 1;
			}
			&:after {
				right: 2px;
				border-left-color: #e9ecf6;
				z-index: 2;
			}
			&.active {
				color: #ff8837;
				&:after, &:before {
					display: block;
				}
			}
		}
        
        .default-nav {
            .n-item {
                color: #bbb;
                cursor: default;
            }
        }
        
		.no-name {
			display: none;
		}
    }
    
    // 内容区域
    .con-c {
        position: absolute;
            left: 201px; top: 0; bottom: 0; right: 0;    
        .item {
            padding: 40px;
        }
    }
    
    // 基础信息设置
    .base-name {
        padding-bottom: 40px;
        border-bottom: 1px dashed #eee;
        margin-bottom: 40px;
        line-height: 36px;
        label {
            padding-bottom: 12px;
            display: block;
            font-size: 16px;
            line-height: 24px;
            em {
                margin-right: 5px;
                color: #ff7663;
                line-height: 20px;
                font-size: 14px;
            }
        }
        input {
            width: 410px;
        }
    }
    
    // 设置阶段
    .base-step {
        h3 {
            font-size: 16px;
            padding-bottom: 30px;
        }
        .step-box {
            width: 585px;
			padding-bottom: 40px;
            float: left;
        }
        .step-box-next {
            width: 400px;
        }
        .end-step {
            margin-top: 15px;
        }
		.tit-name, .tit-may, .tit-status {
			display: inline-block;
			padding-bottom: 10px;
			margin-left: 60px;
			font-size: 12px;
		}
		.tit-may {
			margin-left: 165px;
		}
        .tit-status {
            margin-left: 48px;
        }
        .add-step-btn {
            color: @linkColor;
            cursor: pointer;
            &:hover {
                text-decoration: underline;
            }
        }
    }
    .b-item {
        position: relative;
        width: 100%;
        overflow: hidden;
		padding-bottom: 15px;
		line-height: 34px;
        label, input, span, .step-status {
            float: left;
			margin-right: 10px;
        }
		label {
			color: #999;
            width: 55px;
            margin-right: 5px;
		}
        .step-name {
			width: 188px;
        }
		.step-may {
			width: 30px;
			padding: 5px 22px 5px 10px;
		}
        .step-status {
            width: 50px;
            margin-left: 15px;
            color: @gray;
        }
        .step-status-select {
            width: 90px;
            &.error {
                .g-select-title-wrapper {
                    border-color: #f5715f;
                }
            }
        }
		.add-step,.del-step {
			width: 20px;
			height: 20px;
			margin-top: 6px;
			background-color: #d4d6dc;
			color:#9fa3b9;
			#css3>.radius(50%);
			text-align: center;
			line-height: 20px;
			font-weight: 900;
			cursor: pointer;
			&:hover {
				color: var(--color-neutrals01);
				background-color: @linkColor;
			}
		}
		.del-step:hover {
			background-color: #ff7663;
		}
		.txt {
			position: absolute;
				top: 0; right: 241px;
			font-family: Arial;
			font-size: 12px;
			color: #ccc;
		}
        .fm-error {
            clear: both;
        }
    }
    
    
    
    // 适用范围区域
    .r-item {
        padding-bottom: 40px;
        line-height: 24px;
        .circle-select {
            width: 370px;
            margin-top: 15px;
        }
        h3 {
            font-size: 16px;
            padding-bottom: 5px;
        }
        .tip {
            color: #999;
        }
        .mn-checkbox-item {
            vertical-align: -2px;
            margin-right: 15px;
        }
    }
    
    // 保存提示
    .save-confirm {
        p {
            position: relative;
            padding:0 0 15px 25px;
            font-size: 15px;
        }
        span {
            position: absolute;
                left: 0; top: 5px;
            margin-right: 10px;
        }
        .save-tip {
            display: block;
            position: relative;top: 0;
            padding-top: 3px;
            font-size: 12px;
            color: #666;
        }
    }
    .old-sale-change {
        padding-left: 25px;
        p {
            padding-bottom: 5px;
            line-height: 30px;
        }
        label {
            display: inline-block;
            width: 115px;
            vertical-align: middle;
        }
        
    }
    .new-sele-name {
        width: 180px;
        padding: 3px 5px;
    }
        
    // 完成第几阶段方可新建成交
    .complete-step-box {
        font-size: 16px;
        line-height: 34px;
        position: relative;
        span {
            margin-right: 15px;
        }
        .complete-step {
            margin: 0 10px;
            display: inline-block;
            width: 100px;
            vertical-align: middle;
            margin-top: -2px;
        }
        .select-title {
            font-size: 13px;
        }
        .mask {
            display: none;
            width: 150px;
            height: 34px;
            position: absolute;
                left: 80px; top: 0;
            z-index: 20;
        }
        &.disabled {
            .mask {display: block;}
            .g-select-title-wrapper {
                background-color: #eee;
                color: #999;
            }
        }
        
        .crm-tip {
            position: absolute;
                left: 340px; top: 10px;
        }
    }  
    .crm-c-dialog .mn-radio-box .mn-selected {
      position: absolute;
    }  
}
