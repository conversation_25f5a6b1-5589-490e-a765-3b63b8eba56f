/**
 * @desc 销售流程中的阶段设置
 * author qigb
 */
define(function(require, exports, module) {
    var util    = require('crm-modules/common/util'),
		FieldManage = require('crm-modules/setting/common/fieldmanage/fieldmanage'),
        tpl     = require('./template/tpl-html');


    var Stage = Backbone.View.extend({

        options: {
			className: 'stage-wrap',
			tageName:  'div',
			$el:  null, // 外层元素
            data: null  // 阶段数据
        },

		initialize: function() {
			var me = this;
			me.options.$el.append(me.$el);
            me.relateObjPos = 2;
            me._getAllField(function() {
                me._initFieldMange();
                me._renderRule();
                me._bindEvents();
            });
		},

        /**
         * @desc 获取销售流程下的所有自定义字段
         * 判断 是否允许添加自定义字段
         * 如果由企业运用了自定义字段 提示即将停用
         * 否则不再允许添加自定义字段
         */
		_getAllField: function(cb) {
            var me = this, key = '__saleActionStageRelateObjPos';
            if (CRM.get(key)) {
                setTimeout(function() {
                    cb && cb();
                }, 100);
                return;
            }
            // 商机迁移，这个是.net接口，已停用
            // util.FHHApi({
            //     url: '/EM1HCRM/UserDefinedField/GetAllUserDefinedFieldList',
            //     data: {
            //         OwnerTypes: [7]
            //     },
            //     success: function(data) {
            //         if (data.Result.StatusCode == 0) {
            //             var items = data.Value.Items || [];
            //             me.relateObjPos = items.length > 0 ? 2 : 1;
            //         }
            //     },
            //     complete: function() {
            //         CRM.set(key, true);
            //         cb && cb();
            //     }
            // }, {
            //     errorAlertModel: 1
            // });
            CRM.set(key, true);
            cb && cb();
        },

		_initFieldMange: function() {
			var me = this, data = me.options.data.UDFieldInfos, addFilter = [16,20,23,24,25,26,27,28,29,30,31];
            if (CRM.control.isYunZhiJia) {
                addFilter.push(14);
            }
            var moData=me.options.data;

			me._field = new FieldManage({
				el:      me.$el,
                isRelateObj:   true,
                relateObjPos:  me.relateObjPos,
                isShowHideSet: false,
                isSetRelate: false,
                isSaveBtn: false,
                addFilter: addFilter,
                from: 3,
                relateData: {
                    'customer':moData.CustomerFieldInfos.length>=1?me._parseRelateData(moData.CustomerFieldInfos,moData.CustomerIsRequiredDescribe):me._parseRelateData(moData.CustomerDescribe,moData.CustomerIsRequiredDescribe),
                    'oppo': moData.OppoFieldInfos.length>=1?me._parseRelateData(moData.OppoFieldInfos,moData.OpportunityIsRequiredDescribe):me._parseRelateData(moData.OpportunityDescribe,moData.OpportunityIsRequiredDescribe),
                },
				fieldData: me.options.data.UDFieldInfos || []
			});

			me._field.on('add', function() {
				util.hideErrmsg(me.$('.tit-backinfo .text'));
				me.$('.tit-backinfo').hide();
			});
		},
        //格式化相关对象（客户，商机字段）数据
        _parseRelateData:function(data,requiredConfig){

            var flag=_.isArray(data)?true:false,
                requiredObj=JSON.parse(requiredConfig||'""'),
                dataArr=[];

            if(flag){
                dataArr=data;
            }else{
                var dataObj=JSON.parse(data||'""');
                for(var i in dataObj){
                    dataArr.push(dataObj[i])
                }
            }

            _.each(dataArr,function(d){
                //第一次进页面，接口数据未格式化过

                if(!d.EditFlag){
                    //格式化是否可编辑必填数据
                    var is_allow=(d.config&&d.config.attrs&&(d.config.attrs.is_required=="false"||d.config.attrs.is_required==0))?0:1;
                    //格式化是否可编辑必填数据
                    var enumDetails=[];
                    if(d.options){
                        _.each(d.options,function(item){
        					if(!item.not_usable){
        						item=_.extend(item,{
        							ItemName:item.label
        						})
        						enumDetails.push(item);
        					}
        				})
                    }
                    if(d.api_name=='country'){
                        d.label=$t('国家/省/市/区')
                    }

                    d=_.extend(d,{
                        FieldType: d.type,
                        FieldCaption:d.label,
        				FieldApiName: d.api_name,
                        FieldName: d.api_name,
        				DefaultValue:d.default_value||'',
                        RelationField:d.relation_field||'',
                        IsAllowEditNotNull:is_allow,
                        IsAllowHide:d.is_allowhide||false,
                        IsWatermark:d.is_watermark||false,
                        IsNotNull:requiredObj[d.api_name]||d.is_required,
                        IsVisible:d.is_visible||true,
                        UserDefinedFieldID: d._id,
                        EnumDetails: enumDetails
                    })
                }
            });
            var typeObj = {
				province: 1,
				city: 1,
				district: 1
			}
            //屏蔽省市区
            dataArr=_.filter(dataArr,function(d){
                return !typeObj[d.FieldType]
            })
            return dataArr
        },

        /**
         * @desc 渲染规则
         */
        _renderRule: function() {
            var me = this;
            me.$('.defined-box').before(tpl(me.options.data));
        },

        /**
         * @desc 编辑数据变化
         */
        _setChange: function() {
            this.options.data.changeNum += 1;
        },

        events: {
            'click .s-item .mn-checkbox-item': '_onCheckRule',
			'focus .stage-rule .b-g-ipt':      '_hideError'   // 隐藏所有错误信息
        },

        _bindEvents: function() {
            var me = this;
            util.onlyAllowNum('.j-days', me.$el, false , 99999, $.proxy(me._changeDays, me));
            util.fixInputEvent('.j-days', function(e) {
                me._changeDays($(e.target));
            }, me.$el);
            util.fixInputEvent('.j-desc', function(e) {
                me._changeDays($(e.target));
            }, me.$el);
        },


        /**
         * @desc 选择规则
         */
        _onCheckRule: function(e) {
            var me = this, $target = $(e.target),
                key = $target.attr('data-key');
            $target.toggleClass('mn-selected');
            me.options.data[key] = $target.hasClass('mn-selected');
            me._setChange();
			me._hideError(e);
            return false;
        },

        /**
         * @desc 变更天数 要求
         */
        _changeDays: function($target) {
            var me = this,
                key = $target.attr('data-key'),
                val = $target.val();
            me.options.data[key] = val;
            me._setChange();
        },


		// 返回阶段数据
		getValue: function() {
			var me = this, data = me._doFieldData()
            if(data.fields?.length) {
                data.fields.forEach(item => {
                    const oldField = me.options.data.UDFieldInfos.find(field => field.UserDefinedFieldID == item.UserDefinedFieldID);
                    if(oldField) {
                        // 202304迁移为了保留服务端返回的自定义字段;
                        item.is_deleted                         = oldField.is_deleted;
                        item.create_time                        = oldField.create_time;
                        item.created_by                         = oldField.created_by;
                        item.last_modified_by                   = oldField.last_modified_by;
                        item.last_modified_time                 = oldField.last_modified_time;
                        item.object_describe_api_name           = oldField.object_describe_api_name;
                        item.sale_action_stage_id               = oldField.sale_action_stage_id;
                        item.sale_action_stage_id__relation_ids = oldField.sale_action_stage_id__relation_ids;
                        item.tenant_id                          = oldField.tenant_id;
                        item.version                            = oldField.version;
                    }
                })
            }
			me.options.data.UDFieldInfos = data.fields;
            me.options.data.CustomerFieldInfos = data.customer;
            me.options.data.OppoFieldInfos = data.oppo;
            me.options.data.submitData = me._field.getSubmitData();
            if (me._field.changeNum > 0) {
                me.options.data.changeNum += 1;
            }
			return me.options.data;
		},

        /**
         * @desc 验证信息是否通过
         */
		valid: function() {
            var me = this,
                data = me._field.getSubmitData();
            if (!data || !me._validRule()) {
                // me._field.showError(data.error);
				// if (data.fields.length == 0) {
					// me.$('.tit-backinfo').show();
					// util.showErrmsg(me.$('.tit-backinfo .text'), '至少添加一项字段， 以便员工填写阶段反馈， 请添加字段');
				// }
                return false;
            }
			me.options.data.isValid = true;
            return true;
        },

        /**
         * @desc 验证规则
         * @return 是否通过验证
         */
        _validRule: function() {
            var me = this, num = 0, $desc = me.$('.j-desc');
            me.$('.j-days').each(function(index, item) {
				var $item = $(item), val = $.trim($item.val()),
					$p = $item.closest('p'), key = $item.attr('data-key'),
					tip = (key == 'ContactCount') ? $t("要存在的联系人的个数") : $t("停留提醒天数");
				if ($('.mn-checkbox-item', $p).hasClass('mn-selected') && val == '') {
					num += 1;
					util.showErrmsg($item, $t("crm.请填写") + tip);
				}
			});
            return num == 0;
        },

        /**
         * @desc 隐藏所有错误信息
         */
        _hideError: function(e) {
            var $target = $(e.target);
            util.hideErrmsg($target);
        },

		/**
		 * @desc 处理字段管理中的数据
         * 用于下次渲染
		 */
		_doFieldData: function() {
			var me = this,
				data = me._field.getSubmitData(true);
                console.log(112)
			var fields = _.map(data.fields, function(item) {
				if (item.FieldType == 8 || item.FieldType == 9) {
                    item.ModifyEnums = item.ModifyEnums2 && item.ModifyEnums2.length > 0 ? item.ModifyEnums2: item.ModifyEnums;
					// _.each(item.ModifyEnums || [], function(item, index) {
						// if (item.EditFlag == 1 && item.ItemCode == 999) {
							// item.ItemCode = 'new' + index;
						// }
					// });
					item.ModifyEnums = item.ModifyEnums && item.ModifyEnums.length > 0 ? item.ModifyEnums : [{
						ItemName: $t("选项一"),
						ItemCode: 'dfc1'
					}, {
						ItemName: $t("选项二"),
						ItemCode: 'dfc2'
					}];
                    // item.ModifyEnums = _.filter(item.ModifyEnums, function(item) {
                        // return item.EditFlag != 3;
                    // });
				}
                // if (item.FieldType == 14) {
                    // item.ModifyEnums = _.filter(item.ModifyEnums, function(item) {
                        // item.Children = _.filter(item.Children, function(cItem) {
                            // return cItem.EditFlag != 3;
                        // });
                        // return item.EditFlag != 3;
                    // });
                // }
				item.FieldCaption = item.FieldCaption || $t("字段名称");
				item.EnumDetails = item.ModifyEnums;
				return _.extend({
					IsAllowEditNotNull: true,
					IsAllowEditOption:  true,
					FieldProperty:      2
				}, item);
			});
            var customer = _.map(data.customer, function(item) {
				if (item.FieldType == 8 || item.FieldType == 9) {
					item.ModifyEnums = [{
						ItemName: $t("选项一"),
						ItemCode: 'dfc1'
					}, {
						ItemName: $t("选项二"),
						ItemCode: 'dfc2'
					}];
				}
				item.EnumDetails = item.ModifyEnums;
				return _.extend({
					IsAllowEditOption:  false
				}, item);
			});
            var oppo = _.map(data.oppo, function(item) {
				if (item.FieldType == 8 || item.FieldType == 9) {
					item.ModifyEnums = [{
						ItemName: $t("选项一"),
						ItemCode: 'dfc1'
					}, {
						ItemName: $t("选项二"),
						ItemCode: 'dfc2'
					}];
				}
				item.EnumDetails = item.ModifyEnums;
				return _.extend({
					IsAllowEditOption:  false
				}, item);
			});
			return {
                fields: _.filter(fields, function(item) {  return item.EditFlag != 3; }),
                customer: _.filter(customer, function(item) { return item.EditFlag != 3;}),
                oppo: _.filter(oppo, function(item) { return item.EditFlag != 3;})
            }
		},


		destroy: function() {
			var me = this;
            // me.getValue();
			me.undelegateEvents();
			me.options = this.events = null;
			me.remove();
			me._field.destroy();
		}
    });

    module.exports = Stage;

});
