.crm-s-saleaction {
    .stage-wrap {
        .full();
    }
    .stage-rule {
        width: 610px;
        padding: 38px 0 25px 0;
        margin-left: 40px;
        border-bottom: 1px dashed #eee;
    }
    .s-item {
        padding-bottom: 10px;
        label {
            display: block;
            font-size: 16px;
            padding-bottom: 12px;
            em {
                margin-right: 5px;
                color: #ff7663;
                line-height: 20px;
                font-size: 14px;
            }
        }
        textarea {
            width: 590px;
        }
        p {
            width: 100%;
            overflow: hidden;
            padding: 8px 0;
        }
        .mn-checkbox-box {
            margin-top: 0 !important;
        }
        .mn-checkbox-item {
            float: left;
            margin:7px 8px 0 0;
        }
        .check-lb {
            width: 100%;
            overflow: hidden;
            line-height: 28px;
            em {
                float: left;
            }
        }
        input {
            float: left;
            margin: 0 5px;
            padding: 2px 8px;
            width: 34px;
        }
        .fm-error {
            display: block;
            padding-top: 2px;
            float: none;
            clear: both;
        }
    }
    
    // 字段管理中的样式覆盖

    .crm-g-form {
        &.defined-box .form-tit {
            display: none;
        }
        .fm-item {
            width: 570px;
            padding: 10px 20px;
            &.dragging {
                width: 570px!important;
            }
        }
        .fm-lb {
            display: block;
            width: 100%;
            padding: 0 0 5px 0;
            line-height: 32px;
            em {
                width: auto;
                margin-right: 5px;
                font-size: 13px;
                line-height: 30px;
            }
        }
        .fm-ipt {
            margin: 0;
            width: 590px;
        }
        .b-g-ipt {
            width: 545px;
        }
        .select-box-mask {
            top: 46px!important;
            left: 22px!important;
        }
    }
    
    .crm-c-fieldmanage {
        .preset-box{
            display: none;
        }
        .left {
            width: 810px;
            bottom: 115px;
        }
        .right {
            left: 810px;
        }
        .field-error {
           padding: 5px 0 0 5px;
        }
        .del-field{
            top: 5px;
            left: 580px;
        }
        .fields-type-box{
            left: 62px;
        }
        @media screen and (max-width: 1412px) {
            .left {
                width: 675px;
            }
            .right{
                left: 675px;
            }
            .crm-g-form{
                padding-left: 30px;
            }
        }
    }
    .fields-type-box {
        bottom: 35px!important;
    }
    
    /* 没有阶段时的提示
    .tit-backinfo {
        margin: 0 40px;
        padding: 30px 0 0;
        .text {
            display: block;
            height: 1px;
            overflow: hidden;
        }
    }*/
}
