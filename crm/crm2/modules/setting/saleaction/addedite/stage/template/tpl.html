<div class="stage-rule">

    <div class="s-item">
        <label>{{$t("阶段名称")}}</label>
        <p style="padding: 0 0 25px;">{{{{-Name}}}}</p>
    </div>
    
    <div class="s-item">
        <label>{{$t("阶段要求")}}</label>
        ##var type_t = $t('请填写此阶段对{{type}}人员的要求以便{{type}}人员更好的执行', {type: Type == 1 ? $t("售前") : $t("售后")})##
        <textarea class="b-g-ipt j-desc" data-key="Description" maxlength="1000" placeholder='{{type_t}}'>{{{{-Description}}}}</textarea>
    </div>
    <div class="s-item mn-checkbox-box">
        ## if (Type == 1) { ##
        <p>
            <span class="mn-checkbox-item j-salestate {{IsTimeoutRemind ? 'mn-selected' : ''}}" data-key="IsTimeoutRemind"></span>
            <span class="check-lb">
                <em>{{$t("本阶段停留")}}</em>
                <input class="b-g-ipt j-days" type="text" value="{{RemainDays}}" maxlength="5" data-key="RemainDays" />
                <em>{{$t("天时提醒销售人员执行（当商机关联了合作伙伴时提醒人为外部负责人）")}}</em>
            </span>
        </p>
        <p>
            <span class="mn-checkbox-item j-leader {{IsLeaderConfirm ? 'mn-selected' : ''}}" data-key="IsLeaderConfirm"></span>
            <span class="check-lb">{{$t("本阶段完成需直属上级确认（当商机由合作伙伴来推进时由该数据的负责人来确认）")}}</span>
        </p>
        <p>
            <span class="mn-checkbox-item j-contacts {{IsFinishByContacts ? 'mn-selected' : ''}}" data-key="IsFinishByContacts"></span>
            <span class="check-lb">
                <em>{{$t("客户中至少存在")}}</em>
                <input class="b-g-ipt j-days" type="text" value="{{ContactCount}}" maxlength="5" data-key="ContactCount" />
                <em>{{$t("个联系人时方可完成本阶段")}}</em>
            </span>
        </p>
        ## } else { ##
        <p>
            <span class="mn-checkbox-item j-salestate {{AsIsThatDayRemind ? 'mn-selected' : ''}}" data-key="AsIsThatDayRemind"></span>
            <span class="check-lb">
                <em>{{$t("本阶段停留")}}</em>
                <input class="b-g-ipt j-days" type="text" value="{{AsThatRemainDays}}" data-key="AsThatRemainDays" maxlength="5" />
                <em>{{$t("天时提醒售后人员执行")}}</em>
            </span>
        </p>
        <p>
            <span class="mn-checkbox-item j-saletimeout {{AsIsTimeoutRemind ? 'mn-selected' : ''}}" data-key="AsIsTimeoutRemind"></span>
            <span class="check-lb">
                <em>{{$t("本阶段停留")}}</em>
                <input class="b-g-ipt j-days" type="text" value="{{AsRemainDays}}" data-key="AsRemainDays" maxlength="5" />
                <em>{{$t("天后超时提醒负责人及售后人员")}}</em>
             </span>
        </p>
        ## } ##
    </div>
</div>
<div class="tit-backinfo b-g-hide">
	<span class="text"></span>
</div>
