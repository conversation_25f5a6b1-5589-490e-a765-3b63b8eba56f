<div class="add-saleaction">
    <h3 class="add-tit">
        <span class="text">{{status=='add' ? $t("新建销售流程"): $t("编辑销售流程")}}</span>
        <div class="btns-box">
            <span class="b-g-btn">{{$t("保存")}}</span>
            <span class="b-g-btn-cancel">{{$t("退 出")}}</span>
        </div>
    </h3>
    
    <div class="add-con">

        <div class="con-l j-nav">
            <div class="scroll-el">
                <div class="item active">
                    <div class="nav-tit">
                        <h3>{{$t("第一步")}}</h3>
                        <p>{{$t("设置基本信息")}}</p>
                    </div>
                </div>
                <div class="item pre-step-nav step-nav">
                    <div class="nav-tit">
                        <h3>{{$t("第二步")}}</h3>
                        <p>{{$t("售前阶段反馈表")}}</p>
						<div class="pre-nav nav-box"></div>
                        <div class="default-nav">
                            ## _.each(EndStages || [], function(item) { ##
                            ## if (item.StageFlag != 1) { ##
                            <p class="n-item" data-id="{{item.SaleActionStageID}}"><span>{{$t("结束:")}}</span><span class="nav-name">{{{{-item.Name}}}}</span></p>
                            ## } ##
                            ## }) ##
                        </div>
                    </div>
                </div>
                ## if (isNotYunZhiJia) { ##
                <div class="item next-step-nav step-nav">
                    <div class="nav-tit">
                        <h3>{{$t("第三步")}}</h3>
                        <p>{{$t("售后阶段反馈表")}}</p>
						<div class="next-nav nav-box"></div>
                    </div>
                </div> 
                ## } ##
                <div class="item">
                    <div class="nav-tit">
                        ## if (isNotYunZhiJia) { ##
                        <h3>{{$t("第四步")}}</h3>
                        ## } else { ##
                        <h3>{{$t("第三步")}}</h3>
                        ## } ##
                        <p>{{$t("选择适用范围")}}</p>
                    </div>
                </div>
            </div>
        </div>
        
        
        <div class="con-c j-con">
            <div class="scroll-el">
            
                <div class="item base-item">
                    <div class="base-name">
                        <label><em>*</em>{{$t("销售流程名称")}}</label>
                        <input type="text" data-default="{{Name}}" maxlength="20" value="{{Name}}" class="b-g-ipt j-base-name" placeholder='{{$t("例如销售部销售流程")}}' />
                    </div>
                    <div class="base-step b-g-clear">
                        <div class="step-box step-box-pre">
                            <h3>{{$t("售前阶段设置")}}</h3>
                            <h4><span class="tit-name">{{$t("阶段名称")}}</span><span class="tit-may">{{$t("赢率")}}</span><span class="tit-status">{{$t("状态")}}</span></h4>
							<div class="pre-step j-step" data-type="1"></div>
                            <div class="default-step end-step j-step" data-type="3">                             
                            </div>
                        </div>
                        ## if (isNotYunZhiJia) { ##
                        <div class="step-box step-box-next">
                            <h3>{{$t("售后阶段设置")}}</h3>
                            <h4><span class="tit-name">{{$t("阶段名称")}}</span></h4>
							<div class="next-step j-step" data-type="2"></div>
                        </div>
                        ## } ##
                    </div>
                </div>
                
                
                <div class="item b-g-hide"></div>
                
                ## if (isNotYunZhiJia) { ##
                <div class="item b-g-hide"></div>
                ## } ##
                
                <div class="item b-g-hide">
                    <div class="r-item">
                        <h3><span>{{$t("以下部门今后新增的商机将使用此销售流程")}}</span></h3>
                        <div class="circle-select"></div>
                    </div>
					<div class="r-item mn-checkbox-box">
						<h3><span class="mn-checkbox-item {{CheckFieldIfWin ? 'mn-selected' : ''}}" data-default="{{CheckFieldIfWin ? 1 : 2}}" data-key="CheckFieldIfWin"></span>{{$t("赢单时校验之前所有阶段必填项")}}</h3>
					</div>
                    <div class="r-item mn-checkbox-box">
                        <h3><span class="mn-checkbox-item {{IsAllowTradeIfWin ? 'mn-selected' : ''}}" data-default="{{IsAllowTradeIfWin ? 1 : 2}}" data-key="IsAllowTradeIfWin"></span>{{$t("必须新建订单方可赢单")}}</h3>
                        <p class="tip">{{$t("勾选并保存当选择赢单时必须新建销售订单后商机的状态才为赢单反之不需要新建销售订单直接赢单")}}</p>
                    </div>
                    <div class="complete-step-box mn-checkbox-box {{IsAllowTrade ? '' : 'disabled'}}">
                        <span class="mn-checkbox-item {{IsAllowTrade ? 'mn-selected' : ''}}" data-default="{{IsAllowTrade ? 1 : 2}}" data-key="IsAllowTrade"></span>{{$t("完成售前")}}<div class="j-complete complete-step"></div>{{$t("后方可新建订单")}}<div class="crm-tip">
                            <span class="tip-btn"></span>
                            <div class="tip-text">
                                <p>1.{{$t("如果需要此功能控制新建订单时关联商机建议将新建销售订单中的商机字段设置为必填")}},{{$t("才能达到更好的效果。")}}</p>
                                <p>2.{{$t("使用该销售流程的商机需要完成指定阶段后才能在新建订单时关联此商机。")}}</p>
                            </div>
                        </div>
                        <div class="mask"></div>
                    </div>
                </div>                
            </div>
        </div>
    </div>
</div>
