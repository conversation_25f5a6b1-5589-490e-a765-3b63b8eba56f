/**
 * 销售流程详情侧滑
 * <AUTHOR>
 */
define(function(require, exports, module) {

    var util      =  require('crm-modules/common/util'),
        Slide     =  require('crm-modules/common/slide/slide'),
        Select    =  require('crm-widget/select/select'),
        AddEdite  =  require('./../addedite/addedite'),
        tpl       =  require('./template/tpl-html');


    var Detail = Slide.extend({

        events: {
            'click':                       'hideSelect',
            // 'mouseenter .step-nav-item':   'stepNavHandle',
            'click .detail-nav span':      'navHandle',
            'click .page-select span':     'selectHandle',         // 下拉事件
            'click .page-select li':       'selectedHandle',       // 下拉选择事件
            'click .j-del':                'delHandle',
            'click .j-edite':              'editHandle',
            'click .step-type-tab span':   'stepTypeTabHandle'    // 切换售前售后
        },

        // 当前销售流程数据
        curSaleData: null,

        // 切换售前售后流程tab
        stepTypeTabHandle: function(e) {
            var $target = $(e.currentTarget),
                type = $target.attr('data-type'),
                $detailBox = $target.parents('.step-type-tab').siblings('.detail-con');
            $detailBox.removeClass('sale-nodata');
            if( $target.hasClass('cur') ){
                return;
            }
            var curTargetHasNoData = $('.' + type + '-sale-box', this.$el).find('.step-item').length === 0 ? 'sale-nodata':'';
            $target.addClass('cur').siblings().removeClass('cur');
            $detailBox.addClass( curTargetHasNoData );
            $('.j-step-type-item', this.$el).hide();
            $('.' + type + '-sale-box', this.$el).show();
        },

        hideSelect: function() {
            $('.page-select', this.$el).removeClass('page-selected');
        },
        
        // 删除
        delHandle: function(e) {
            var me = this,
                tip = $t("确定删除这个销售流程吗");
            var confirm = util.confirm(tip, $t("删除"), function() {
                util.FHHApi({
                   url: '/EM1HCRM/SaleActionSetting/DeleteSaleAction',
                   data: {
                       SaleActionID: me.sId
                   },
                   success: function(data) {
                       confirm.hide();
                       if (data.Result.StatusCode == 0) {
                           me.trigger('refresh', 'del');
                           me.hide();
                           util.remind('1', $t("删除成功"));
                           return;
                       }
                       util.alert(data.Result.FailureMessage);
                   }
                }, {
                    submitSelector: $('.b-g-btn', confirm.element),
                    errorAlertModel: 1
                });
            });
        },

        // 编辑详情
        editHandle: function() {
            var me = this,
                edit = new AddEdite();
            me.hide();
            edit.on('refresh', function(type) {
                if (type != 'del') {
                    me.show(me.sId);
                }
                me.trigger('refresh', type);
            });
            edit.show(me.curSaleData);
        },

        // 下拉事件
        selectHandle: function(e) {
            var $target = $(e.currentTarget),
                $parent = $target.parent();
            $parent.toggleClass('page-selected');
            $('.page-selected', this.$el).each(function(i, item) {
                if ($(item)[0] != $parent[0]) {
                    $(item).removeClass('page-selected');
                }
            });
            e.stopPropagation();
            return false;
        },

        //下拉选择事件
        selectedHandle: function(e) {
            var $target = $(e.currentTarget),
                $select = $target.closest('.page-select');
            $select.removeClass('page-selected');
            $('.text', $select).html($target.html());
            e.stopPropagation();
            return false;
        },


        // 切换详情导航
        navHandle: function(e) {
            var $target = $(e.currentTarget),
                index = $target.index();
            $target.addClass('cur').siblings().removeClass('cur');
            $('.detail-con .info-item', this.$el).eq(index).show().siblings().hide();
            $('.step-type-tab', this.$el).toggle( index == 0 );
            if( index == 0 ){
                $('.step-type-tab span:eq(0)',this.$el).click();
            }else{
                $('.detail-con', this.$el).removeClass('sale-nodata');
            }
        },

        // 根据id获取详情数据
        getDetailDataById: function(callBack) {
            var me = this;
            me.detailAjax && me.detailAjax.abort();
            me.detailAjax = util.FHHApi({
                url: '/EM1HCRM/SaleActionSetting/GetSaleActionByID',
                data: {
                    SaleActionID: me.sId
                },
                success: function(data) {
                    if (data.Result.StatusCode == 0) {
                        callBack && callBack(data.Value);
                        return;
                    }
                    me.setDetailCon(data.Result.FailureMessage);
                },
                complete: function() {
                     me.detailAjax = null;
                }
            },{
                errorAlertModel: 1
            });
        },

        // 渲染详情
        renderDetail: function() {
            var me = this,
                $box =  $('.slide-con', me.$el);
            me.getDetailDataById(function(data) {
               var len = data.SaleAction.Stages.length || 2,
                   preStages = [],
                   nextStages = [],
                   marginLeft =  ($box.width() - 42 - len * 27) / (len - 1);  // 阶段导航布局
               data.moment       = me.moment;
               data.getNameByIds = util.getNameByIds;
               data.marginLeft   = marginLeft + 'px';
               data.suffixes = ['', '', $t("这是一个单行文本字段"),
                                $t("这是一个多行文本字段"), '8888',
                                '8888.00', '8,888.88', 'YYYY-MM-DD'];
               me.curSaleData = data.SaleAction;
               _.each(data.SaleAction.Stages || [], function(item) {
                   if (item.Type == 1) {
                       preStages.push(item);
                   } else {
                       nextStages.push(item);
                   }
               });
               data.nextStages = me._parseRelateData(nextStages);
               data.preStages = me._parseRelateData(preStages);

               $box.html(tpl(data));
               me.renderBanner(preStages, nextStages);
               me.renderCascade(data);
            });
        },
        //格式化客户商机关联字段
        _parseRelateData:function(data){
            var me=this;
            _.each(data,function(d){
                var customerObj=JSON.parse(d.CustomerDescribe||'""'),
                    oppoObj=JSON.parse(d.OpportunityDescribe||'""');
                d.CustomerFieldInfos=me._parseRelateHandle(customerObj);
                d.OppoFieldInfos=me._parseRelateHandle(oppoObj);
            })
            return data;

        },
        _parseRelateHandle:function(obj){
            var arr=[];
            for(var i in obj){
                arr.push(obj[i])
            }
            _.each(arr,function(d){
                d=_.extend(d,{
                    FieldType: d.type,
                    FieldCaption:d.label,
                    FieldApiName: d.api_name,
                    FieldName: d.api_name,
                })
            });
            return arr;

        },

        renderBanner : function(preStages, nextStages){
            var me = this;
            require.async('crm-modules/setting/common/saleaction/saleaction',function(sale) {
                // 售前
                if (me.$('.pre-sale-box .steps-nav').length > 0) {
                    me.prebanner && me.prebanner.destroy();
                    me.prebanner = new sale.Banner({
                        width : 520,
                        el : me.$('.pre-sale-box .steps-nav'),
                        maxNum : 5
                    });
                    me.prebanner.on('click',function(data){
                        $('.pre-sale-box .steps-con .step-item', me.$el).eq(data.order-1).show().siblings().hide();
                    });
                    me.prebanner.show(_.map(preStages, function(a,b) {
                        return {
                            active : 2,
                            name : a.Name,
                            order : b+1,
                            stageID : a.SaleActionStageID
                        }
                    }));
                }

                // 售后
                if (me.$('.next-sale-box .steps-nav').length > 0) {
                    me.nextbanner && me.nextbanner.destroy();
                    me.nextbanner = new sale.Banner({
                        width : 520,
                        el : me.$('.next-sale-box .steps-nav'),
                        maxNum : 5
                    });
                    me.nextbanner.on('click',function(data){
                        $('.next-sale-box .steps-con .step-item', me.$el).eq(data.order-1).show().siblings().hide();
                    });
                    me.nextbanner.show(_.map(nextStages, function(a,b) {
                        return {
                            active : 2,
                            name : a.Name,
                            order : b+1,
                            stageID : a.SaleActionStageID
                        }
                    }));
                }
            })
        },

        renderCascade : function(data){
            var obj = {}
            function collect(stage){
                _.each(data.preStages,function(item){
                    _.each(item.CustomerFieldInfos,function(a){
                        a.FieldType == 14 && (obj[a.UserDefinedFieldID] = a.EnumDetails);
                    });
                    _.each(item.OppoFieldInfos,function(a){
                        a.FieldType == 14 && (obj[a.UserDefinedFieldID] = a.EnumDetails);
                    });
                    _.each(item.UDFieldInfos,function(b){
                        b.FieldType == 14 && (obj[b.UserDefinedFieldID] = b.EnumDetails);
                    });
                });
            }
            collect(data.preStages);
            collect(data.nextStages);
            var me = this;
            me.$('.cascade-wrap').each(function(){
                var $el = $(this);
                me._createCascade($el,obj[$el.data('id')]);
            })
        },

        _createCascade : function($el,enums){
            var options = [],
                r1, r2, me = this;

            $el.html('<div class="sel-p"></div><div class="sel-c"></div>');
            _.each(enums, function(a) {
                options.push({
                    value: a.ItemCode,
                    name: a.ItemName,
                    children: a.Children
                });
            });
            r1 = new Select({
                $wrap: $el.find('.sel-p'),
                appendBody: false,
                zIndex: 2000,
                options: [{
                    name: $t("请选择"),
                    value: '---'
                }].concat(options || []),
                disabled: true,
                defaultValue: '---'
            });
            r2 = new Select({
                $wrap: $el.find('.sel-c'),
                appendBody: false,
                zIndex: 2000,
                disabled: true,
                options: [{
                    name: $t("请选择"),
                    value: '---'
                }],
                defaultValue: '---'
            });
            r1.on('change', function(v, data) {
                var options = [];
                if (data) {
                    _.each(data.children, function(child) {
                        options.push({
                            value: child.ItemCode,
                            name: _.escape(child.ItemName)
                        });
                    });
                    r2.resetOptions([].concat(options));
                }
            });
            (this.cascadeList || (this.cascadeList = [])).push(r1,r2);
        },

        // 格式化时间
        moment: function(value) {
            return FS.moment.unix(value / 1000,true).format('YYYY-MM-DD HH:mm');
        },

        // 显示
        show: function(id) {
            if (id) {
                this.sId = id;
                this.renderDetail();
            }
            Slide.prototype.hasShowDetail = false;
            Slide.prototype.show.call(this);
        },
        // 显示
        hide: function() {
            Slide.prototype.hide.call(this);
        },

        destroy: function() {
            // _.each(this.cascadeList,function(item){
                // item && item.destroy();
                // item = null;
            // });
            // this.cascadeList = null;
            this.prebanner && (this.prebanner.destroy(),this.prebanner = null);
            this.nextbanner && (this.nextbanner.destroy(),this.nextbanner = null);
            Slide.prototype.destroy.apply(this, arguments);
        }
    });

    module.exports = Detail;
});
