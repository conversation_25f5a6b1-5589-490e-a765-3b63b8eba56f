<div class="crm-d-detail">
    <div class="detail-tit">
        <h2>{{{{-SaleAction.Name}}}}</h2>
        ## if (SaleAction.Status == 1) {##
        <div class="detail-btns">
            <span class="crm-ico-del j-del">{{$t("删除")}}</span>
            <span class="crm-ico-edite j-edite">{{$t("编辑")}}</span>
        </div>
        ## } ##
        <div class="detail-status">
            <span>{{$t("状态")}}：{{[$t("使用中"), $t("已停用")][SaleAction.Status - 1]}}</span>
        </div>
    </div>

    <div class="detail-nav">
        <span class="cur">{{$t("销售流程详情")}}</span>
        <span>{{$t("基础信息")}}</span>
    </div>

    <div class="step-type-tab">
        <span class="cur" data-type="pre">{{$t("售前流程")}}</span>
        <em class="line">|</em>
        <span data-type="next">{{$t("售后流程")}}</span>
    </div>

    <div class="detail-con ##if(preStages.length==0){##sale-nodata##}##">
        <div class="info-item step-info">
            <div class="pre-sale-box j-step-type-item">
                ## if (preStages.length > 0) { ##
                <div class="steps-nav b-g-clear"></div>
                <div class="steps-con">
                    ## _.each(preStages, function(item, index) { ##
                    ##var number = index+1;##
                    <div class="step-item {{index != 0 ? 'b-g-hide' : ''}}">
                        <div class="step-desc">
                            ##var number_t = $t('第{{number}}阶段', {number: number})##
                            <h2>{{number_t}}：{{{{-item.Name}}}} <span class="may">{{$t("赢率")}}{{item.Weight ? item.Weight + '%' : '--'}}</span></h2>
                            <h3>{{$t("阶段要求")}}</h3>
                            <p>{{-item.Description || $t("无")}}</p>
                            ## if (item.IsTimeoutRemind || item.IsLeaderConfirm || item.IsFinishByContacts) { ##
                                <h3>{{$t("规则")}}</h3>
                                <p style="line-height: 24px;">## if (item.IsTimeoutRemind) { ##
                                ##var RemainDays_t = $t('本阶段停留时间超过{{RemainDays}}天时提醒销售人员', {RemainDays: item.RemainDays})##
                                {{RemainDays_t}}
                                <br/>
                                ## } ##
                                ## if (item.IsLeaderConfirm) { ##{{$t("本阶段完成需直属上级确认")}}<br/>
                                ## } ##
                                ## if (item.IsFinishByContacts) { ##
                                ##var ContactCount_t = $t("客户中存在{{ContactCount}}个联系人时方可完成本阶段", {ContactCount: item.ContactCount});##
                                {{ContactCount_t}}
                                ## } ##</p>
                            ## } ##
                        </div>
                        <h3 class="detail-sec-tit"><span>{{$t("阶段反馈")}}</span></h3>
                        <div class="step-field">
                            ## if (item.UDFieldInfos.length > 0) { ##
                            ## _.each(item.UDFieldInfos, function(fItem) { ##
                                <div class="detail-item">
                                    <span class="item-tit"><em>{{fItem.IsNotNull ? '*' : ''}}</em> {{{{-fItem.FieldCaption}}}}</span>
                                    <div class="item-con">
                                        ## if (fItem.FieldType == 3) { ##
                                            <textarea class="b-g-ipt b-g-ipt-disabled" disabled="disabled">{{suffixes[fItem.FieldType]}}</textarea>
                                        ## } else if (fItem.FieldType == 8) { ##
                                            <div class="page-select">
                                                <span><em class="text">{{$t("请选择")}}</em><i></i></span>
                                                <ul>
                                                    <li>{{$t("请选择")}}</span></li>
                                                    ## _.each(fItem.EnumDetails, function(eItem) { ##
                                                        <li>{{{{-eItem.ItemName}}}}</span></li>
                                                    ## }) ##
                                                </ul>
                                            </div>
                                        ## } else if (fItem.FieldType == 9) { ##
                                            <div class="mn-checkbox-box">
                                                ## _.each(fItem.EnumDetails, function(eItem) { ##
                                                    <p><span class="mn-checkbox-item"></span><span class="check-label">{{{{-eItem.ItemName}}}}</span></p>
                                                ## }) ##
                                            </div>
                                        ## } else if (fItem.FieldType == 10) { ##
                                            <div class="upload-img-box">
                                                <div class="img-place"></div>
                                                <span class="up-text">{{$t("上传图片")}}</span>
                                                <span>{{$t("支持jpggifpng格式的图片")}}</span>
                                            </div>
                                        ## } else if(fItem.FieldType == 14){ ##
                                             <div data-id="{{fItem.UserDefinedFieldID}}" class="cascade-wrap"></div>
                                        ## } else if(fItem.FieldType == 17){ ##
                                             <p>+{{$t("添加附件")}}</p>
                                        ## } else { ##
                                            <input tpye="text" class="b-g-ipt b-g-ipt-disabled" value="{{suffixes[fItem.FieldType] || ''}}" disabled="disabled"/>
                                        ## } ##
                                    </div>
                                </div>
                            ## }) ##
                            ## } else { ##
                                <div class="no-data">{{$t("暂未设置任何阶段反馈")}}</div>
                            ## } ##
                        </div>
                        <h3 class="detail-sec-tit"><span>{{$t("关联客户字段")}}</span></h3>
                        <div class="step-field">
                            ## if (item.CustomerFieldInfos.length > 0) { ##
                            ## _.each(item.CustomerFieldInfos, function(fItem) { ##
                                <div class="detail-item">
                                    <span class="item-tit"><em>{{fItem.IsNotNull ? '*' : ''}}</em> {{{{-fItem.FieldCaption}}}}</span>
                                    <div class="item-con">
                                        ## if (fItem.FieldType == 'long_text') { ##
                                            <textarea class="b-g-ipt b-g-ipt-disabled" disabled="disabled">{{suffixes[fItem.FieldType]}}</textarea>
                                        ## } else if (fItem.FieldType == 'select_one') { ##
                                            <div class="page-select">
                                                <span><em class="text">{{$t("请选择")}}</em><i></i></span>
                                                <ul>
                                                    <li>{{$t("请选择")}}</span></li>
                                                    ## _.each(fItem.EnumDetails, function(eItem) { ##
                                                        <li>{{{{-eItem.ItemName}}}}</span></li>
                                                    ## }) ##
                                                </ul>
                                            </div>
                                        ## } else if (fItem.FieldType == 'select_many') { ##
                                            <div class="mn-checkbox-box">
                                                ## _.each(fItem.options, function(eItem) { ##
                                                    <p><span class="mn-checkbox-item"></span><span class="check-label">{{{{-eItem.label}}}}</span></p>
                                                ## }) ##
                                            </div>
                                        ## } else if (fItem.FieldType == 'image') { ##
                                            <div class="upload-img-box">
                                                <div class="img-place"></div>
                                                <span class="up-text">{{$t("上传图片")}}</span>
                                                <span>{{$t("支持jpggifpng格式的图片")}}</span>
                                            </div>
                                        ## } else if(fItem.FieldType == 'multi_level_select_one'){ ##
                                            <div data-id="{{fItem.UserDefinedFieldID}}" class="cascade-wrap"></div>
                                        ## } else if(fItem.FieldType == 'file_attachment'){ ##
                                             <p>+{{$t("添加附件")}}</p>
                                        ## } else { ##
                                            <input tpye="text" class="b-g-ipt b-g-ipt-disabled" value="{{suffixes[fItem.FieldType] || ''}}" disabled="disabled"/>
                                        ## } ##
                                    </div>
                                </div>
                            ## }) ##
                            ## } else { ##
                                <div class="no-data">{{$t("暂未设置任何关联客户字段")}}</div>
                            ## } ##
                        </div>

                        <h3 class="detail-sec-tit"><span>{{$t("关联商机字段")}}</span></h3>
                        <div class="step-field">
                            ## if (item.OppoFieldInfos.length > 0) { ##
                            ## _.each(item.OppoFieldInfos, function(fItem) { ##
                                <div class="detail-item">
                                    <span class="item-tit"><em>{{fItem.IsNotNull ? '*' : ''}}</em> {{{{-fItem.FieldCaption}}}}</span>
                                    <div class="item-con">
                                        ## if (fItem.FieldType == 'long_text') { ##
                                            <textarea class="b-g-ipt b-g-ipt-disabled" disabled="disabled">{{suffixes[fItem.FieldType]}}</textarea>
                                        ## } else if (fItem.FieldType == 'select_one') { ##
                                            <div class="page-select">
                                                <span><em class="text">{{$t("请选择")}}</em><i></i></span>
                                                <ul>
                                                    <li>{{$t("请选择")}}</span></li>
                                                    ## _.each(fItem.EnumDetails, function(eItem) { ##
                                                        <li>{{{{-eItem.ItemName}}}}</span></li>
                                                    ## }) ##
                                                </ul>
                                            </div>
                                        ## } else if (fItem.FieldType == 'select_many') { ##
                                            <div class="mn-checkbox-box">
                                                ## _.each(fItem.options, function(eItem) { ##
                                                    <p><span class="mn-checkbox-item"></span><span class="check-label">{{{{-eItem.label}}}}</span></p>
                                                ## }) ##
                                            </div>
                                        ## } else if (fItem.FieldType == 'image') { ##
                                            <div class="upload-img-box">
                                                <div class="img-place"></div>
                                                <span class="up-text">{{$t("上传图片")}}</span>
                                                <span>{{$t("支持jpggifpng格式的图片")}}</span>
                                            </div>
                                        ## } else if(fItem.FieldType == 'multi_level_select_one'){ ##
                                            <div data-id="{{fItem.UserDefinedFieldID}}" class="cascade-wrap"></div>
                                        ## } else if(fItem.FieldType == 'file_attachment'){ ##
                                             <p>+{{$t("添加附件")}}</p>
                                        ## } else { ##
                                            <input tpye="text" class="b-g-ipt b-g-ipt-disabled" value="{{suffixes[fItem.FieldType] || ''}}" disabled="disabled"/>
                                        ## } ##
                                    </div>
                                </div>
                            ## }) ##
                            ## } else { ##
                                <div class="no-data">{{$t("暂未设置任何关联商机字段")}}</div>
                            ## } ##
                        </div>

                    </div>
                    ## }) ##
                </div>
                ## } else { ##
                <div class="no-data">{{$t("暂未设置售前流程")}}</div>
                ## } ##
            </div>

            <!--售后阶段-->
            <div class="next-sale-box j-step-type-item b-g-hide">
                ## if (nextStages.length > 0) { ##
                <div class="steps-nav b-g-clear"></div>
                <div class="steps-con">
                    ## _.each(nextStages, function(item, index) { ##
                    ##var number = index+1 ##
                    <div class="step-item {{index != 0 ? 'b-g-hide' : ''}}">
                        <div class="step-desc">
                            ##var number_t = $t('第{{number}}阶段', {number: number})##
                            <h2>{{number_t}}：{{{{-item.Name}}}}</h2>
                            <h3>{{$t("阶段要求")}}</h3>
                            <p>{{-item.Description || $t("无")}}</p>
                            ## if (item.AsIsThatDayRemind || item.AsIsTimeoutRemind) { ##
                                <h3>{{$t("规则")}}</h3>
                                ## if (item.AsIsThatDayRemind) { ##{{$t("本阶段停留")}}{{item.AsThatRemainDays}}{{$t("天时提醒售后人员执行")}}<br/>
                                ## } ##
                                ## if (item.AsIsTimeoutRemind) { ##
                                ##var RemainDays_t = $t('本阶段停留时间超过{{RemainDays}}天时提醒销售人员', {RemainDays: item.AsRemainDays})##
                                {{RemainDays_t}}
                                ## } ##</p>
                            ## } ##
                        </div>
                        <h3 class="detail-sec-tit"><span>{{$t("阶段反馈")}}</span></h3>
                        <div class="step-field">
                            ## if (item.UDFieldInfos.length > 0) { ##
                            ## _.each(item.UDFieldInfos, function(fItem) { ##
                                <div class="detail-item">
                                    <span class="item-tit"><em>{{fItem.IsNotNull ? '*' : ''}}</em> {{{{-fItem.FieldCaption}}}}</span>
                                    <div class="item-con">
                                        ## if (fItem.FieldType == 3) { ##
                                            <textarea class="b-g-ipt b-g-ipt-disabled" disabled="disabled">{{suffixes[fItem.FieldType]}}</textarea>
                                        ## } else if (fItem.FieldType == 8) { ##
                                            <div class="page-select">
                                                <span><em class="text">{{$t("请选择")}}</em><i></i></span>
                                                <ul>
                                                    <li>{{$t("请选择")}}</span></li>
                                                    ## _.each(fItem.EnumDetails, function(eItem) { ##
                                                        <li>{{{{-eItem.ItemName}}}}</span></li>
                                                    ## }) ##
                                                </ul>
                                            </div>
                                        ## } else if (fItem.FieldType == 9) { ##
                                            <div class="mn-checkbox-box">
                                                ## _.each(fItem.EnumDetails, function(eItem) { ##
                                                    <p><span class="mn-checkbox-item"></span><span class="check-label">{{{{-eItem.ItemName}}}}</span></p>
                                                ## }) ##
                                            </div>
                                        ## } else if (fItem.FieldType == 10) { ##
                                            <div class="upload-img-box">
                                                <div class="img-place"></div>
                                                <span class="up-text">{{$t("上传图片")}}</span>
                                                <span>{{$t("支持jpggifpng格式的图片")}}</span>
                                            </div>
                                        ## } else if(fItem.FieldType == 14){ ##
                                             <div data-id="{{fItem.UserDefinedFieldID}}" class="cascade-wrap"></div>
                                        ## } else if(fItem.FieldType == 17){ ##
                                             <p>+{{$t("添加附件")}}</p>
                                        ## } else if(fItem.FieldType == 18){ ##
                                            <input tpye="text" class="b-g-ipt b-g-ipt-disabled" value='{{$t("这是一个电话号码")}}' disabled="disabled"/>
                                        ## } else if(fItem.FieldType == 19){ ##
                                            <input tpye="text" class="b-g-ipt b-g-ipt-disabled" value='{{$t("这是一个邮箱地址")}}' disabled="disabled"/>
                                        ## } else { ##
                                            <input tpye="text" class="b-g-ipt b-g-ipt-disabled" value="{{suffixes[fItem.FieldType] || ''}}" disabled="disabled"/>
                                        ## } ##
                                    </div>
                                </div>
                            ## }) ##
                            ## } else { ##
                                <div class="no-data">{{$t("暂未设置任何阶段反馈")}}</div>
                            ## } ##
                        </div>
                        <h3 class="detail-sec-tit"><span>{{$t("关联客户字段")}}</span></h3>
                        <div class="step-field">
                            ## if (item.CustomerFieldInfos.length > 0) { ##
                            ## _.each(item.CustomerFieldInfos, function(fItem) { ##
                                <div class="detail-item">
                                    <span class="item-tit"><em>{{fItem.IsNotNull ? '*' : ''}}</em> {{{{-fItem.FieldCaption}}}}</span>
                                    <div class="item-con">
                                        ## if (fItem.FieldType == 2 || fItem.FieldType == 4 || fItem.FieldType == 14 || fItem.FieldType == 11 || fItem.FieldType== 5 || fItem.FieldType == 6 | fItem.FieldType == 7) { ##
                                            <input tpye="text" class="b-g-ipt b-g-ipt-disabled" value="{{suffixes[fItem.FieldType]}}" disabled="disabled"/>
                                        ## } else if (fItem.FieldType == 3) { ##
                                            <textarea class="b-g-ipt b-g-ipt-disabled" disabled="disabled">{{suffixes[fItem.FieldType]}}</textarea>
                                        ## } else if (fItem.FieldType == 8) { ##
                                            <div class="page-select">
                                                <span><em class="text">{{$t("请选择")}}</em><i></i></span>
                                                <ul>
                                                    <li>{{$t("请选择")}}</span></li>
                                                    ## _.each(fItem.EnumDetails, function(eItem) { ##
                                                        <li>{{{{-eItem.ItemName}}}}</span></li>
                                                    ## }) ##
                                                </ul>
                                            </div>
                                        ## } else if (fItem.FieldType == 9) { ##
                                            <div class="mn-checkbox-box">
                                                ## _.each(fItem.EnumDetails, function(eItem) { ##
                                                    <p><span class="mn-checkbox-item"></span><span class="check-label">{{{{-eItem.ItemName}}}}</span></p>
                                                ## }) ##
                                            </div>
                                        ## } else if (fItem.FieldType == 10) { ##
                                            <div class="upload-img-box">
                                                <div class="img-place"></div>
                                                <span class="up-text">{{$t("上传图片")}}</span>
                                                <span>{{$t("支持jpggifpng格式的图片")}}</span>
                                            </div>
                                        ## } else if(fItem.FieldType == 14){ ##
                                             <div data-id="{{fItem.UserDefinedFieldID}}" class="cascade-wrap"></div>
                                        ## } else if(fItem.FieldType == 17){ ##
                                             <p>+{{$t("添加附件")}}</p>
                                        ## } else if(fItem.FieldType == 18){ ##
                                            <input tpye="text" class="b-g-ipt b-g-ipt-disabled" value='{{$t("这是一个电话号码")}}' disabled="disabled"/>
                                        ## } else if(fItem.FieldType == 19){ ##
                                            <input tpye="text" class="b-g-ipt b-g-ipt-disabled" value='{{$t("这是一个邮箱地址")}}' disabled="disabled"/>
                                        ## } else { ##
                                        ## } ##
                                    </div>
                                </div>
                            ## }) ##
                            ## } else { ##
                                <div class="no-data">{{$t("暂未设置任何关联客户字段")}}</div>
                            ## } ##
                        </div>
                        <h3 class="detail-sec-tit"><span>{{$t("关联商机字段")}}</span></h3>
                        <div class="step-field">
                            ## if (item.OppoFieldInfos.length > 0) { ##
                            ## _.each(item.OppoFieldInfos, function(fItem) { ##
                                <div class="detail-item">
                                    <span class="item-tit"><em>{{fItem.IsNotNull ? '*' : ''}}</em> {{{{-fItem.FieldCaption}}}}</span>
                                    <div class="item-con">
                                        ## if (fItem.FieldType == 3) { ##
                                            <textarea class="b-g-ipt b-g-ipt-disabled" disabled="disabled">{{suffixes[fItem.FieldType]}}</textarea>
                                        ## } else if (fItem.FieldType == 8) { ##
                                            <div class="page-select">
                                                <span><em class="text">{{$t("请选择")}}</em><i></i></span>
                                                <ul>
                                                    <li>{{$t("请选择")}}</span></li>
                                                    ## _.each(fItem.EnumDetails, function(eItem) { ##
                                                        <li>{{{{-eItem.ItemName}}}}</span></li>
                                                    ## }) ##
                                                </ul>
                                            </div>
                                        ## } else if (fItem.FieldType == 9) { ##
                                            <div class="mn-checkbox-box">
                                                ## _.each(fItem.EnumDetails, function(eItem) { ##
                                                    <p><span class="mn-checkbox-item"></span><span class="check-label">{{{{-eItem.ItemName}}}}</span></p>
                                                ## }) ##
                                            </div>
                                        ## } else if (fItem.FieldType == 10) { ##
                                            <div class="upload-img-box">
                                                <div class="img-place"></div>
                                                <span class="up-text">{{$t("上传图片")}}</span>
                                                <span>{{$t("支持jpggifpng格式的图片")}}</span>
                                            </div>
                                        ## } else if(fItem.FieldType == 14){ ##
                                            <div data-id="{{fItem.UserDefinedFieldID}}" class="cascade-wrap"></div>
                                        ## } else if(fItem.FieldType == 17){ ##
                                             <p>+{{$t("添加附件")}}</p>
                                        ## } else { ##
                                            <input tpye="text" class="b-g-ipt b-g-ipt-disabled" value="{{suffixes[fItem.FieldType] || ''}}" disabled="disabled"/>
                                        ## } ##
                                    </div>
                                </div>
                            ## }) ##
                            ## } else { ##
                                <div class="no-data">{{$t("暂未设置任何关联商机字段")}}</div>
                            ## } ##
                        </div>
                    </div>
                    ## }) ##
                </div>
                ## } else { ##
                    <div class="no-data">{{$t("暂未设置售后流程")}}</div>
                ## } ##
            </div>

        </div>

        <!--基础信息-->
        <div class="info-item base-info b-g-hide">
            <div class="detail-item">
                <span class="item-tit">{{$t("创建时间")}}</span>
                <div class="item-con">{{moment(SaleAction.CreateTime)}}</div>
            </div>
            <div class="detail-item">
                <span class="item-tit">{{$t("应用部门")}}</span>
                <div class="item-con">{{getNameByIds(SaleAction.CircleIDs, 'g') || '--'}}</div>
            </div>
            <div class="detail-item">
                <span class="item-tit">{{$t("必须新建销售订单方可赢单")}}</span>
                <div class="item-con">{{SaleAction.IsAllowTradeIfWin ? $t("是") : $t("否")}}</div>
            </div>
			<div class="detail-item">
				<span class="item-tit">{{$t("赢单时校验之前所有阶段必填项")}}</span>
				<div class="item-con">{{SaleAction.CheckFieldIfWin ? $t("是") : $t("否")}}</div>
			</div>
            ## if (SaleAction.IsAllowTrade) {##
            <div class="detail-item">
                <span class="item-tit">{{$t("完成售前第几阶段方可新建订单")}}</span>
                ##var SaleAction_t = $t('第{{AllowTradeStageOrder}}阶段', {AllowTradeStageOrder: SaleAction.AllowTradeStageOrder})##
                <div class="item-con">{{SaleAction_t}}</div>
            </div>
            ## } ##
        </div>
    </div>
</div>
