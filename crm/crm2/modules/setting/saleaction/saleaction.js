/**
 * @desc 商机销售流程
 * author qigb
 * @LastEdit: wwf 2023-03-30
 */
define(function(require, exports, module) {
    var util = require('crm-modules/common/util'),
        AddEdite = require('./addedite/addedite'),
        migrationUtils = require('./utils/migrationdatatransfer'),
        List = require('crm-modules/page/list/list');
        // Detail = require('./detail/detail'),
        // Datatable = require('crm-modules/common/datatable/datatable');

    var Saleaction = Backbone.View.extend({

        initialize: function(opts) {
            var me = this;
            me.setElement(opts.wrapper);
            // me.options = _.extend({}, opts, {modClassName: "crm-page crm-p-saleactionnewobj"});
            // me.setElement(this.setWrapper());
            me.initTable();
        },

        // setWrapper: function () {
        //     return this.options.wrapper.html(`<div class="crm-module-top"></div><div class="j-cs-object-list-container crm-module-wrap crm-module-content crm-scroll ${this.options.modClassName}"></div>`);
        // },

        events: {
            // 'click .j-add': 'addHandle'
        },

        

        // 销售流程列表
        initTable: function() {
            var me = this;
            if (me.dt) return;
            // 服务端接口迁移改为使用标准列表——2023.3
            // let $wrapper = this.$('.crm-module-content');
            let Table = List.extend({
                __ChangeStatusHandle: function() {
                    me.setNewStatusMigration.apply(me, arguments);  // 启用和停用
                },
                __EditHandle: function() {
                    me.editHandleMigration.apply(me, arguments);  // 编辑
                },
                addHandle: function() {
                    me.addHandleMigration.apply(me, arguments);  // 新建
                },
                initComplete: function() {
                    me.$('.last-target-item').after('<a style="float:right; margin-left: 16px" href="http://www.fxiaoke.com/mob/guide/crmdoc/src/7-4-1销售流程设置.html" target="_blank">使用说明</a>');
                },
                parseTerm: function() {
					var term = List.prototype.parseTerm.apply(this, arguments);
                    term.showManage = false;  // 场景列表不显示新建和管理场景, 改掉objectTable
                    return term
                }
			});
            me.dt = new Table({
                // wrapper: $wrapper,
                wrapper: me.$el,
                apiname: 'saleactionnew',
                tableOptions: {
                    showMultiple: false, // 表格不显示选择列
                    forbidSernialnumber: true, // 表格不显示序号列
                    hideIconSet: true   // 屏蔽字段设置icon
                }
            });
            me.dt.render && me.dt.render(["SaleActionNewObj"]);
            
        },
       
        // 编辑迁移
        editHandleMigration: function(e) {
            var me = this,
                $target = $(e.target),
                action = $target.data('action'),
                idx = $target.closest(".tr").data("index");
                allData = me.dt.getCurData().data;
            if (action && allData?.[idx]) {
                const editItemData = allData?.[idx]
                me.fetchWebDetailDataMigration(editItemData, function(editData) {
                    var edit = new AddEdite();
                    edit.on('refresh', function(type) {
                        if (type != 'out') {
                            me.dt.refresh();
                        }
                        me._edit = null;
                    });
                    edit.show(editData);
                    me._edit = edit;
                })
            }
            
        },

        // 编辑获取详情新接口数据并进行自动格式化映射
        fetchWebDetailDataMigration: function (editItemData, callback) {
            var me = this,
                editSaleActionData = {
                    Stages: []
                }
            // 获取object_data数据
            Promise.all([me.fetchWebDetailMigration(editItemData), me.fetchObjDescribeDataMigration('AccountObj'),me.fetchObjDescribeDataMigration('OpportunityObj')]).then((allData)=>{
                // 映射新旧接口参数
                // 映射新旧接口参数
                migrationUtils.handleObjectdataMapping(editItemData, editSaleActionData, allData[0].Value.data);
                migrationUtils.handleStagesdataMapping(editSaleActionData, allData[0].Value.data.stage, allData);
                console.log(editSaleActionData, 'editSaleActionData')
                callback && callback(editSaleActionData)
                return;
            })
        },

        // 启用、停用参数format
        setNewStatusMigration: function(e) {
            let me = this,
                param = {},
                $target = $(e.target),
                action = $target.data('action'),
                idx = $target.closest(".tr").data("index");
                allData = me.dt.getCurData().data;
            if (action && allData?.[idx]) {
                const data = allData?.[idx]
                param = {
                    sale_action_id: data['_id'],
                    status: data['status'] == '1' ? '2' : '1'
                }
                me.fetchChangeStatusMigration(param);
            }
        },

        // 启用、停用接口调用
        fetchChangeStatusMigration: function(param) {
            var me = this;
            util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/SaleActionNewObj/action/ChangeStatus',
                data: param,
                success(res) {
                    if(res.Result.StatusCode === 0) {
                        if (param.status == 1) {
                                util.remind($t("启用成功！"));
                                me.dt.refresh()
                        } else {
                            util.remind($t("停用成功")+'！');
                            me.dt.refresh()
                        }
                        return;
                    }
                    util.alert(res.Result.FailureMessage);
                }
            }, {
                errorAlertModel: 1
            })
        },     

        // 添加销售流程处理事件
        addHandleMigration: function(e) {
            var me = this,
                add = new AddEdite();
            add.on('refresh', function(type) {
                if (type != 'out') {
                    me.dt.refresh();
                }
                me._add = null;
            });
            add.show();
            me._add = add;
        },

        // 获取编辑和详情数据
        fetchWebDetailMigration: function(editItemData) {
            var me = this;
			return new Promise((resolve) => {
				util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/SaleActionNewObj/controller/WebDetail',
                    data: {
                        "layoutVersion":          "V3",
                        "objectDataId":           editItemData._id,
                        "objectDescribeApiName":  "SaleActionNewObj",
                        "fromRecycleBin":         false,
                        "management":             false,
                        "serializeEmpty":         false,
                        "describeVersionMap":     { "SaleActionNewObj":3 }
                    },
					success:(response) => {
						if(response.Result.StatusCode == 0){
							resolve(response);
							return;
						}
                        util.alert(response.Result.FailureMessage);
					}
				})
			})
        },

        // 获取客户和商机describe数据
        fetchObjDescribeDataMigration(api_name){
			var me = this;
			return new Promise((resolve) => {
				util.FHHApi({
					url: '/EM1HNCRM/API/v1/object/describe/service/findDescribeByApiName',
					type: "post",
					data: {
						describe_apiname: api_name,
						include_layout: false,
						include_related_list: false,
						get_label_direct: true
					},
					success:(response) => {
						if(response.Result.FailureCode === 0){
							resolve(response.Value.objectDescribe.fields);
							return;
						}
                        util.alert(response.Result.FailureMessage);
					}
				})
			})
		},

        //销毁
        destroy: function() {
            var me = this;
            _.each(['dt', 'detail', '_add', '_edit'], function(item) {
                me[item] && me[item].destroy();
            });
        }

        // me.dt = new Datatable({
        //     $el: me.$el,
        //     url: '/EM1HCRM/SaleActionSetting/GetSaleActionList',
        //     tableName: 'saleaction',
        //     showMultiple: false,
        //     showFilerBtn: true,
        //     showMoreBtn: false,
        //     showManage: false,
        //     showCustom: false,
        //     title: $t("商机销售流程"),
        //     search: {
        //         placeHolder: $t("搜索流程名称"),
        //         fieldName:   'Name'
        //     },
        //     searchTerm: {
        //         pos: 'C'
        //     },
        //     customColumns: [{
        //         data: 'Name',
        //         title: $t("销售流程名称"),
        //         width: 210,
        //         fixed: true,
        //         render: function(data) {
        //             return '<a href="javascript:;">' + data + '</a>';
        //         }
        //     }],
        //     moreCustomColumns: [{
        //         data:     null,
        //         width:    110,
        //         title:    $t("操作"),
        //         lastFixed: true,
        //         lastFixedIndex: 2,
        //         render: function(data, type, full) {
        //             if (full.Status == 1) {
        //                 return '<a href="javascript:;" class="edit-btn" style="margin-right: 10px;">'+ $t("编辑")+'</a><a href="javascript:;" class="set-status" data-type="2">'+ $t("停用")+'</a>';
        //             } else {
        //                 return '<a href="javascript:;" class="set-status" data-type="1">'+ $t("启用") +'</a>';
        //             }
        //         }
        //     }],
        //     operate: {
        //         pos: 'C',
        //         btns: [{
        //             text: $t("新建"),
        //             className: 'j-add'
        //         }]
        //     },
        //     initComplete: function() {
        //         me.$('.last-target-item').after('<a style="float:right;" href="http://www.fxiaoke.com/mob/guide/crmdoc/src/7-4-1销售流程设置.html" target="_blank">使用说明</a>');
        //     },
        //     formatData: function(data) {
        //         return {
        //             totalCount: data.Page ? data.Page.TotalCount : 0,
        //             data: data.SaleActions
        //         }
        //     }
        // });

        // me.dt.on('trclick', function(data, $tr, $target) {
        //     if ($target.hasClass('edit-btn')) {
        //         me.getDetailDataById(data.SaleActionID, function(data) {
        //             me.editHandle(data.SaleAction);
        //         });
        //     } else if($target.hasClass('set-status')) {
        //         me.setStatus({
        //             SaleActionID: data.SaleActionID,
        //             Name: data.Name,
        //             Status: $target.attr('data-type') || 1
        //         });
        //     } else {
        //         me.showDetail(data.SaleActionID);
        //     }
        // });
        
        /**
         * @desc 设置销售流程名称
         */
        //  setName: function(param) {
        //     var me = this,
        //         tpl = '<p style="color:#ff6600;padding-bottom: 15px;">'+ $t("销售流程名称不能重复请修改销售流程名称后再启用") +'</p>' +
        //               '<div>'+ $t("销售流程名称")+'：<input type="text" value="'+ param.Name +
        //               '" class="b-g-ipt" style="padding: 3px 10px;width: 180px;"/></div>';
        //     var confirm = util.confirm(tpl, $t("修改销售流程名称"), function() {
        //             var name = $.trim($('.b-g-ipt', confirm.element).val());
        //             if (name == '' || name == param.Name) {
        //                 util.alert($t("请修改销售流程名称"));
        //                 return;
        //             }
        //             param.Name = name;
        //             util.FHHApi({
        //                 url: '/EM1HCRM/SaleActionSetting/SetSaleActionName',
        //                 data: param,
        //                 success: function(data) {
        //                     if (data.Result.StatusCode == 0) {
        //                         confirm.hide();
        //                         util.remind($t("名称修改成功"));
        //                         me.dt.setParam({}, true);
        //                     } else {
        //                         util.alert(data.Result.FailureMessage);
        //                     }
        //                 }
        //             }, {
        //                 errorAlertModel: 1,
        //                 submitSelector: $('.b-g-btn', confirm.element)
        //             });
        //     });
        // },

        // 详情
        // showDetail: function(id) {
        //     var me = this;
        //     if (!me.detail) {
        //         me.detail = new Detail({
        //             width: 600,
        //             className: 'crm-s-saleaction'
        //         });
        //         me.detail.on('refresh', function(type) {
        //             if (type != 'out') {
        //                 me.dt.setParam({}, true);
        //             }
        //         });
        //     }
        //     me.detail.show(id);
        // },

        // 根据id获取详情数据
        //  getDetailDataById: function(id, callBack) {
        //     var me = this;
        //     me.detailAjax && me.detailAjax.abort();
        //     me.detailAjax = util.FHHApi({
        //         url: '/EM1HCRM/SaleActionSetting/GetSaleActionByID',
        //         data: {
        //             SaleActionID: id
        //         },
        //         success: function(data) {
        //             if (data.Result.StatusCode == 0) {
        //                 callBack && callBack(data.Value);
        //                 return;
        //             }
        //             util.alert(data.Result.FailureMessage);
        //         },
        //         complete: function() {
        //              me.detailAjax = null;
        //         }
        //     },{
        //         errorAlertModel: 1
        //     });
        // },

        // 编辑
        // editHandle: function(data) {
        //     var me = this,
        //         edit = new AddEdite();
        //     edit.on('refresh', function(type) {
        //         if (type != 'out') {
        //             me.dt.setParam({}, true);
        //         }
        //         me._edit = null;
        //     });
        //     edit.show(data);
        //     me._edit = edit;
        // },

        // // 添加销售流程处理事件
        // addHandle: function(e) {
        //     var me = this,
        //         add = new AddEdite();
        //     add.on('refresh', function(type) {
        //         if (type != 'out') {
        //             me.dt.setParam({}, true);
        //         }
        //         me._add = null;
        //     });
        //     add.show();
        //     me._add = add;
        // },
        
        // // 启用销售流程
        // setStatus: function(param) {
        //     var me = this;
        //     util.FHHApi({
        //         url: '/EM1HCRM/SaleActionSetting/SetSaleActionStatus',
        //         data: param,
        //         success: function(data) {
        //             if (data.Result.StatusCode == 0) {
        //                 if (param.Status == 1) {
        //                     if (data.Value.Result) {
        //                         util.remind($t("启用成功！"));
        //                         me.dt.setParam({}, true);
        //                     } else {
        //                         me.setName({
        //                             SaleActionID: param.SaleActionID,
        //                             Name: param.Name
        //                         });
        //                     }
        //                 } else {
        //                     util.remind($t("停用成功")+'！');
        //                     me.dt.setParam({}, true);
        //                 }
        //             } else {
        //                 util.alert(data.Result.FailureMessage);
        //             }
        //         }
        //     }, {
        //         errorAlertModel: 1
        //     });
        // },
    });
    module.exports = Saleaction;
});
