.crm-s-saleaction {
    // .crm-p-saleactionnewobj{
        // height: 100%;
        .fix-end-b{
            z-index: 10;
            .column-operate{
                .tr-operate-wrap{
                    .tr-operate-btn-item{
                        color: #3487E2;
                    }
                }
            }
        }
    // }
    .slide-con {
        .step-field {
            color: #999;
        }       
        .b-g-ipt-disabled {
            width: 344px;
            color: #ccc;
            background-color: #f5f5f5;
        }
        .check-label {
            display: inline-block;
            vertical-align: middle;
            margin-left: 15px;
            color: #ccc;
        }
        .mn-checkbox-box {line-height: 29px;}
        .mn-checkbox-item {
            width: 13px; height: 13px;
            background: #f5f5f5!important;
            border: 1px solid #ddd!important;
            #css3 > .radius(4px);
        }
        .upload-img-box {
            width: 300px;
            overflow: hidden;
            color: #ccc;
            line-height: 1.5;
            .up-text {
                display: block;
                padding: 8px 0 4px;
            }
        }
        .img-place {
            float: left;
            width: 74px;
            height: 56px;
            margin-right: 10px;
            background: url("@{imgUrl}/field-img.png") no-repeat;
            #css3 > .radius(4px);
        }
        .crm-d-detail .detail-con {
            padding: 20px;
        }         
    }
    
    .page-select {
        width: 224px;
        line-height: 32px;
        position: relative;
        span {
            display: block;
            padding: 0 10px;
            background-color: #f5f5f5;
            color: #ccc;
            border: 1px solid #ddd;
            #css3 > .radius(4px);
            cursor: pointer;
            i {
                position: absolute;
                    right: 10px;top: 12px;
                width: 12px;
                height: 8px;
                background: url(/html/base/assets/images/select/bg.png) no-repeat 0 -10px;
                background-size: 12px 18px;
                overflow: hidden;
                opacity: 0.4;
            }
        }
        ul {
            display: none;
            width: 222px;
            background-color: #eee;
            border: 1px solid #ddd;
            position: absolute;
                top: 32px; left: 0;z-index: 100;
        }
        li {
            padding: 0 10px;
            cursor: pointer;
            color: #ccc;
            &:hover {
                background-color: #e8e8e8;
            }
        }
        &.page-selected {
            ul { display: block;}
        }
    }
    
    .trade-desc {
        color: #999;
    }
    .steps-nav {
        position: relative;       
    }
    .cascade-wrap {
        width: 100%;
        height: 34px;
        position: relative;
    }
    .sel-p, .sel-c {
        float: left;
        width: 200px;
        margin-right: 20px;
    }
    
    .step-nav-item {
        float: left;
        width: 27px;
        height: 27px;
        text-align: center;
        cursor: pointer;    
        span {
            display: block;
            width:  14px;
            height: 14px;
            margin: 6px auto;
            background-color: #ebebeb;
            border: 1px solid #d0d0d0;
            #css3>.radius(30px);
            em {
                display: none;
            }
        }
        &.cur span {
            width: 25px;
            height: 25px;
            margin-top: 0;
            line-height: 25px;
            background-color: #7ed321;
            border-color: #80ce2d;
            color: var(--color-neutrals01);
            em {
                display: block;
                font-weight: bold;
            }
        }
    }

    .step-desc {
        margin-top: 40px;
        h2{
            font-size: 18px;
            .may {
                font-size: 13px;
                padding: 0 10px;
                margin-left: 8px;
                border-left: 1px solid #eee;
            }
        }
        h3{
            font-size: 16px;
            color : #999;
            margin : 30px 0 16px 0;
        }
    }
    
    .steps-con .detail-item {
        line-height: 34px;
        em {
            display: inline-block;
            vertical-align: -2px;
        }
    }
    .detail-nav {
        margin-bottom: 20px!important;
    }
    .step-type-tab {
        width: 100%;
        overflow: hidden;
        font-size: 18px;
        span {
            float: left;
            color: #737a87;
            margin-left: 10px;
            cursor: pointer;
        }
        .line {
            float: left;
            margin: 0 15px;
            color: #737a87;
        }
        .cur {
            color: var(--color-neutrals01);
        }
    }
    
    .detail-con .detail-item {
        padding-left: 0!important;
        padding-bottom: 20px!important;
        .item-tit {
            display: block;
            width: 100%!important;
            padding-bottom: 5px;
            position: relative!important;
        }
    }
    .no-data {
        padding-bottom: 15px;
    }
}


