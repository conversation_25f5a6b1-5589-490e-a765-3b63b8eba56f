/*
 * @Descripttion: 
 * @Author: wang<PERSON><PERSON>
 * @Date: 2023-04-10
 */
/**
 * @desc 接口迁移字段映射的相关函数
 */
 define(function(require, exports, module) {
    const allowEditOptionTypes = ['select_one', 'select_many', 'multi_level_select_one']
    var migrationUtils = {
        /**
        * @desc 自定义字段，单选，多选enumDetails字段映射
        */
         enumDetailsTransfer: function(select_options=[], field_type){
            let result = [];
            select_options.forEach(item => {
                if(field_type == 'select_one' || field_type == 'select_many') {
                    result.push(migrationUtils.enumItemTransfer(item)) 
                } else if(field_type == 'multi_level_select_one') {
                    let data = migrationUtils.enumItemTransfer(item);
                    data.Children = (item.children || []).map(child => migrationUtils.enumItemTransfer(child))
                    result.push(data);
                }
            })
            return result;
        },
        enumItemTransfer: function(item){
            return {
                EnumDetailID:   item.itemOrder,
                isNew:          true,
                IsDeleted:      false,
                IsSysItem:      false,
                ItemCode:       item.itemCode,
                ItemName:       item.itemName
            }
        },
        /**
        * @desc 自定义字段，单选，多选、级联需要设置IsAllowEditOption为true
        */
        checkIsAllowEditOption: function (fieldType) {
            return allowEditOptionTypes.findIndex(item => item == fieldType) != -1;
        },

        // 编辑详情接口-通用字段映射
        handleObjectdataMapping: function (editItemData, editSaleActionData, originData) {
            editSaleActionData.Description           = ''
            editSaleActionData.IsAllowOldData        = true
            editSaleActionData.SaleActionID          = editItemData._id;
            editSaleActionData.Name                  = originData.name;
            editSaleActionData.CircleIDs             = (originData.sale_action_circle_id || []).map(id => Number(id))
            editSaleActionData.Status                = originData.status
            editSaleActionData.CreateTime            = originData.create_time
            editSaleActionData.IsAllowTradeIfWin     = originData.is_allow_trade_if_win
            editSaleActionData.IsAllowTrade          = originData.is_allow_trade
            editSaleActionData.AllowTradeStageOrder  = originData.allow_trade_stage_order
            editSaleActionData.CheckFieldIfWin       = originData.check_field_if_win
        },

        // 编辑详情接口-阶段相关字段映射
        handleStagesdataMapping: function (editSaleActionData, stateArray=[], allFieldData) {
            editSaleActionData.Stages = stateArray.map(stageItem => {
                let accountObjFieldsArray = [], opportunityObjFieldsArray = [], udFieldsArray = [];
                (stageItem.stage_user_field_info || []).forEach(item => {
                    if(item.field_belong === 'AccountObj') {
                        accountObjFieldsArray.push(item)
                    } else if(item.field_belong === 'OpportunityObj') {
                        opportunityObjFieldsArray.push(item)
                    } else {
                        // if(['text', 'long_text', 'number', 'currency', 'date', 'select_one', 'select_many', 'image', 'multi_level_select_one', 'file_attachment', 'phone_number','email'].indexOf(item.field_type) != -1) {
                            udFieldsArray.push(item)
                        // }
                    }
                });
                return {
                    SaleActionStageID:              stageItem._id,
                    Name:                           stageItem.name,
                    Description:                    stageItem.description,
                    IsTimeoutRemind:                stageItem.is_timeout_remind,
                    RemainDays:                     stageItem.remain_days,
                    IsLeaderConfirm:                stageItem.is_leader_confirm,
                    IsFinishByContacts:             stageItem.is_finish_by_contacts,
                    ContactCount:                   stageItem.contact_count,
                    Type:                           stageItem.type,
                    AsIsThatDayRemind:              stageItem.as_is_that_day_remind,
                    AsThatRemainDays:               stageItem.as_that_remain_days,
                    AsIsTimeoutRemind:              stageItem.as_is_timeout_remind,
                    AsRemainDays:                   stageItem.as_remain_days,
                    Weight:                         stageItem.weight,
                    StageFlag:                      stageItem.stage_flag,
                    CustomerFieldInfos:             [],
                    OppoFieldInfos:                 [],
                    UDFieldInfos:                   udFieldsArray.length ? migrationUtils.handleStagesUdFieldInfoMigration(udFieldsArray) : [],
                    CustomerDescribe:               accountObjFieldsArray.length ? migrationUtils.handleStagesDescribeMigration(accountObjFieldsArray, allFieldData[1]) : '',
                    CustomerIsRequiredDescribe:     accountObjFieldsArray.length ? migrationUtils.handleStagesDescribeIsRequiredMigration(accountObjFieldsArray) : '',
                    OpportunityDescribe:            opportunityObjFieldsArray.length ?  migrationUtils.handleStagesDescribeMigration(opportunityObjFieldsArray, allFieldData[2]) : '',
                    OpportunityIsRequiredDescribe:  opportunityObjFieldsArray.length ?  migrationUtils.handleStagesDescribeIsRequiredMigration(opportunityObjFieldsArray) : '',
                }
            })
        },

        // 编辑详情接口-自定义字段编辑数据映射
        handleStagesUdFieldInfoMigration: function (udFieldsArray) {
            return udFieldsArray.map(item => {
                return {
                    FieldName:              item.field_api_name,
                    FieldCaption:           item.field_caption,
                    FieldOrder:             Number(item.field_order),
                    FieldType:              migrationUtils.fieldTypeTransfer(item.field_type, Number(item.decimal_digits)),
                    IsNotNull:              item.is_not_null,
                    IsWatermark:            item.is_watermark,
                    UserDefinedFieldID:     item._id,
                    DecimalDigits:          item.decimal_digits || 0,
                    EnumDetails:            item.select_options?.length ? migrationUtils.enumDetailsTransfer(item.select_options, item.field_type) : [],
                    IsAllowEditOption:      migrationUtils.checkIsAllowEditOption(item.field_type),
                    EnumName:               item?.select_options?.[0]?.enumName || '',
                    AggregateFieldName:     '',
                    AggregateObject:        0,
                    AggregateType:          '',
                    CalFormula:             '',
                    CascadeEnumRelations:   [],
                    CascadeFields:          [],
                    CodeRule:               {},
                    DefaultIsZero:          true,
                    DefaultValue:           '',
                    ExtendProp:             "{\"New_IsVisible\":true,\"New_IsEditable\":true,\"Modify_IsVisible\":true,\"Modify_IsEditable\":true,\"View_IsVisible\":true,\"IsAllowHide\":true}",
                    FieldLength:            100,
                    FieldProperty:          2,
                    Fields:                 '',
                    Formula:                '',
                    IsAllowEditNotNull:     true,
                    IsCalculateFieldHidden: false,
                    IsCodeField:            false,
                    IsSelfClone:            false,
                    IsVisible:              true,
                    ObjectAggregate:        null,
                    ObjectRelation:         null,
                    OwnerType:              7,
                    ReferRule:              '',
                    RelationField:          '',
                    ReturnValueType:        '',
                    UsedIn:                 '',
                    // 以下为服务端下发，前端不需要，但保存时需要全量提交字段     
                    create_time:                            item.create_time,
                    created_by:                             item.created_by,
                    is_deleted:                             item.is_deleted,
                    last_modified_by:                       item.last_modified_by,
                    last_modified_time:                     item.last_modified_time,
                    object_describe_api_name:               item.object_describe_api_name,
                    sale_action_stage_id:                   item.sale_action_stage_id,
                    sale_action_stage_id__relation_ids:     item.sale_action_stage_id,
                    tenant_id:                              item.tenant_id,
                    version:                                item.version,

                }
            })
        },

        // 编辑详情接口-字段描述映射
        handleStagesDescribeMigration: function (fieldsArray, originFieldData=[]) {
            let obj = {}
            fieldsArray.forEach(fieldItem => {
                obj[fieldItem.field_api_name] = originFieldData[fieldItem.field_api_name]
            })
            return JSON.stringify(obj)
        },

        // 编辑详情接口-字段是否必填映射
        handleStagesDescribeIsRequiredMigration: function (fieldsArray) {
            let obj = {}
            fieldsArray.forEach(fieldItem => {
                obj[fieldItem.field_api_name] = !!fieldItem.is_not_null
            })
            return JSON.stringify(obj)
        },
        // 编辑详情接口-字段类型转换
        fieldTypeTransfer: function(strFieldType, DecimalDigits = 0) {
            if(strFieldType == 'text') {
                return 2;
            }else if(strFieldType == 'long_text'){
                return 3;
            }else if(strFieldType == 'number' && DecimalDigits == 0){
                return 4;  // 整数
            }else if(strFieldType == 'number' && DecimalDigits > 0){
                return 5;  // 小数
            }else if(strFieldType == 'currency'){
                return 6;
            }else if(strFieldType == 'date'){
                return 7;
            }else if(strFieldType == 'select_one'){
                return 8;
            }else if(strFieldType == 'select_many'){
                return 9;
            }else if(strFieldType == 'image'){
                return 10;
            }else if(strFieldType == 'multi_level_select_one'){
                return 14;
            }else if(strFieldType == 'file_attachment'){
                return 17;
            }else if(strFieldType == 'phone_number'){
                return 18;
            }else if(strFieldType == 'email'){
                return 19;
            }else{
                return 0;
            }
        },
        // 编辑保存接口-字段类型转换
        submitFieldTypeTransfer: function(strFieldType) {
            switch(strFieldType) {
                case 2:
                    return 'text';
                case 3:
                    return 'long_text';
                case 4:
                    return 'number';  // 整数
                case 5:
                    return 'number';  // 小数
                case 6:
                    return 'currency';
                case 7:
                    return 'date';
                case 8:
                    return 'select_one';
                case 9:
                    return 'select_many';
                case 10:
                    return 'image';
                case 14:
                    return 'multi_level_select_one';
                case 17:
                    return 'file_attachment';
                case 18:
                    return 'phone_number';
                case 19:
                    return 'email';
                // case 'jisuanziduan':
                //     return 21;  // 计算字段不好使，迁移时不在解析
                default:
					return '0';
                    
            }
        },
        /**
         * @desc 新建和编辑销售流程保存接口——阶段相关参数映射
         */
         formatAddStagesDataMigration: function(objectValueMigration, SaleActionStageRequests=[], type) {
            const { objectData, objectDescribe } = objectValueMigration;
            return SaleActionStageRequests.map(stageItemData => {
                let result = {
                    description:               stageItemData.Description,
                    name:                      stageItemData.Name,
                    stage_flag:                stageItemData.StageFlag + '',
                    as_is_that_day_remind:     stageItemData.AsIsThatDayRemind,
                    as_that_remain_days:       stageItemData.AsThatRemainDays,
                    as_is_timeout_remind:      stageItemData.AsIsTimeoutRemind,
                    as_remain_days:            stageItemData.AsRemainDays,
                    is_leader_confirm:         stageItemData.IsLeaderConfirm,
                    is_finish_by_contacts:     stageItemData.IsFinishByContacts,
                    contact_count:             stageItemData.ContactCount,
                    is_timeout_remind:         stageItemData.IsTimeoutRemind,
                    remain_days:               stageItemData.RemainDays,
                    weight:                    stageItemData.Weight,
                    stage_order:               stageItemData.StageOrder,
                    type:                      stageItemData.Type + '',
                    object_describe_api_name:  objectData.object_describe_api_name,
                    record_type:               objectData.record_type,
                    object_describe_id:        objectDescribe._id,
                    lock_rule:                 "default_lock_rule",
                    lock_status:               "0",
                    opportunity_stage_field:   migrationUtils.formatAddFieldMigration((stageItemData.SaleStageFieldRelationRequests || []).filter(item => item.OwnerType == 8), 'OpportunityObj', type), 
                    account_stage_field:       migrationUtils.formatAddFieldMigration((stageItemData.SaleStageFieldRelationRequests || []).filter(item => item.OwnerType == 2), 'AccountObj', type),
                    udef_stage_field:          migrationUtils.formatAddFieldMigration(stageItemData.UserDefinedFieldRequests, 'UdobjObj'),
                }
                if(type == 'Edit') { result._id = stageItemData.SaleActionStageID}
                return result;
            })
        },

        // 新建和编辑销售流程保存接口——添加字段数据格式化映射
        formatAddFieldMigration: function(fieldsArray=[], type, submitType) {
            return fieldsArray.map(fieldsData => {
                let udeFieldParam = {};
                const commonParam = {
                    is_not_null: fieldsData.IsNotNull,
                    field_order: fieldsData.FieldOrder,
                    field_belong: type,
                    field_api_name: fieldsData.FieldApiName,
                }
                if(type == 'UdobjObj') {
                    // 自定义字段没有FieldApiName， 有FieldCaption， 自定义字段只有编辑没有新建，新建已经屏蔽
                    udeFieldParam = {
                        is_watermark:   fieldsData.IsWatermark,
                        field_api_name: fieldsData.FieldName,
                        decimal_digits: fieldsData.DecimalDigits,
                        field_caption:  fieldsData.FieldCaption,
                        field_type:     migrationUtils.submitFieldTypeTransfer(fieldsData.FieldType),
                        // 以下为服务端所需全量提交参数
                        field_order:                            fieldsData.FieldOrder+'',
                        is_deleted:                             fieldsData.is_deleted,
                        create_time:                            fieldsData.create_time,
                        created_by:                             fieldsData.created_by,
                        last_modified_by:                       fieldsData.last_modified_by,
                        last_modified_time:                     fieldsData.last_modified_time,
                        object_describe_api_name:               fieldsData.object_describe_api_name,
                        sale_action_stage_id:                   fieldsData.sale_action_stage_id,
                        sale_action_stage_id__relation_ids:     fieldsData.sale_action_stage_id__relation_ids,
                        tenant_id:                              fieldsData.tenant_id,
                        version:                                Number(fieldsData.version) + 1 + '',
                        _id:                                    fieldsData.UserDefinedFieldID
                    }   
                    if(fieldsData?.EnumDetails?.length) {
                        udeFieldParam.select_options = JSON.stringify(migrationUtils.formatSelectOptionsMigration(fieldsData.EnumDetails))
                    }
                }
                return Object.assign({}, commonParam, udeFieldParam)
            })
        },

        // 自定义字段编辑保存接口-格式映射化select_options参数
        formatSelectOptionsMigration: function(EnumDetails) {
            return EnumDetails.map(item => {
                let result = migrationUtils.enumItemFormat(item);
                if(item?.Children?.length) {
                    result.children = item.Children.map(child => migrationUtils.enumItemFormat(child))
                }
                return result;
            })
        },

        // 自定义字段编辑保存接口-option公共参数
        enumItemFormat: function(item){
            return {
                itemName: item.ItemName,
                itemCode: item.ItemCode+'',
                itemOrder: item.ItemOrder+'',
            }
        },

        // 新建编辑保存接口——全量参数映射
        addAndEditParamMigration:function(objectValueMigration, data, type, addStageOriginData, requestId) {
            const { objectData, objectDescribe } = objectValueMigration;
            let object_data = {
                status:                    objectData.status,    // 销售流程启用/停用状态
                is_default:                false,
                requestId:                 requestId, 
                object_describe_api_name:  objectData.object_describe_api_name,
                object_describe_id:        objectDescribe._id, 
                record_type:               objectData.record_type,
                created_by:                objectData.created_by,  
                data_own_department:       objectData.data_own_department, 
                data_own_department__r:    objectData.data_own_department__r, 
                name:                      data.Name,
                description:               data.Description,
                sale_action_circle:        (data.CircleIDs || []).map(id => Number(id)),
                is_allow_trade_if_win:     data.IsAllowTradeIfWin,
                check_field_if_win:        data.CheckFieldIfWin,
                is_allow_trade:            data.IsAllowTrade,
                allow_trade_stage_order:   data.IsAllowTrade ? data.AllowTradeStageOrder : 0,
            }
            if(type == 'Edit') {
                object_data._id         =  data.SaleActionID;
            }
            return {
                object_data: object_data,
                details: {
                    SaleActionStageNewObj: migrationUtils.formatAddStagesDataMigration(objectValueMigration, addStageOriginData, type) 
                },
                maskFieldApiNames: {
                    SaleActionNewObj: [

                    ]
                },
                seriesId: requestId, 
            };
		},
    }

    module.exports = migrationUtils;
});