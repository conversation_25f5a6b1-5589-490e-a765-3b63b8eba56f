define(function(require, exports, module) {
	var util = require("crm-modules/common/util");

	function ajax(options, callback) {
		return util.FHHApi({
			url: options.url,
			data: options.data,
			success: function(res) {
				if (res.Result.StatusCode === 0) {
					callback && callback(res.Value);
				}
			}
		});
	}

	module.exports = {
		//列表
		list: function(options, callback) {
			return ajax(
				{
					url: "/EM1HSTAGE/Definition/GetDefinitionList",
					data: {
						name: options.name,
						enable: options.enable,
						page: options.page,
						pageSize: options.pageSize
					}
				},
				callback
			);
		},
		// 启用禁用
		enable: function(options, callback) {
			return ajax(
				{
					url: "/EM1HSTAGE/Definition/Enable",
					data: {
						enable: options.enable,
						sourceWorkflowId: options.flowid
					}
				},
				callback
			);
		},
		// 删除
		delete: function(flowid, callback) {
			return ajax(
				{
					url: "/EM1HSTAGE/Definition/Delete",
					data: {
						sourceWorkflowId: flowid
					}
				},
				callback
			);
		},
		//配额信息
		quota: function(callback) {
			return ajax(
				{
					url: "/EM1HSTAGE/Tenant/GetQuota"
				},
				callback
			);
		},
		get: function(id, callback) {
			return ajax(
				{
					url: "/EM1HSTAGE/Definition/GetBySourceWorkflowId",
					data: {
						sourceWorkflowId: id
					}
				},
				callback
			);
		}
	};
});
