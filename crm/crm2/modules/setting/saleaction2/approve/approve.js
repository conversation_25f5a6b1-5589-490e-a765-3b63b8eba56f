/**
 * @description 阶段视图查看设置
 * <AUTHOR>
 */

define(function(require, exports, module) {
    var util = CRM.util,
        Dialog = require('crm-widget/dialog/dialog'),
        settingTpl = require('./approve-html');
    var Setting = Dialog.extend({
        attrs: {
            width: 510,
            title: $t("阶段变更触发审批类型设置"),
            showBtns: true,
            showScroll: false,
			className: 'crm-d-page-config setting-saleaction2'
        },

        events: {
            'click .b-g-btn': 'confirmHandle',
            'click .b-g-btn-cancel': 'cancelHandle',
            'click .config-radio': 'triggerRadio'
        },
        initialize: function() {
            this.attrs = $.extend(true, {}, Setting.prototype.attrs, this.attrs);
            return Setting.superclass.initialize.apply(this, arguments);
        },
        show: function(data) {
            let me = this;
            let result =  Setting.superclass.show.call(this);
            me.getPageConfig();
            return result;
        },
        triggerRadio: function(e) {
            e && e.stopPropagation();
            let me = this;
            let ele = $(e.target).closest('.config-radio');
            if (ele.hasClass('readOnly')) {
                return;
            }
            if (!ele.hasClass('active')) {
                ele.siblings().removeClass('active');
                ele.addClass('active');
                me.changed = !me.changed;
            }
            me.setting = ele.data('value')
        },
        getPageConfig: function() {
            let me = this;
            me.element.find('.dialog-con').html(settingTpl());
            let tooltips = new Vue({
                template:` <fx-tooltip effect="dark" :content="text" placement="top-start">
                            <span class="setting-tooltip">?</span>
                           </fx-tooltip>`,
                data:{
                    text:$t('切换为「编辑审批」时，请先定义对象的编辑触发审批流程。')
                    }
                })
            tooltips.$mount('#fortooltips');
            util.FHHApi({
                url: '/EM1AFLOW/Config/Get',
                data: {
                    flowType: 'stage',
                    types: ['stageChangeApproval'],
                    terminal: 'ALL'
                },
                success: function(res) {
                    if (res.Result.StatusCode == 0) {
                        if (res.Value.values) {
                            me.setting = res.Value.values.stageChangeApproval
                            me.changed = false;
                            me.editables = res.Value.editables && res.Value.editables.stageChangeApproval
                            if(me.editables === false) {
                                $('.config-radio',me.$el).each((index, e) =>{
                                    $(e).data('value') !== me.setting &&  $(e).addClass('readOnly');
                                })    
                            }
                            $('.config-radio',me.$el).each((index, e) =>{
                                $(e).data('value') === me.setting &&  $(e).addClass('active');
                            })
                        }

                    };
                }
            }, {
                errorAlertModel: 1
            })
        },

        hide: function() {
            return Setting.superclass.hide.call(this);
        },

        confirmHandle: function() {
            let me = this;
            if(me.changed){
                FxUI.MessageBox.confirm(me.setting === 'EDIT' ? 
                    $t('flow.stage.changeApproval.confirm_content','切换至「编辑审批」后， 不支持再修改为 「阶段变更审批」，是否确定修改？') 
                    : $t('flow.stage.changeApproval.confirm_content2',
                    {isEdit: me.setting === 'EDIT' ? '编辑审批' :'阶段变更审批',//l-18nIgnore
                    isChange: me.setting === 'EDIT' ? '阶段变更审批' :'编辑审批'},//l-18nIgnore
                    '确定要将「{{isChange}}」修改为「{{isEdit}}」吗?'), $t('提示')).then(() => {
                    util.FHHApi({
                        url: '/EM1AFLOW/Config/Save',
                        data: {
                            flowType: 'stage',
                            flowConfigs: [{
                                "type": "stageChangeApproval",
                                "value":  me.setting, //说明：STAGE_CHANGE是触发阶段变更审批，EDIT是触发编辑审批
                                "terminal": "ALL"
                            }]
                        },
                        success: function(res) {
                            if (res.Result.StatusCode == 0) {
                                FxUI.Message.success($t('成功'))
                                me.hide();
                            };
                        }
                    }, {
                        errorAlertModel: 1
                    })
                }).catch(() => {
                });
            } else {
                me.hide();
            }
        },

        cancelHandle: function() {
            this.hide();
        },

        destroy: function() {
            Dialog.prototype.destroy.call(this);
        }
    });

    module.exports = Setting;
});
