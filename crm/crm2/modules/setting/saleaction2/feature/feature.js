/*
 * 版本新功能展示
 */
define(function(require, exports, module) {
    var Dialog = require('base-modules/dialog/dialog'),
          featureView = require('./feature-html');

    var Feature = Backbone.View.extend({

        initialize: function() {
            var me = this;
            this.featureView = featureView({});
            this.featureDialog = new Dialog({
                title: $t("阶段推进器说明"),
                content: this.featureView,
                btns: [{
                    label: $t("我知道了"),
                    action: 'iknow',
                    type: 'default'
                }],
                width: 712,
                zIndex: 999
            });
            this.featureDialog.show();
            this.featureDialog.on('hide', function() {
                me.trigger('featureClose');
                me.featureDialog.destroy();
            });
            this.featureDialog.on('iknow', function() {
                me.trigger('featureClose');
                me.featureDialog.hide();
                me.featureDialog.destroy();
            });
        }
    });

    module.exports = Feature;
});
