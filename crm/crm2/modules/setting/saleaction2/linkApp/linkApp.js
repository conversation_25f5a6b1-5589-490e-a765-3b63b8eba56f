/**
 * @description 阶段视图查看设置
 * <AUTHOR>
 */

define(function(require, exports, module) {
    var util = CRM.util,
        Dialog = require('crm-widget/dialog/dialog'),
        settingTpl = require('./linkApp-html');
    var Setting = Dialog.extend({
        attrs: {
            width: 510,
            title: $t("互联流程设置"),
            showBtns: true,
            showScroll: false,
			className: 'crm-d-page-config setting-saleaction2'
        },

        events: {
            'click .b-g-btn': 'confirmHandle',
            'click .b-g-btn-cancel': 'cancelHandle',
            'click .config-radio': 'triggerRadio'
        },
        initialize: function() {
            this.attrs = $.extend(true, {}, Setting.prototype.attrs, this.attrs);
            return Setting.superclass.initialize.apply(this, arguments);
        },
        show: function(data) {
            let me = this;
            let result =  Setting.superclass.show.call(this);
            me.getPageConfig();
            return result;
        },
        triggerRadio: function(e) {
            e && e.stopPropagation();
            let me = this;
            let ele = $(e.target).closest('.config-radio');
            if (!ele.hasClass('active')) {
                ele.siblings().removeClass('active');
                ele.addClass('active');
                me.changed = !me.changed;
            }
            me.setting = ele.data('value')
        },
        getPageConfig: function() {
            let me = this;
            me.element.find('.dialog-con').html(settingTpl());
            let tooltips = new Vue({
                template:` <fx-tooltip effect="dark" :content="text" placement="top-start">
                            <span class="setting-tooltip">?</span>
                           </fx-tooltip>`,
                data:{
                    text:$t('选择「是」，下游发起流程时，只能使用为当前「互联应用」创建的阶段推进器；选择「否」，下游发起流程时，可以使用与该对象相关的所有阶段推进器')
                    }
                })
            tooltips.$mount('#fortooltips');
            util.FHHApi({
                url: '/EM1AFLOW/Config/Get',
                data: {
                    flowType: 'stage',
                    type: 'outerUserTriggerScopeFilterLinkApp',
                    terminal: 'ALL'
                },
                success: function(res) {
                    if (res.Result.StatusCode == 0) {
                        me.setting = !!res.Value.value
                        me.changed = false;
                        $('.config-radio',me.$el).each((index, e) =>{
                            $(e).data('value') === me.setting &&  $(e).addClass('active');
                        })
                    };
                }
            }, {
                errorAlertModel: 2
            })
        },

        hide: function() {
            return Setting.superclass.hide.call(this);
        },

        confirmHandle: function() {
            let me = this;
            if(me.changed){
                util.FHHApi({
                    url: '/EM1AFLOW/Config/Save',
                    data: {
                        flowType: 'stage',
                        flowConfigs: [{
                            "type": "outerUserTriggerScopeFilterLinkApp",
                            "value":  me.setting,
                            "terminal": "ALL"
                        }]
                    },
                    success: function(res) {
                        if (res.Result.StatusCode == 0) {
                            FxUI.Message.success($t('成功'))
                            me.hide();
                        };
                    }
                }, {
                    errorAlertModel: 2
                })
            } else {
                me.hide();
            }
        },

        cancelHandle: function() {
            this.hide();
        },

        destroy: function() {
            Dialog.prototype.destroy.call(this);
        }
    });

    module.exports = Setting;
});
