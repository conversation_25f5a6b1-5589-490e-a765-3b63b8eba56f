/**
 * @desc
 * author qigb
 */
define(function(require, exports, module) {
  var Saleaction = Backbone.View.extend({
    initialize: function (opts) {
      var me = this;
      me.setElement(opts.wrapper);
      me.$el.addClass('crm-stage-table');
      const newChildElement = document.createElement('div');
      newChildElement.className = 'stage-manage';
      me.$el[0].appendChild(newChildElement)
    },
    render() {
      require.async(['paas-paasui/vui','paas-rocket/secondDev'], (vui,secondDev) => {
        secondDev.getComponent('manage').then(cmpt => {
          this.inst  = new Vue({
            el: this.$el.children('.stage-manage')[0],
            render: h => h(cmpt)
          })
        })
      })
    },
    destroy() {
      this.inst && this.inst.$destroy();
    }
  })

  module.exports = Saleaction;
});
