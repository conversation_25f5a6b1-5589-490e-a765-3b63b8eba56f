.crm-rocket-feature {
    width: 712px;
    height: 324px;
    position: relative;
    li {
        float: left;
        width: 180px;
        margin-top: 32px;
        &.feature-ground {
            width: 30px;
            height: 6px;
            background: url("@{imgUrl}/feature_ground.png") no-repeat;
        }
        span {
            font-family: PingFangSC-Regular;
            color: #212B36;
            font-size: 18px;
            line-height: 25px;
            display: inline-block;
            text-align: center;
            &.feature-content {
                font-size: 12px;
                line-height: 17px;
            }
        }
    }
    .crm-doclink {
      background: none;
      text-indent: 0px;
      &::before{
        color: #ccc;
      }
      &:hover{
        &::before {
          color:var(--color-primary05)
        }
      }
    }

    .feature_1 {
        width: 250px;
        margin-left: 84.5px;
        .feature_1img {
            width: 176px;
            height: 176px;
            background: url("@{imgUrl}/feature_1.png") no-repeat;
            margin-left: 25px;
            margin-bottom: 24px;
            background-size: contain;
        }
        .feature-content {
            margin-left: -10.5px;
        }

    }
    .feature-ground {
        margin: 125px 9px 0 9px;
    }
    .feature_2 {
        margin-left: 23px;
        width: 250px;
        .feature_2img {
            width: 176px;
            height: 176px;
            background: url("@{imgUrl}/feature_2.png") no-repeat;
            margin-left: 32px;
            margin-bottom: 24px;
            background-size: contain;
        }
        .feature-content1 {
            margin-left: 8.5px;
        }
    }
}

.setting-saleaction2 .setting-page-config {
	font-size: 14px;
    .config-li .config-radio:first-child {
        margin-right: 32px;
    }
    .setting-tooltip{
      display: inline-block;
      padding: 0 5px;
      line-height: 15px;
      border-radius: 10px;
      background: #cecece;
      margin-left: 5px;
      color: var(--color-neutrals01);
      font-size: 12px;
    }

	.config-radio {
		position: relative;
		line-height: 30px;
		padding-left: 24px;
		cursor: pointer;

		&::before {
			position: absolute;
			left: 0;
			top: 0;
			width: 15px;
			height: 15px;
			border: 1px solid #dee1e6;
			border-radius: 50%;
			content: '';
			display: block;
		}

		color: var(--color-neutrals19);

		&.active {
			&::before {
				position: absolute;
				left: 0;
				top: 0;
				width: 8px;
				height: 8px;
				border: 4px solid var(--color-primary06);
				border-radius: 50%;
				content: '';
				display: block;
			}
		}
        &.readOnly {
            color: #c0c4cc;
            &::before {
                background-color: #f5f7fa;
                border-color: #e4e7ed;
            }
        }
	}

}
.crm-s-saleaction2 {
  .crm-doclink {
    background-image: none;
    vertical-align: -2px;
    text-indent: inherit;
    &::before {
      color: #C1C5CE;
    }
  }
  .dt-term-batch{
    padding-bottom: 8px;
  }
  .j-saleaction2-export {
    background-color: #fff !important;
    color: #2a304d !important;
    border: 1px solid #c1c5ce !important;
  }
  .apiName{
    &:hover{
        .fx-icon-fuzhi{
            display: inline-block;
        }
    }
    .fx-icon-fuzhi {
        display: none;
    }
  }
  .saleaction2-search-button-class {
    color: #fff !important;
    border-radius: 0px 2px 2px 0px;
    background-color: var(--color-primary03) !important;
  }
  .saleaction2-search-focus-button-class {
    color: #fff !important;
    border-radius: 0px 2px 2px 0px;
    background-color: var(--color-primary06) !important;

  }
  .saleaction2-search-entity-class, .saleaction2-search-state-class {
    margin-right: 8px;
  }
  .definition-icon {
    height: 18px;
    color: #91959E;
    border: 1px solid #DEE1E8;
    display: inline-block;
    padding: 0px 4px;
    border-radius: 2px;
  }
  .definition-icon:nth-of-type(1){
    margin-left: 4px;
  }
}
