/**
 * @description 阶段视图查看设置
 * <AUTHOR>
 */

define(function(require, exports, module) {
    var util = CRM.util,
        Dialog = require('crm-widget/dialog/dialog'),
        settingTpl = require('./setting-html');
    var Setting = Dialog.extend({
        attrs: {
            width: 510,
            title: $t("阶段视图查看设置"),
            showBtns: true,
            showScroll: false,
			className: 'crm-d-page-config setting-saleaction2'
        },

        events: {
            'click .b-g-btn': 'confirmHandle',
            'click .b-g-btn-cancel': 'cancelHandle',
            'click .config-radio': 'triggerRadio'
        },
        initialize: function() {
            this.attrs = $.extend(true, {}, Setting.prototype.attrs, this.attrs);
            return Setting.superclass.initialize.apply(this, arguments);
        },
        show: function(data) {
            let me = this;
            let result =  Setting.superclass.show.call(this);
            me.getPageConfig();
            return result;
        },
        triggerRadio: function(e) {
            e && e.stopPropagation();
            let me = this;
            let ele = $(e.target).closest('.config-radio');
            let parentEle = $(e.target).closest('.config-li');
            if (!ele.hasClass('active')) {
                ele.siblings().removeClass('active');
                ele.addClass('active');
                if (parentEle.hasClass('stage-scope')) {
                    me.stageDefinitionViewScope = ele.index() ? 'SCOPE' : 'ALL';
                } else if (parentEle.hasClass('all-stage')) {
                    me.showAllStages = !ele.index();
                }
            }
        },
        getPageConfig: function() {
            let me = this;
            util.FHHApi({
                url: '/EM1AFLOW/Config/Get',
                data: {
                    flowType: 'stage',
                    types: ['showAllStages', 'stageDefinitionViewScope'],
                    terminal: 'ALL'
                },
                success: function(res) {
                    if (res.Result.StatusCode == 0) {
                        if (res.Value.values) {
                            me.element.find('.dialog-con').html(settingTpl());
                            let values = res.Value.values;
                            if (values.stageDefinitionViewScope) {
                                me.element.find('.stage-scope .config-radio').removeClass('active');
                                if (values.stageDefinitionViewScope == 'ALL') {
                                    me.element.find('.stage-scope .see-all').addClass('active');
                                } else if (values.stageDefinitionViewScope == 'SCOPE') {
                                    me.element.find('.stage-scope .see-scope').addClass('active');
                                }
                                me.stageDefinitionViewScope = values.stageDefinitionViewScope;
                            }
                            if (_.isBoolean(values.showAllStages)) {
                                me.element.find('.all-stage .config-radio').removeClass('active');
                                if (values.showAllStages) {
                                    me.element.find('.all-stage .true').addClass('active');
                                } else {
                                    me.element.find('.all-stage .false').addClass('active');
                                }
                                me.showAllStages = values.showAllStages;
                            }
                        }

                    };
                }
            }, {
                errorAlertModel: 1
            })
        },

        hide: function() {
            return Setting.superclass.hide.call(this);
        },

        confirmHandle: function(e) {
            let me = this;
            let flowConfigs = _.map(['showAllStages', 'stageDefinitionViewScope'], (item) => {
                return {
                    type: item,
                    value: me[item],
                    terminal: 'ALL'
                }
            })
            util.FHHApi({
                url: '/EM1AFLOW/Config/Save',
                data: {
                    flowType: 'stage',
                    flowConfigs: flowConfigs
                },
                success: function(res) {
                    if (res.Result.StatusCode == 0) {
                        FxUI.Message.success($t('成功'))
                        me.hide();
                    };
                }
            }, {
                errorAlertModel: 1
            })
        },

        cancelHandle: function() {
            this.hide();
        },

        destroy: function() {
            Dialog.prototype.destroy.call(this);
        }
    });

    module.exports = Setting;
});
