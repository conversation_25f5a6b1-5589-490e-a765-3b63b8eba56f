/**
 * @description 审批流排序
 * <AUTHOR>
 */

define(function(require, exports, module) {
    var util = CRM.util,
        Dialog = require('crm-widget/dialog/dialog'),
        Select = require('crm-widget/select/select'),
        tpl = require('./template/tpl-html'),
        listTpl = require('./template/list-html');
    var Sort = Dialog.extend({
        options: {
            objectType: 200022
        },

        attrs: {
            width: 1150,
            title: $t("排列优先级"),
            showBtns: true,
            showScroll: false,
            className: 'crm-d-sort'
        },

        events: {
            'click .b-g-btn': 'confirmHandle',
            'click .b-g-btn-cancel': 'cancelHandle',
            'dragstart tbody tr': 'startHandle',
            'dragend tbody tr': 'endHandle',
            'dragenter tbody tr': 'enterHandle',
            'dragover tbody tr': 'overHandle',
            'drop tbody tr': 'dropHandle'
        },

        initialize: function() {
            var result = Sort.superclass.initialize.apply(this, arguments);
            return result;
        },

        initSelect: function(list) {
            var me = this;
            if (me._select) {
                me._select.destroy();
                me._select = null;
            }
            me._select = new Select({
                $wrap: $('.select-con', this.element),
                zIndex: this.get('zIndex') * 1 + 10,
                defaultValue: null,
                size: 1,
                options: _.map(list, function(item) {
                    return {
                        name: item.entityName,
                        value: item.entityId
                    }
                })
            });
            this._select.on('change', function(v, item) {
                me.renderList();
            });
        },

        getWorkFlowList: function(callback) {
            var me = this;
            util.FHHApi({
                url: '/EM1HSTAGE/Definition/GetDefinitionList',
                data: {
                    entityId: me._select.getValue(),
                    supportPagingQuery: false,
                    enable: 0
                },
                success: function(res) {
                    if (res.Result.StatusCode == 0) {
                        var data = res.Value || {};
                        data.result = _.sortBy(data.data || [], 'priority');
                        callback && callback(data);
                        return;
                    };

                    util.alert(res.Result.FailureMessage);
                    console.log(res);
                }
            }, {
                errorAlertModel: 1
            })
        },

        createMask: function() {
            var mask = $('<div  class="tr drag-mask" draggable="true"></div>'),
                draging = this.draging,
                draged = this.draged,
                wrapOffset = this.wrap.offset(),
                dragedOffset = draged.offset();
            this.mask && this.mask.remove();
            mask.css({
                width: draged.width(),
                height: draged.height() - 1,
                position: 'absolute',
                left: dragedOffset.left - wrapOffset.left,
                top: dragedOffset.top - wrapOffset.top,
                background: '#ffffff'
            });

            mask.on({
                dragover: this.overHandle,
                drop: function(e) {
                    e.stopPropagation();
                    e.preventDefault();
                    return false;
                }
            })

            this.mask = mask;
            this.wrap.append(mask);
        },

        startHandle: function(e) {
            var oEvent = e.originalEvent;
            if (oEvent.dataTransfer) {
                oEvent.dataTransfer.setData('text', '');
            }

            this.draging = $(e.currentTarget);
            this.trigger('dragstart', oEvent);
        },

        endHandle: function(e) {
            var oEvent = e.originalEvent;
            this.mask && this.mask.remove();
            oEvent.dataTransfer.clearData('text');
            return false;
        },

        enterHandle: function(e) {
            var $target = $(e.currentTarget);
            if (this.draging.attr('drag_id') !== $target.attr('drag_id')) {
                this.draged = $target;
                this.createMask();
                if (this.draging.index() > this.draged.index()) {
                    this.draged.before(this.draging);
                } else {
                    this.draged.after(this.draging);
                }
                this.mask.css({
                    top: this.draging.offset().top - this.wrap.offset().top + 1
                });
            }
        },

        overHandle: function(e) {
            var oEvent = e.originalEvent;
            if (oEvent.dataTransfer) {
                oEvent.dataTransfer.dropEffect = 'move';
            }
            e.preventDefault();
            return false;
        },

        dropHandle: function(e) {
            var oEvent = e.originalEvent;
            if (oEvent.dataTransfer) {
                oEvent.dataTransfer.clearData();
            }
            e.preventDefault();
        },

        show: function(data) {
            var me = this,
                result = Sort.superclass.show.call(this);
            if (!this.isRender) {
                this.setContent(tpl());
                this.wrap = $('table', this.element);
                this.isRender = true;
            }
            me.roles = data.roles || [];
            me.groups = data.groups || [];
            me.initSelect(data.list);
            if (data.list && data.list.length) {
                me.renderList();
            }
            return result;
        },

        renderList: function() {
            var me = this;
            this.getWorkFlowList(function(value) {
                me.element.find('tbody').html(listTpl(_.extend({}, value, {
                    util: util,
                    getScope: me.getScope.bind(me)
                })));
                me.resizedialog();
            });
        },

        getScope: function(rangeCircleIds, rangeEmployeeIds, rangeGroupIds, rangeRoleIds) {
            var me = this;
            var scope = [];
            _.each(rangeCircleIds, function(id) {
                var cir = util.getCircleById(id);
                scope.push(cir ? cir.name : '--');
            });
            _.each(rangeEmployeeIds, function(id) {
                var emp = util.getEmployeeById(id);
                scope.push(emp ? emp.name : '--');
            });
            _.each(rangeGroupIds, function(id) {
                var group = _.findWhere(me.groups, {
                    id: id
                });
                scope.push(group ? group.name : '--');
            });
            _.each(rangeRoleIds || [], function(id) {
                var role = _.findWhere(me.roles, {
                    id: id
                });
                scope.push(role ? role.name : '--');
            });
            if (scope.length) {
                return scope.join($t("，"));
            } else {
                return '--';
            }
        },

        hide: function() {
            var result = Sort.superclass.hide.call(this);
            return result;
        },

        setWorkFlowPriorities: function(data) {
            var me = this;
            util.FHHApi({
                url: '/EM1HSTAGE/Definition/SetPriorities',
                data: {
                    priorities: data
                },
                success: function(res) {
                    if (res.Result.StatusCode == 0) {
                        me.trigger('refresh', 'sort');
                        me.hide();
                        return;
                    };
                    util.alert(res.Result.FailureMessage);
                }
            }, {
                errorAlertModel: 1,
                submitSelector: $('.b-g-btn', this.element)
            })
        },

        confirmHandle: function(e) {
            var me = this,
                priority = [],
                trEl = $('tbody .tr', me.element);

            trEl.each(function(index, el) {
                priority.push({
                    sourceWorkflowId: $(el).data('sourceworkflowid'),
                    priority: index
                });
            });

            if (priority.length < 2) {
                me.hide();
                return;
            }
            this.setWorkFlowPriorities(priority);
        },

        cancelHandle: function() {
            this.hide();
        },

        destroy: function() {
            var me = this;
            _.each(['_select'], function(item) {
                if (me[item] && me[item].destroy) {
                    me[item].destroy();
                    me[item] = null;
                }
            });
            me.undelegateEvents();
        }
    });

    module.exports = Sort;
});
