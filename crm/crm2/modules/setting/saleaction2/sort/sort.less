.crm-d-sort{
	.sort-wrap{
		border: 1px solid #eee;
		.select-wrap{
			position: relative;
			height: 48px;
			line-height: 48px;
			padding-left: 20px;
			.select-label{
				float: left;
				width: 50px;
				color: #999;
			}
			.select-con{
				position: absolute;
				top: 10px;
				width: 140px;
				height: 28px;
				margin-left: 50px;
			}
			.g-select-title-wrapper{
				height: 28px;
				line-height: 28px;
				i{
					top: 10px;
				}
			}
		}
	}
	table{
		position: relative;
		width: 100%;
		line-height: 48px;
		color: #666;
		thead tr th{
			border-top: 1px solid #eee;
			border-bottom: 1px solid #eee;
			.th-tit{
				display: block;
				padding: 0 10px;
			}
			&:first-child{
				.th-tit{
					padding: 0 10px 0 20px;
				}
			}
		}
		tbody{
			.drag-mask{
				border-bottom: 1px solid #eee;
			}
		}

		tbody tr td{
			border-bottom: 1px solid #eee;
			padding: 0 10px;
			overflow: hidden;
			text-overflow: ellipsis; 
    		white-space:nowrap;
			&:first-child{
				padding: 0 10px 0 20px;
			}
			.cursor{
				margin: 16px 20px 0 0;
				float: left;
				width: 8px;
				height: 18px;
				background: url('@{imgUrl}/sort.png') no-repeat 0 0;
			}
			&.tr-name {
				max-width: 200px;
			}
			&.tr-entityName {
				max-width: 120px;
			}
			&.tr-stageFieldDisplayName, &.tr-scope {
				max-width: 100px;
			}
		}
	}
}