##_.each(result, function(item, index) {##
	<tr draggable='true' class="tr" data-sourceworkflowid="{{item.sourceWorkflowId}}" drag_id="{{index}}">
		<td class="tr-name" title="{{item.name || '--'}}"><span class="cursor"></span>{{item.name || '--'}}</td>
		<td class="tr-entityName" title="{{item.entityName || '--'}}">{{item.entityName || '--'}}</td>
		<td class="tr-stageFieldDisplayName" title="{{item.stageFieldDisplayName || '--'}}">{{item.stageFieldDisplayName || '--'}} </td>
		## var scope = getScope(item.rangeCircleIds, item.rangeEmployeeIds, item.rangeGroupIds, item.rangeRoleIds); ##
		<td class="tr-scope" title="{{scope}}">{{scope}}</td>
		<td>{{util.getEmployeeById(item.creator) ? util.getEmployeeById(item.creator).name : '--'}}</td>
		<td>{{FS.moment(item.createTime).format('YYYY-MM-DD HH:mm:ss')}}</td>
		<td>{{util.getEmployeeById(item.modifier) ? util.getEmployeeById(item.modifier).name : '--'}}</td>
		<td>{{FS.moment(item.modifyTime).format('YYYY-MM-DD HH:mm:ss')}}</td>
		<td>{{item.enable ? $t("启用") : $t("停用")}}</td>
	</tr>
##})##