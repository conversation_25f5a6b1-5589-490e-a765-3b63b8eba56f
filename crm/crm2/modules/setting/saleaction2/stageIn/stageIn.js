/**
 * @description 进入阶段提示语配置
 * <AUTHOR>
 */

 define(function(require, exports, module) {
    var util = CRM.util,
        Dialog = require('crm-widget/dialog/dialog'),
        settingTpl = require('./stageIn-html');
    var Setting = Dialog.extend({
        attrs: {
            width: 510,
            title: $t("进入阶段提示语配置"),
            showBtns: true,
            showScroll: false,
			className: 'crm-d-page-config setting-saleaction2'
        },
        events: {
            'click .b-g-btn': 'confirmHandle',
            'click .b-g-btn-cancel': 'cancelHandle',
            'click .config-radio': 'triggerRadio'
        },
        initialize: function() {
            this.attrs = $.extend(true, {}, Setting.prototype.attrs, this.attrs);
            return Setting.superclass.initialize.apply(this, arguments);
        },
        show: function(data) {
            let me = this;
            let result =  Setting.superclass.show.call(this);
            me.getPageConfig();
            return result;
        },
        triggerRadio: function(e) {
            e && e.stopPropagation();
            let me = this;
            let ele = $(e.target).closest('.config-radio');
            if (!ele.hasClass('active')) {
                ele.siblings().removeClass('active');
                ele.addClass('active');
                me.showSkipInTip = ele.index() ? true : false;
            }
        },
        getPageConfig: function() {
            let me = this;
            util.FHHApi({
                url: '/EM1AFLOW/Config/Get',
                data: {
                    flowType: 'stage',
                    types: ['showSkipInTip'],
                    terminal: 'ALL'
                },
                success: function(res) {
                    if (res.Result.StatusCode == 0) {
                        if (res.Value.values) {
                            me.element.find('.dialog-con').html(settingTpl());
                            let values = res.Value.values;
                            me.element.find('.stage-scope .config-radio').removeClass('active');
                            if (!values.showSkipInTip) {
                                me.element.find('.stage-scope .see-all').addClass('active');
                            } else {
                                me.element.find('.stage-scope .see-scope').addClass('active');
                            }
                            me.showSkipInTip = values.showSkipInTip;
                        }

                    };
                }
            }, {
                errorAlertModel: 1
            })
        },

        hide: function() {
            return Setting.superclass.hide.call(this);
        },

        confirmHandle: function(e) {
            let me = this;
            let flowConfigs = _.map(['showSkipInTip'], (item) => {
                return {
                    type: item,
                    value: me[item],
                    terminal: 'ALL'
                }
            })
            util.FHHApi({
                url: '/EM1AFLOW/Config/Save',
                data: {
                    flowType: 'stage',
                    flowConfigs: flowConfigs
                },
                success: function(res) {
                    if (res.Result.StatusCode == 0) {
                        FxUI.Message.success($t('成功'))
                        me.hide();
                    };
                }
            }, {
                errorAlertModel: 1
            })
        },

        cancelHandle: function() {
            this.hide();
        },

        destroy: function() {
            Dialog.prototype.destroy.call(this);
        }
    });

    module.exports = Setting;
});
