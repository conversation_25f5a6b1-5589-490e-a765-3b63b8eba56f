/**
 * @description 阶段任务展示顺序设置
 * <AUTHOR>
 */

 define(function(require, exports, module) {
    var util = CRM.util,
        Dialog = require('crm-widget/dialog/dialog'),
        tpl= require('./task-html');
        module.exports = Dialog.extend({
            attrs: {
                title: $t("阶段任务展示顺序"),
                width: 500,
                showBtns: true,
                className: 'crm-d-page-config setting-saleaction2'
            },
    
            events: {
                'click .order-radio': 'radioHandleShow',
                'click .b-g-btn': 'confirmHandle',
                'click .b-g-btn-cancel': '_closeHandle',
            },
    
            radioHandleShow: function(e) {
                e && e.stopPropagation();
                let me = this;
                let element = $(e.target).closest('.order-radio');
                let radio = me.element.find('.config-li .order-radio');
                let index = $(e.target).closest('.order-radio').index();
                if (!element.hasClass('active')) {
                    radio.removeClass('active');
                    element.addClass('active');
                    me.showed = index - 1;
                }
            },
            confirmHandle: function() {
                let me = this;
                let param ={
                    "flowType": "stage",
                    flowConfigs: [
                        {
                        "type": "stageTaskDisplaySequence",
                        "value": me.showed === 0  ? 'default':'prioritizeShowUnfinishedTasks', //说明：true:默认展示顺序,false:优先展示未完成任务
                        "terminal": "ALL"
                      }]
                }
                util.FHHApi({
                    url: '/EM1AFLOW/Config/Save',
                    data: param,
                    success: function() {
                        me._closeHandle();
                    }
                }, {
                    errorAlertModel: 2
                })
    
            },
            show: function() {
                var me = this;
                let params = {
                    "flowType": "stage",
                    "type": "stageTaskDisplaySequence",
                    "terminal": "ALL"
                  };
                util.FHHApi({
                    url: '/EM1AFLOW/Config/Get',
                    data: params,
                    success: function(res) {
                        let value = res.Value.value;
                        me.showed = value==='default' ? 0 : 1;
                        me.initBtnStatus();
                    }
                }, {
                    errorAlertModel: 2
                })
                result = module.exports.superclass.show.call(this);
                me.setContent(tpl());
                FxUI.create({
                    wrapper:me.element.find('.config-li .fortooltips')[0],
                    template:` <fx-tooltip effect="dark" :content="text" placement="top-start">
                                <span class="setting-tooltip" >?</span>
                               </fx-tooltip>`,
                    data(){
                        return{
                            text:$t('flow.stage.taskDisplaySequence.tooltip','按阶段任务的配置顺序')
                        }}
                    })
                me.resizedialog();
                return result;
            },
            initBtnStatus() {
                this.element.find('.config-li .order-radio')[this.showed].classList.add('active');
            },
            _closeHandle: function() {
                this.hide();
            },
    
            destroy:function(){
                return module.exports.superclass.destroy.call(this);
            }
        });
});
