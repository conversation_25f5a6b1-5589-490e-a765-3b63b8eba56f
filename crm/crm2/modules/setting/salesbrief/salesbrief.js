/**
 * @desc 目标设置弹窗
 * <AUTHOR>
 */

define(function (require, exports, module) {
    var indexTpl = require('./template/tpl-html'),
        contentTpl = require('./template/content-html'),
        util = require('crm-modules/common/util'),
        Selector = require('crm-widget/selector/selector'),
        TimeSelect = require('./timeselect/timeselect');
    var reportListType = [0, 0, 'day', 'week', 'month', 'page'];
    var data = [
        {
            "SaleRangeType": 2,
            "IsPush": false,
            "WhiteList": [],
            "BlackList": [],
            "Interval": 0,
            "PushTime": 0
        }, {
            "SaleRangeType": 3,
            "IsPush": false,
            "WhiteList": [],
            "BlackList": [],
            "Interval": 0,
            "PushTime": 0
        }, {
            "SaleRangeType": 4,
            "IsPush": false,
            "WhiteList": [],
            "BlackList": [],
            "Interval": 0,
            "PushTime": 0
		},
		// {
		//     "SaleRangeType": 5,
		//     "IsPush": false,
		//     "WhiteList": [],
		//     "BlackList": [],
		//     "Interval": 0,
		//     "PushTime": 0
		// }
    ];
    var Page = Backbone.View.extend({
        initialize: function(opts) {
            this.day = [];
            this.week = [];
            this.month = [];
            this.page = [];
            this.setElement(opts.wrapper);
            this.el.innerHTML = indexTpl();
        },

        events: {
            'click .switch-sec': 'clickHandle',
            'click .mn-checkbox-item': 'checkHandle'
        },

        render: function() {
            var me = this;

            util.FHHApi({
                url: '/EM1HCRM/SaleReportSetting/GetSaleReportSetting',
                success: function(res) {
                    if(res.Result.StatusCode == 0) {
                        me.reportSets = me.formatData(res.Value);

                        // 绘制完成后再显示，减少重绘次数
                        me.$('.content-wrap').hide()
                            .html(contentTpl(me.reportSets))
                        me.initPlug();
                        me.$('.crm-loading').hide();
                        me.$('.content-wrap').show();
                    }
                }
            });
        },

        clickHandle: function(e) {
            var isPush = 0,
                target = $(e.currentTarget),
                parent = target.closest('.report-set'),
                type = parent.attr('type'),
                mask = parent.find('.forbiden-mask').length ? parent.find('.forbiden-mask') : parent.prepend('<div class="forbiden-mask" />');
            target.toggleClass('on');
            if(target.hasClass('on')) {
                isPush = 1;
                parent.removeClass('forbiden');
                parent.find('.mn-selected').removeClass('disabled-selected');
                mask.remove();
            } else {
                isPush = 0;
                parent.addClass('forbiden');
                parent.find('.mn-selected').addClass('disabled-selected');
            }

            // 选人控件置灰
            _.each(this[type] || [], function(item) {
                if (target.hasClass('on')) {
                    item.instance && item.instance.lock && item.instance.unlock();
                } else {
                    item.instance && item.instance.lock && item.instance.lock();
                }
            });
            this.setSaleReport(type, isPush);
        },

        checkHandle: function(e) {
            var me = this;
            var $target = $(e.currentTarget);
            var $parent = $target.closest('.report-set'); 
            $target.toggleClass('mn-selected');

            me.remindDayList = [];
            _.each($parent.find('.mn-checkbox-item'), function(item) {
                if ($(item).hasClass('mn-selected')) {
                    me.remindDayList.push($(item).data('index'));
                }; 
            });
            me.setSaleReport($parent.attr('type'), $parent.find('.switch-sec').hasClass('on') ? 1 : 0);
            return false;
        },

        collect: function(type, isPush) {
            var white = [];
            var black = [];
            var interval;
            var pushtime;

            _.each(this[type], function(item) {
                if(item.type == 'interval') {
                    interval = item.instance.select.getValue()
                };
                if(item.type == 'pushtime') {
                    pushtime = item.instance.select.getValue();
                }
                if(item.type == 'rangWhite') {
                    var member = item.instance.getValue('member');
                    var group  = item.instance.getValue('group');
                    if (member && member.length > 0) {
                        _.each(member, function(d) {
                            white.push({
                                type: 1,
                                data: d
                            });
                        });
                    }
                    if(group && group.length > 0) {
                        _.each(group, function(d) {
                            white.push({
                                type: 2,
                                data: d
                            });
                        });
                    }
                }
                if(item.type == 'rangBlack') {
                    var rMember = item.instance.getValue('member');
                    var rGroup  = item.instance.getValue('group');
                    if (rMember && rMember.length > 0) {
                        _.each(rMember, function(d) {
                            black.push({
                                type: 1,
                                data: d
                            });
                        });
                    }
                    if(rGroup && rGroup.length > 0) {
                        _.each(rGroup, function(d) {
                            black.push({
                                type: 2,
                                data: d
                            });
                        });
                    }
                }
            });
            var data = {
                SaleRangeType: {'day': 2, 'week': 3, 'month': 4,'page': 5}[type],
                IsPush: isPush,
                WhiteList: white,
                BlackList: black,
                Interval: interval || 0,
                PushTime: pushtime ? FS.moment(FS.moment().format("YYYY/MM/DD"), "YYYY/MM/DD").valueOf() + pushtime * 60 * 60 * 1000: 0,
                RemindDays: this.remindDayList.join(',')
            }
            return data;
        },

        initPlug: function() {
            var me = this, optionArr=[],
                getDefault = function(item) {
                var name, type = item.Type == 1 ? 'p' : 'g';
                if(item.Type == 1) {
                    name = util.getEmployeeById(item.Data);
                } else {
                    name = util.getCircleById(item.Data);
                }
                return {
                    name: name,
                    id:item.Data,
                    type: type
                }
            };

            _.each(this.reportSets.SaleReportSettingList, function(item, index) {
                var push = me.$('.' + reportListType[item.SaleRangeType] + '-push'),
                    unpush = me.$('.' + reportListType[item.SaleRangeType] + '-unpush'),
                    hourselect = me.$('.' + reportListType[item.SaleRangeType] + '-hour-select'),
                    weekselect = me.$('.' + reportListType[item.SaleRangeType] + '-week-select'),
                    dayselect = me.$('.' + reportListType[item.SaleRangeType] + '-day-select');
                   
                // TODO: 1. 选人组件append入document， 2. 共用同一选人组件 
                me[reportListType[item.SaleRangeType]].push({
                    type: 'rangWhite',
                    instance: me.instanceSelectBar({
                        selectbar: push,
                        isPush: item.IsPush,
                        defaultsValue: _.map(item.WhiteList, getDefault)
                    })
                });
                
                me[reportListType[item.SaleRangeType]].push({
                    type: 'rangBlack',
                    instance: me.instanceSelectBar({
                        selectbar: unpush,
                        isPush: item.IsPush,
                        defaultsValue: _.map(item.BlackList, getDefault)
                    })
                });
                
                if(hourselect.length > 0) {
                    me[reportListType[item.SaleRangeType]].push({
                        type: 'pushtime',
                        instance: new TimeSelect({
                            parentNode: hourselect,
                            element: hourselect,
                            defaultValue: FS.moment(item.PushTime).format('H'),
                            callback: function(item) {
                                var type = this.element.closest('.report-set').attr('type');
                                me.setSaleReport(type, 1);
                            }
                        })
                    });
                }
                if(weekselect.length > 0) {
                    me[reportListType[item.SaleRangeType]].push({
                        type: 'interval',
                        instance: new TimeSelect({
                            parentNode: weekselect,
                            type: 2,
                            defaultValue: item.Interval,
                            element: weekselect,
                            callback: function(item) {
                                var type = this.element.closest('.report-set').attr('type');
                                me.setSaleReport(type, 1);
                            }
                        })
                    });
                }
                if(dayselect.length > 0) {
                    me[reportListType[item.SaleRangeType]].push({
                        type: 'interval',
                        instance: new TimeSelect({
                            parentNode: dayselect,
                            type: 3,
                            defaultValue: item.Interval,
                            element: dayselect,
                            callback: function(item) {
                                var type = this.element.closest('.report-set').attr('type');
                                me.setSaleReport(type, 1);
                            }
                        })
                    });
                }

                if (item.SaleRangeType === 2) {
                    me.remindDayList = item.RemindDays ? item.RemindDays.split(',') : [];
                };
            });
        },

        instanceSelectBar: function(options) {
            var me = this;

            var sb = new Selector({
                $wrap:      me.$(options.selectbar),
                parentNode: me.$(options.selectbar),
                zIndex:     1000,
                member:     true,
                group:      true,
                single:     false,
                label:      options.label || $t("选择范围"),
                defaultSelectedItems: (function(){
                    var data = {member: [], group: []};
                    _.each(options.defaultsValue || [], function(item) {
                        if (item.type == 'p') {
                            data.member.push(item.id);
                        } else {
                            data.group.push(item.id);
                        }
                    });
                    return data;
                }())
            });

            sb.on('change', function() {
                var type = sb.$el.closest('.report-set').attr('type');
                me.setSaleReport(type, 1);
            });
            
            if (!options.isPush) {
                sb.lock();
            }
            
            return sb;
        },

        setSaleReport: function(type, isPush) {
            var data = this.collect(type, isPush);
            util.FHHApi({
                url: '/EM1HCRM/SaleReportSetting/SetSaleReportSetting',
                data: data,
                success: function(res) {
                    if(res.Result.StatusCode == 0) {
                        util.remind(1, $t("设置成功"));
                    } else {
                        util.remind(2, $t("设置失败"));
                    }
                    
                }
            });
        },

        formatData: function(setlist) {
            var tempList = {
                SaleReportSettingList: []
            };
            _.each(data, function(item, index) {
                _.each(setlist.SaleReportSettingList, function(list) {
                    if(list.SaleRangeType == item.SaleRangeType) {
                        data[index] = list;
                    }
                });
            });

            return {
                SaleReportSettingList: data
            };
        },

        destroy: function() {
            var me = this;
            _.each(['day', 'week', 'month', 'page'], function(items) {
                _.each(me[items], function(item) {
                    item.instance.destroy && item.instance.destroy();
                });
                me.items = null;
            })
            this.undelegateEvents();
        }
    });

    module.exports = Page;
});
