.crm-s-salesbrief {
    .forbiden{
        color: #cccccc;
        overflow: hidden;
        .forbiden-mask{
            width: 100%;
            height: 100%;
            position: absolute;
            z-index: 1;
            margin-top: 30px;
        }
    }
    .describe{
        padding: 15px 20px;
        background: #f4f6f9;
        .des-title{
            line-height: 24px;
            font-size: 18px;
            padding-bottom: 10px;
        }
        .des-text{
            line-height: 22px;
            margin-bottom: 4px;
            font-size: 14px;
        }
    }
    .report-set{
        position: relative;
        width: 580px;
        padding: 40px 20px 12px 20px;
        background: var(--color-neutrals01);
        h3.title{
            margin-bottom: 30px;
            line-height: 21px;
            font-size: 16px;
            color: #333;
        }
        .content{
            
        }
        .switch-sec{
            position: absolute;
            right: 84px;
            top: 40px;
            cursor: pointer;
            .label{
                margin-right: 10px;
                vertical-align: middle;
            }
            .core{
                position: relative;
                display: inline-block;
                width: 44px;
                height: 24px;
                vertical-align: middle;
                border-radius: 50px;
                background: #ccc;
            }
            .slider{
                position: absolute;
                top: 0;
                left: 2px;
                bottom: 0;
                margin: auto 0;
                line-height: 24px;
                display: block;
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background: var(--color-neutrals01);
                #css3 > .transition(left .1s);
            } 
        }
        .on{
            .core{
                background: #72ce56;
            }
            .slider{
                left: 21px;
            }
        }
        .field-item{
            margin-bottom: 20px;
            .field-label{
                float: left;
                width: 106px;
                line-height: 36px;

            }
            .pre-text{
                float: left;
                line-height: 36px;
                margin-right: 10px;
            }
            .center-text{
                float: left;
                line-height: 36px;
                margin-right: 10px;
            }
            .field-anchor{
                position: relative;
                width: 410px;
                float: left;
                line-height: 36px;
            }
            .hour-select{
                width: 225px;
            }
            .week-select{
                width: 175px;
                margin-right: 10px;
            }
            .day-select{
                width: 128px;
                margin-right: 10px;
            }
        }
    }
    .checkbox-group {
        margin-bottom: 15px;
        .check-lb {
            margin-left: 10px;
        }
        li{
            float: left;
            width: 100px;
            padding: 10px 15px 0 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    } 
    .day-hour-select {
        margin-left: 106px;
    }

    .selector-search .search-icon {
        margin-top: 2px;
    }
}