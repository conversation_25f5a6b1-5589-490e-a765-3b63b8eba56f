 ## _.each(SaleReportSettingList, function(item) {
   var configArr = [
    {},
    {},
    {type: 'day', text: $t("日报"), interval: $t("推送时间")}, 
    {type: 'week', text: $t("周报"), interval: $t("每周")}, 
    {type: 'month', text: $t("月报"), interval: $t("每月")}, 
    {type: 'page', text: $t("目标设置页面"), interval: ''}
   ]; 
   var dayList = [$t("周一"), $t("周二"), $t("周三"), $t("周四"), $t("周五"), $t("周六"), $t("周日")];
##
    <div type="{{configArr[item.SaleRangeType].type}}" class="{{configArr[item.SaleRangeType].type}}-set report-set {{item.IsPush ? '' : 'forbiden'}}">
        ##if(!item.IsPush) {##
            <div class="forbiden-mask"></div>
        ##}##
        <h3 class="title">{{configArr[item.SaleRangeType].text}}{{$t("推送设置")}}</h3>
        <div class="content">
            <div class="switch-sec {{item.IsPush ? 'on' : ''}}">
                <span class="label">{{$t("推送")}}</span>
                <span class="core">
                    <i class="slider"></i>
                </span>
            </div>
            <div class="field-item clearfix">
                <label class="field-label">{{$t("推送范围")}}</label>
                <div class="field-anchor {{configArr[item.SaleRangeType].type}}-push"></div>
            </div>
            <div class="field-item clearfix">
                <label class="field-label">{{$t("不参加推送")}}</label>
                <div class="field-anchor {{configArr[item.SaleRangeType].type}}-unpush"></div>
            </div>
            <div class="field-item clearfix">
                <label class="field-label">{{configArr[item.SaleRangeType].interval}}</label>
                ## if(item.SaleRangeType == 2) {##
                    <div>
                        <ul class="mn-checkbox-box checkbox-group b-g-clear">
                            ##_.each(dayList, function(ite, index){##
                            <li>
                                <span class="mn-checkbox-item {{item.RemindDays && item.RemindDays.indexOf(index + 1) > -1 ? 'mn-selected' : ''}}" data-index="{{index + 1}}"></span>
                                <span class="check-lb">{{ite}}</span>
                            </li>
                            ##})##
                        </ul>
                        <div class="field-anchor hour-select {{configArr[item.SaleRangeType].type}}-hour-select"></div>
                    </div>
                ## } else if(item.SaleRangeType == 3) {##
                    <div class="field-anchor week-select {{configArr[item.SaleRangeType].type}}-week-select"></div>
                    <div class="field-anchor hour-select {{configArr[item.SaleRangeType].type}}-hour-select"></div>
                ## } else if(item.SaleRangeType == 4){##
                    <span class="pre-text">{{$t("第")}}</span>
                    <div class="field-anchor day-select {{configArr[item.SaleRangeType].type}}-day-select"></div>
                    <span class="center-text">{{$t("天")}}</span>
                    <div class="field-anchor hour-select {{configArr[item.SaleRangeType].type}}-hour-select"></div>
                ## } ##
            </div>
        </div>
    </div>
##})##