define(function (require, exports, module) {
    var util = require('crm-modules/common/util'),
        Select = require('crm-widget/select/select');

    var TimeSelect = Backbone.View.extend({
        initialize: function(opts) {
            this.options = opts;
            this.options.type = this.options.type || 1;
            this.setElement(opts.element);
            this.render();
        },

        render: function() {

            var me = this, selectArr = [];
            /* type 代表类型，1天 2周 3月*/

            if(this.options.type == 1) {
                selectArr = this.hourItem();
            } else if(this.options.type == 2) {
                selectArr = this.weekItem();
            } else if(this.options.type == 3) {
                selectArr = this.monthItem();
            }
            this.select = this.listSelect({
                parentNode: this.options.parentNode,
                element: this.$el,
                arr: selectArr,
                errmsg: this.options.errmsg || $t("请选择"),
                defaultValue:  this.options.defaultValue || 1
            });

            this.select.on('change', function(item) {
                me.options.callback(item);
            });
        },

        hourItem: function() {
            var options = [],
                beginTime = FS.moment(FS.moment().format("YYYY/MM/DD"), "YYYY/MM/DD"),   //每天的0点
                tempTime = beginTime.add('minutes', 120);

            for(var i=0; i < 22; i++) {
                options.push({
                    value: tempTime.format("H"),
                    name: tempTime.format("HH:mm")
                });
                
                tempTime = beginTime.add('minutes', 60);
                
               
            };

            return options;
        },

        weekItem: function() {
            var options = [],
                beginTime = FS.moment(FS.moment().format("YYYY/MM/DD"), "YYYY/MM/DD").subtract('day', FS.moment().format('d') - 1),   //每周一的0点
                tempTime = beginTime;

            for(var i=0; i < 7; i++) {
                options.push({
                    value: tempTime.format('d'),
                    name: tempTime.format('dddd')
                });
                tempTime = beginTime.add('day', 1);
            };

            return options;
        },

        monthItem: function() {
            var options = [],
                beginTime = FS.moment(FS.moment().format("YYYY/MM/DD"), "YYYY/MM/DD").subtract('day', FS.moment().format('D') - 1),   //每月1号的0点
                tempTime = beginTime,
                maxDayCount = 28;
            for(var i=0; i < maxDayCount; i++) {
                options.push({
                    value: tempTime.format('D'),
                    name: tempTime.format('DD')
                });
                tempTime = beginTime.add('day', 1);
            };

            return options;
        },


        listSelect: function (opts) {
            var me = this, options, selectList;
            options = {
                $wrap: opts.element,// 容器
                zIndex: 1100,
                options: opts.arr,
                appendBody: false
            };
            if (!_.isUndefined(opts.defaultValue)) options.defaultValue = opts.defaultValue;
            selectList = new Select(options);

            selectList.on('change', function (v, item) {
                if (item.value == 0) {
                    util.showErrmsg(opts.element, opts.errmsg);
                } else {
                    util.hideErrmsg(opts.element);
                }
            });

            return selectList;
        },

        destory: function() {
            this.select.destroy && this.selectbarMember.destroy();
        }
    });
    module.exports = TimeSelect;
})


