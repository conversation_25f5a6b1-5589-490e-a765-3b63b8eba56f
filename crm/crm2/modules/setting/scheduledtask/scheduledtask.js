define(function (require, exports, module) {

    var VuiSdk = require('paas-vui/sdk').default;

    var SchedulerTask = Backbone.View.extend({
        initialize: function (opts) {
            this.setElement(opts.wrapper);
            this.instance = null;
        },

        render: function () {
            var me = this;
            var el = me.el;
            VuiSdk.getSchedulerTask().then(function (Scheduler) {
                me.instance = Scheduler.init(el);
            })
        },

        destroy: function () {
            this.instance && this.instance.$destroy && this.instance.$destroy();
        }
    });

    module.exports = SchedulerTask;
});