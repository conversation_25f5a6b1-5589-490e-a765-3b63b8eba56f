define(function(require,exports,module) {
    var VcrmSdk = require('vcrm/sdk');
    
    var ScrmGuide = Backbone.View.extend({
        initialize: function (opts) {
            this.setElement(opts.wrapper);
        },
    
        render: function() {
            var el = this.el;
            VcrmSdk.getComponent('scrmGuide').then(function(comp){
                let Comp = comp.default;
                Comp(el);
            })
        }
    });
  
    module.exports = ScrmGuide;
})