define(function(require, exports, module) {
    var MonitorRule = Backbone.View.extend({
        initialize: function (opts) {
            this.setElement(opts.wrapper);
        },
    
        render: function() {
            CRM.util.getRiskToken().then((res) => {
                // 自助建模
                // 蚂蚁旧链接备份 this.$el.html(`<iframe src="https://rbb.yidun.com/#/pure/v2/tools/scoremodel?token=${res.token}&ruleConfig=1" width="100%" height="100%" frameborder="0"></iframe>`)
                this.$el.html(`<iframe src="https://rbb.yidun.com/new#/pure/riskstrategy/manage?tab=Scoring&token=${res.token}" width="100%" height="100%" frameborder="0"></iframe>`)
            }, () => {})
        }
    });
  
    module.exports = MonitorRule;
  });