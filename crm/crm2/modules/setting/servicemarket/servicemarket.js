define(function (require, exports, module) {
    let Tpl = require('./template/tpl-html');
    // let Table = require('crm-modules/components/objecttable/objecttable');
    let List = require('crm-modules/page/list/list');
    
    var Mod = Backbone.View.extend({
        initialize: function (opts) {
            this.setElement(opts.wrapper);
            this.apiname = 'ServiceMarketObj';
            this.displayName = $t('crm.setting.servicemarket.edit.title');
            this.render()
        },
        render() {
            this.$el.html(Tpl());
            this.renderTable();
        },
        renderTable() {
            let me = this;
            let Table = List.extend({
                getColumns() {
                    let columns = List.prototype.getColumns.apply(this, arguments);
                    let operateCol = _.findWhere(columns, {dataType: 'operate'});
                    operateCol.width = 100;
                    return columns;
                },
                getCustomOperate(operate, data) {
                    _.each(operate, (item) => {
                        item.render_type = 'not_fold';
                        item.data = data;
                    })
                    return operate;
                },
                operateBtnClickHandle(e) {
                    let $tg = $(e.target);
                    var action = $tg.data('action');
                    let $tb = $tg.closest('.tb-cell');
                    let id = $tb.data('id');
                    if (action) {
                        if (_.contains(['Delete'], action)) {
                            let _map = {
                                Delete: $t('删除'),
                            }
                            let confirm = CRM.util.confirm($t('确定{{actionName}}吗？', {
                                actionName: _map[action] || $t('执行操作'),
                            }), null, function() {
                                confirm.hide();
                                action = '__' + action.split('_').join('') + 'Handle';
                                me[action] && me[action](e, id);
                            })
                            return;
                        }
                        action = '__' + action.split('_').join('') + 'Handle';
                        me[action] && me[action](e, id);
                    }
                },
                trclickHandle: function (data, $tr, $target, a, noRealllyClick) {
                    me.showDetail(data, this.table.getCurData());
                },
                // getOptions() {
                //     let options = List.prototype.getOptions.apply(this, arguments);
                //     // 屏蔽全字段搜索
                //     options.search && (options.search.showAllField = false);
                //     return options;
                // },
            });
            this.list = new Table({
                wrapper: $('.crm-qir-table', this.$el),
                apiname: this.apiname,
                isRenderRecordType:false,
                // isFilter: false,
                tableOptions: {
                    searchTerm: null,
                    refreshCallBack: me.refresh(),
                }
            });
            this.list.render && this.list.render();
        },
        showDetail(data, idList) {
            const me = this;
            CRM.api.show_crm_detail({
                apiName: this.apiname,
                id: data._id,
                idList: _.pluck(idList, '_id'),
                showMask: false,
                top: 56,
                callback() {
                    me.refresh();
                }
            })
        },
        __EditHandle(e, id) {
            const me = this;
            this.edit = CRM.api.edit({
                apiname: this.apiname,
                id: id,
                displayName: this.displayName,
                success() {
                    me.refresh();
                }
            })
        },
        __DeleteHandle(e, id) {
            const me = this;
            CRM.util.FHHApi({
                url: `/EM1HNCRM/API/v1/object/${this.apiname}/action/Delete`,
                data: {
                    objectDataId: id,
                    trigger_info: {
                        trigger_page: 'List'
                    }
                    // describe_api_name: this.apiname,
                    // idList: [id]
                },
                success(res) {
                    if (res.Result.StatusCode === 0) {
                        CRM.util.remind(1, $t('删除成功'));
                        me.refresh();
                        return;
                    }
                    CRM.util.alert(res.Result.FailureMessage || DEFAULT_ERROR_MSG);
                },
                error(err) {
                    CRM.util.alert(DEFAULT_ERROR_MSG);
                }
            })
        },
        refresh(){
            const me = this;
            me.list && me.list.refresh && me.list.refresh();
        },
        destroy() {
            this.list && this.list.destroy && this.list.destroy();
        }
    })
    module.exports = Mod;
})