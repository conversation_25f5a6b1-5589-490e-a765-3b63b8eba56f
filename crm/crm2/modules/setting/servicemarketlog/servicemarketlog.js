define(function (require, exports, module) {
    let Tpl = require('./template/tpl-html');
    // let Table = require('crm-modules/components/objecttable/objecttable');
    let List = require('crm-modules/page/list/list');
    
    var Mod = Backbone.View.extend({
        initialize: function (opts) {
            this.setElement(opts.wrapper);
            this.apiname = 'ServiceMarketLogObj';
            // this.displayName = $t("营销方案");
            this.render()
        },
        render() {
            this.$el.html(Tpl());
            this.renderTable();
        },
        renderTable() {
            let me = this;
            this.list = new List({
                wrapper: $('.crm-qir-table', this.$el),
                // wrapper: this.$el,
                apiname: this.apiname,
                showOperate: false,
                tableOptions: {
                    searchTerm: null,
                    showMultiple: false,
                    showFilerBtn: false,
                    showTerm: false,
                }
            });
            this.list.render && this.list.render();
        },
        refresh(){
            const me = this;
            me.list && me.list.refresh && me.list.refresh();
        },
        destroy() {
            this.list && this.list.destroy && this.list.destroy();
        }
    })
    module.exports = Mod;
})