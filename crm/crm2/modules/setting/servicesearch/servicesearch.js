/**
 * @desc 服务人员查询类型
 */
define(function(require,exports,module) {
    
	var util = require('crm-modules/common/util'),
        tpl  = require('./template/tpl-html');

    var Service = Backbone.View.extend({
        
        key: '9',
        
    	initialize: function(opts) {
            this.setElement(opts.wrapper);
    	},
        
        events: {
            'click .mn-radio-item': '_onSet',
            'click .label':         '_onLabel'
        },
        
        render: function() {
            var me = this;
            me._getConfig(function(data) {
                me.$el.html(tpl({
                    val: data
                }));
            });
        },
        
        /**
         * @desc 获取规则
         */
        _getConfig: function(cb) {
            var me = this;
            util.getConfigValue(me.key).then(function(value){
                cb && cb(value || '1');
            }, function(){
                cb && cb('1');
            })      
        },
        
        _onSet: function (e) {
            var $target = $(e.currentTarget);
            if ($target.hasClass('mn-selected')) {return false;}
            this._setConfig($target.attr('data-value'));
        },
        
        /**
         * @desc 设置规则
         */
        _setConfig: function(val) {
            var me = this;
            util.setConfigValue({
                key:   me.key,
                value: val
            }).then(function(){
                util.remind(1, $t("设置成功"));
            }, function(msg){
                me.render();
                util.remind(3, $t("设置失败"));
            })
        },
        
        // 体验优化
        _onLabel: function(e) {
            var $target = $(e.currentTarget);
            $target.closest('p').find('.mn-radio-item').trigger('click');
        }
    });

    module.exports = Service;
});