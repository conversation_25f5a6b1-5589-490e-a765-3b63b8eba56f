/**
 * @desc 服务记录类型
 */
// define(function(require,exports,module) {
// 	var Tags = require('crm-modules/setting/common/tags/tags'),
//         tpl  = require('./template/tpl-html');

//     var SalesType = Backbone.View.extend({

//     	initialize: function(opts) {

//             this.setElement(opts.wrapper);
//             this.$el.html(tpl({}));

//             this._widget = new Tags({
//                 el:   this.$('.crm-module-con'),
//                 type: 1002
//             });
//     	},

//         destroy: function() {
//             this._widget.destroy();
//         }
//     });

//     module.exports = SalesType;
// });

define(function (require, exports, module) {
	var ServiceType = Backbone.View.extend({
		initialize: function (opts) {
			this.setElement(opts.wrapper);
		},

		render: function () {
			var el = this.el;
			require.async('vcrm/sdk',function(SDK){
				SDK.getServiceTypeModule(el);
			});
		}
	});

	module.exports = ServiceType;
});