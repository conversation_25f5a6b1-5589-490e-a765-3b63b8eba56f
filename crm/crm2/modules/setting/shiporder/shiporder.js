/**
 * @description 库存管理页主入口
 */
define(function (require, exports, module) {
	const Model = require('./shiporder/model/model').default;

	module.exports = Backbone.View.extend({
		template: require('./tpl-html'),

		initialize: function (opts) {
			this.setElement(opts.wrapper);
			window.$$stock = this.model = new Model();
			this.listenTo(this.model, 'change:stockType', this.changeType);
			this.listenTo(this.model, 'change:title', this.changeTitle);
			this.listenTo(this.model, 'change:titleLabel', this.changeTitleLabel);
			this.listenTo(this.model, "change:showMask", this.renderMask);
			// this.listenTo(this.model, "change:stockStatus", this.changeTitleLabel);
			// this.listenTo(this.model, "change:isForErp", this.changeTitleLabel);
			this.listenTo(this.model, 'change:_reloadNum', this.reload);
		},

		render: function () {
			var me = this;
			this.$el.html(this.template({
				title: me.model.get('title'),
			}));
			this.model.fetchDHTStatus();
			this.reload();
		},

		reload() {
			this.model.fetchStockType();
            this.model.fetchStockOpen();
		},

		changeType: function () {
			var me = this;
			var stockType = this.model.get('stockType');
			var path = this.getPagePath(stockType);
			require.async(path, function (Page) {
				me.page = new Page({
					wrapper: me.$('.j-stock-con'),
					model: me.model,
				});
				me.page.render();
			});
		},

		getPagePath: function (stockType) {
			var maps = {
				1: 'none',
				2: 'fs',
				3: 'fs',
				4: 'fs'
			};
			return './shiporder/page/' + maps[stockType] + '/' + maps[stockType];
		},

		changeTitle: function () {
			var title = '';
			var stockType = this.model.get('stockType');
			if (+stockType === 3) {
				title = $t("ERP库存管理")
			} else {
				title = $t("纷享库存管理")
			}
			this.$('.j-setting-title').text(title);
		},

		changeTitleLabel: function () {
			var titleLabel = this.model.get('titleLabel');
			if (titleLabel) {
				this.$('.j-setting-titlelabel').removeClass('crm-s-shiporder_hide').text(titleLabel);
			} else {
				this.$('.j-setting-titlelabel').addClass('crm-s-shiporder_hide')
			}
		},

		renderMask: function () {
			var me = this;
			require.async("./shiporder/page/mask/index", function(Page) {
				me.page = new Page({
					wrapper: me.$(".b-g-con"),
					model: me.model
				});
				me.page.render();
			});
		},

		destroy: function () {
			this.page && this.page.destroy();
			this.page = null;
			this.remove();
			window.$$stock = null
		},
	});
});
