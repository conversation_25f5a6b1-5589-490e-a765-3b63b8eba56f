// out: false;
@keyframes shake {
	10%, 90% { transform: translate3d(-1px, 0, 0); }
	20%, 80% { transform: translate3d(+2px, 0, 0); }
	30%, 70% { transform: translate3d(-4px, 0, 0); }
	40%, 60% { transform: translate3d(+4px, 0, 0); }
	50% { transform: translate3d(-4px, 0, 0); }
}

.shiporder-shake{
	animation: shake 800ms ease-in-out;
}

.crm-s-shiporder {
	&_title {
		display: flex;
		height: 60px;
		align-items: center;
		.crm-tit {
			.tit-txt {
				padding: 0;
			}
		}
	}
	&_titlelabel {
		height: 20px;
		padding: 0 4px;
		margin-left: 10px;
		border: 1px solid var(--color-primary06);
		border-radius: 2px;
		color: var(--color-primary06);
		line-height: 20px;
	}
	&_hide {
		display: none;
	}
	.so-actions {
		line-height: 35px;
	}
	.shiporder-con {
		position: absolute;
		top: 0;
		left: 0;
		bottom: 0;
		right: 0;
	}
	.shiporder-con_hastab {
		top: 50px;
	}
	.shiporder-con_box {
		padding: 20px 20px 80px 20px;
	}
	.shiporder-btns {
		margin-top: 36px;
	}
	.tab-loading {
	    height: 100px;
	}
	.shiporder-action_intro {
		margin-top: 8px;
	    font-size: 12px;
	    line-height: 16px;
	    color: var(--color-neutrals11);
	}
	.shiporder-action_intro__fail {

	}
	// 设置开关
	.shiporder-switch {
		display: flex;
    	margin-top: 24px;
    	.switch_label {
    		min-width: 96px;
		    margin-right: 8px;
		    line-height: 32px;
		    font-size: 16px;
		    color: var(--color-neutrals17);
    	}
    	.oprate-btn_switch {
    		display: block;
		    position: relative;
		    overflow: hidden;
		    width: 60px;
		    height: 28px;
		    margin-top: 2px;
		    border-radius: 66px;
		    background-color: var(--color-neutrals05);
		    font-style: normal;
		    cursor: pointer;
		    &::before {
		    	content: '';
			    display: inline-block;
			    width: 24px;
			    height: 24px;
			    margin: 2px;
			    border-radius: 50%;
			    background-color: var(--color-neutrals01);
		    }
		    &::after {
		    	content: '';
				position: absolute;
			    right: 12px;
			    line-height: 28px;
			    color: var(--color-neutrals11);
			    font-size: 14px;
		    }
    	}
    	.oprate-btn_switch--on {
    		text-align: right;
    		background-color: var(--color-primary06);
    		&::after {
		    	left: 12px;
			    content: '';
			    color: var(--color-neutrals01);
			    text-align: left;
		    }
    	}
	}
	// 设置项
	.shiporder-set {
		margin-top: 16px;
	    line-height: 32px;
	    font-size: 12px;
	    color: var(--color-neutrals19);
	}
	.shiporder-set--flex {
		display: flex;
	}
	.shiporder-set--hide {
		display: none;
	}
	.shiporder-set--disabled {
		.shiporder-radio_set {
			cursor: default;
			&.on {
				.shiporder-radio_icon {
					background-position: -18px -61px;
				}
			}
		}
		.shiporder-radio_icon {
			border-color: var(--color-neutrals05) !important;
		}
		.shiporder-checkbox_icon {
			cursor: default;
		}
	}
	.shiporder-set_label {
		margin-right: 20px;
	}
	.shiporder-set_label--tip {
		font-size: 12px;
		color: var(--color-neutrals11);
	}
	// 单选项
	.shiporder-radios,
	.shiporder-text,
	.shiporder-checkboxs {
		margin-left: 40px;
	}
	.shiporder-radio_set {
		display: inline-block;
		cursor: pointer;
        &.on {
            .shiporder-radio_icon {
                border-color: var(--color-info05);
                background: url('@{imgUrl}/ico-check.png') no-repeat;
                background-position: -18px 0;
            }
        }
        &:hover .shiporder-radio_icon {
            border-color: var(--color-info05);
        }
	}
	.shiporder-radio_icon {
		display: inline-block;
        width: 14px;
        height: 14px;
        margin-right: 8px;
        border-radius: 50%;
        border: 1px solid var(--color-neutrals05);
        vertical-align: -4px;
        &:hover {
            border-color: var(--color-info05);
        }
	}
	.shiporder-radio_tips {
		margin-left: 50px;
		list-style: disc;
	}
	.shiporder-tiptext{
		margin-left: 10px;
		color: var(--color-neutrals06);
	}
	// 提示
	.shiporder-tip {
	    display: inline-block;
	    position: relative;
	    width: 16px;
	    height: 16px;
	    margin-left: 4px;
	    &:hover {
            .shiporder-tip_intro {
                display: block;
            }
        }
	}
	.shiporder-tip_icon {
	    display: block;
		height: 14px;
		border: 1px solid var(--color-neutrals14);
		border-radius: 50%;
		color: var(--color-neutrals14);
		line-height: 14px;
		text-align: center;
		font-size: 12px;
		font-style: normal;
		cursor: pointer;
	}
	.shiporder-tip_intro {
	    display: none;
	    position: relative;
	    top: -26px;
	    left: 36px;
	    z-index: 10;
	    box-sizing: border-box;
	    width: 370px;
	    padding: 8px 20px 6px;
	    box-shadow: 0 0 4px 0 rgba(0,0,0,.2);
	    border-radius: 3px;
	    background-color: var(--color-neutrals01);
	    text-align: left;
	    color: var(--color-neutrals14);
	    line-height: 22px;
	    &::before,
        &::after {
            content: '';
            position: absolute;
            top: 28px;
            border-top: 8px solid transparent;
            border-right: 8px solid var(--color-neutrals03);
            border-bottom: 8px solid transparent;
        }
        &::before {
            left: -10px;
        }
        &::after {
            left: -8px;
            border-right-color: var(--color-neutrals01);
        }
	    b {
	    	font-size: 13px;
	    }
	    p {
	    	margin-top: 3px;
		    margin-bottom: 4px;
		    font-size: 12px;
	    }
	}
	// 复选框
	.shiporder-checkbox_icon {
		display: inline-block;
	    width: 15px;
	    height: 15px;
	    margin-right: 8px;
	    background: url(../images/ico-check.png) 0 -30px no-repeat;
	    vertical-align: -3px;
	    cursor: pointer;
	}
	.shiporder-checkbox_icon--on {
		background-position: 0 0;
	}
	// 下拉单选
	.shiporder-singleselect_con {
		width: 100px;
	}
	.crm-module-con {
		overflow: auto;
    	padding-bottom: 100px;
	}
	// 引导页
	.shiporder-intr_wrapper {
		position: relative;
		padding: 90px 0;
		min-height: 440px;
	}
	.shiporder-intr_content {
		display: inline-block;
		position: relative;
		left: 50%;
		transform: translateX(-50%);
	}
	.shiporder-intr_tips {
		margin-bottom: 40px;
		font-size: 14px;
		line-height: 20px;
		color: var(--color-neutrals17);
		letter-spacing: 0;
		text-align: center;
	}
	.shiporder-intr_selection {
		display: inline-flex;
		margin-right: -20px;
	}
	.shiporder-intr_selection-item {
		background: var(--color-neutrals01);
		border: 1px solid var(--color-neutrals03);
		border-radius: 3px;
		border-radius: 3px;
		width: 350px;
		min-height: 380px;
		margin-right: 20px;
		cursor: pointer;
		&:hover {
			border: 1px solid var(--color-primary06);
			box-shadow: 0 0 16px 0 rgba(0,0,0,0.10);
		}
	}
	.shiporder-intr_selection-item.active {
		position: relative;
		border: 1px solid var(--color-primary06);
		box-shadow: 0 0 16px 0 rgba(0,0,0,0.10);

		&::before {
			content: '';
			display: block;
			position: absolute;
			left: 0;
			top: 0;
			width: 0;
			height: 0;
			border-width: 22px 22px 0 0;
			border-style: solid;
			border-color: var(--color-primary06) transparent transparent transparent;
		}
	}
	.shiporder-intr_selection-item-icon {
		position: relative;
		left: 50%;
		transform: translateX(-50%);
		display: inline-block;
		width: 170px;
		height: 168px;
		margin: 0 auto;
		img {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translateX(-50%) translateY(-50%);
		}
	}
	.shiporder-intr_selection-item-title {
		margin-top: 36px;
		margin-bottom: 20px;
		font-size: 16px;
		color: var(--color-neutrals17);
		text-align: center;
		line-height: 16px;
	}
	.shiporder-intr_selection-item-tips {
		display: inline-block;
		width: 307px;
		margin: 0 20px;
		li {
			position: relative;
			padding-left: 10px;
			font-size: 14px;
			color: var(--color-neutrals10);
			line-height: 20px;
			&::before {
				content: '';
				display: block;
				position: absolute;
				left: 0;
				top: 8px;
				width: 4px;
				height: 4px;
				border-radius: 50%;
				background: var(--color-neutrals10);
			}
		}
	}
	.shiporder-intr_btn-next {
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: 74px;
		height: 32px;
		background: var(--color-primary06);
		-webkit-border-radius: 3px;
		border-radius: 3px;
		color: white;
		border: none;
		cursor: pointer;
	}
	.shiporder-setting_wrapper {
		padding: 18px 20px;
	}

	.shiporder-setting_intr {
		padding: 16px 18px;
		margin-bottom: 20px;
		background: var(--color-neutrals03);
		border-radius: 4px;
	}

	.shiporder-setting_intr-title {
		font-size: 16px;
		color: var(--color-neutrals17);
	}

	.shiporder-setting_intr-tips {
		line-height: 24px;
	}

	.shiporder-setting_options-title {
		margin-bottom: 24px;
		font-size: 14px;
		color: var(--color-neutrals17);
	}

	.shiporder-setting_options-item {
		margin-bottom: 26px;
		.shiporder-checkbox {
			display: flex;
			align-items: center;
			input[type="checkbox"] {
				display: inline-block;
				width: 16px;
				height: 16px;
				margin-right: 7px;
				cursor: pointer;
			}
			.checkbox-disabled {
				cursor: not-allowed !important;
			}
		}
	}
	.shiporder-setting_options-item.child-option {
		padding-left: 23px;
	}

	.shiporder-setting_options-tips {
		padding-left: 23px;
		font-size: 12px;
		color: var(--color-neutrals10);
		line-height: 14px;
	}

	.shiporder-setting_btns {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 140px;
	}

	.shiporder-setting_btn-back {
		font-size: 14px;
		color: var(--color-neutrals10);
		cursor: pointer;
	}

	.shiporder-setting_btn-confirm {
		display: inline-block;
		width: 88px;
		height: 34px;
		margin-left: 24px;
		background: var(--color-primary06);
		border-radius: 3px;
		line-height: 34px;
		text-align: center;
		color: white;
		cursor: pointer;
	}
	// 基本信息
	.shiporder-base-info_wrapper {
		.shiporder-base-info_item {
			display: flex;
			padding: 20px 30px 20px 0;
			margin-left: 20px;
			border-bottom: 1px dotted var(--color-neutrals04);
			cursor: pointer;
		}
		.shiporder-base-info_item.disabled-item {
			cursor: not-allowed;
		}
		.shiporder-base-info_item.active {
			background: var(--color-neutrals03);
			position: relative;
			&::before {
				content: '';
				position: absolute;
				left: -20px;
				top: 0;
				width: 20px;
				height: 100%;
				background: var(--color-neutrals03);
			}
		}
		.shiporder-base-info_item-title {
			width: 145px;
			font-size: 14px;
			color: var(--color-neutrals14);
		}
		.shiporder-base-info_item-content {
			flex: 1;
			font-size: 14px;
			color: var(--color-neutrals17);
			p{
				margin-right: 25px;
			}
		}
		.shiporder-base-info_item-btn {
			font-size: 14px;
			color: var(--color-info05);
			cursor: pointer;
		}
		.btn-disabled {
			color: var(--color-neutrals10);
			cursor: not-allowed;
		}
		.shiporder-base-info_item-switch {
			display: block;
			position: relative;
			overflow: hidden;
			width: 38px;
			height: 20px;
			border-radius: 66px;
			background-color: var(--color-neutrals05);
			font-style: normal;
			cursor: pointer;
			&::before {
				content: '';
				display: inline-block;
				width: 16px;
				height: 16px;
				margin: 2px;
				border-radius: 50%;
				background-color: var(--color-neutrals01);
			}
		}
		.shiporder-base-info_item-switch.switch-on{
			text-align: right;
			background-color: var(--color-primary06);
		}
		.shiporder-base-info_item-switch.switch-disabled{
			opacity: .5;
			cursor: not-allowed;
		}
		.shiporder-base-info_item-tips {
			font-size: 14px;
			color: var(--color-neutrals10);
			line-height: 22px;
		}
		.shiporder-base-info_float-setting-title {
			min-width: 62px;
			line-height: 36px;
		}
		.shiporder-base-info_setting-detail {
			display: none;
		}
		.shiporder-base-info_float-setting-btns {
			display: flex;
			align-items: center;
			.setting-btn {
				display: inline-block;
				width: 68px;
				height: 30px;
				margin-right: 36px;
				text-align: center;
    			line-height: 30px;
				cursor: pointer;
				box-sizing: border-box;
				border-radius: 3px;
			}
			.btn-confirm {
				color: white;
				background: var(--color-warning05);
			}
			.btn-cancel {
				background: var(--color-neutrals01);
				border: 1px solid var(--color-neutrals06);
				color: var(--color-neutrals10);
			}
		}
		.space-between {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
	}
	.shiporder-base-info_float-setting-item {
		margin-bottom: 8px;
		display: flex;
		cursor: pointer;
		&:hover {
			.shiporder-base-info_radio {
				border-color: var(--color-info05);
			}
		}
		label {
			display: flex;
			align-items: center;
			position: relative;
		}
		.shiporder-tip {
			margin-top: 3px;
		}
		.shiporder-tip_intro::after,
		.shiporder-tip_intro::before {
			top: 10px;
		}
		.radio-option {
			margin-top: 8px;
			padding-left: 20px;
			cursor: pointer;
			input {
				margin-right: 8px;
			}
		}

		&__href {
			margin-left: 20px;
			color: var(--color-info04);

			&:hover {
				text-decoration: underline;
			}
		}

		.fx-radio-icon {
			top: 48% !important;
		}
	}

	.shiporder-base-info_float-setting-item__desc {
		font-size: 14px;
		color: var(--color-neutrals10);
		margin-bottom: 20px;
		padding-left: 20px;
	}

	.shiporder-base-info_float-setting-item--child {
		margin-left: 20px;
	}

	.shiporder-base-info_float-setting-item--no-margin {
		margin: 0
	}

	.shiporder-base-info_order-delivery-mode {
		display: flex;
		margin: 0 0 20px 20px;
		flex-wrap: wrap;
	}

	.shiporder-base-info_order-delivery-mode_item {
		margin: 0 20px 10px 0;
	}

	.j-order-delivery-mode_edit {
		color: var(--color-info04);
		margin-left: 10px;
		cursor: pointer;
	}

	.shiporder-base-info_float-setting-content {
		input {
			display: inline-block;
			width: 68px;
			height: 30px;
			outline: none;
			margin: 0 6px;
			text-align: center;
		}
		input[type=number]::-webkit-inner-spin-button,
		input[type=number]::-webkit-outer-spin-button {
			-webkit-appearance: none;
			margin: 0;
		}
		.decimal-tips {
			font-size: 12px;
			color: var(--color-neutrals10);
		}
	}

	.shiporder-base-info_radio {
		cursor: pointer;
	}

	.shiporder-base-info_float-setting-radio {
		margin-right: 8px;
	}

	// 插件管理
	.shiporder-manage_wrapper {
		padding: 20px 22px;
		.shiporder-manage_category {
			margin-bottom: 32px;
		}
		.shiporder-manage_category-title{
			font-size: 14px;
			color: var(--color-neutrals14);
			line-height: 20px;
			// margin-bottom: 12px;
		}
		.shiporder-manage_category-cards {
			display: flex;
			flex-wrap: wrap;
			width: 100%;
		}
		.shiporder-manage_category-card {
			box-sizing: border-box;
			// width: 352px;
			width: 390px;
			margin-top: 12px;
			padding: 18px 26px 20px 20px;
			margin-right: 10px;
			background: var(--color-neutrals01);
			border: 1px solid var(--color-neutrals04);
			box-shadow: 0 0 8px 0 rgba(0,0,0,0.05);
			display: flex;
			border-radius: 4px;
			flex-direction: column;
			min-height: 164px;

			.oprate-btn_switch {
				display: block;
				position: relative;
				overflow: hidden;
				width: 38px;
				height: 20px;
				border-radius: 66px;
				background-color: var(--color-neutrals05);
				font-style: normal;
				cursor: pointer;
				&::before {
					content: '';
					display: inline-block;
					width: 16px;
					height: 16px;
					margin: 2px;
					border-radius: 50%;
					background-color: var(--color-neutrals01);
				}
			}
			.oprate-btn_switch--on {
				text-align: right;
				background-color: var(--color-primary06);
			}
			.oprate-btn_switch--disabled {
				opacity: .5;
				cursor: not-allowed;
			}
		}
		.shiporder-manage_category-card-title {
			margin-bottom: 6px;
			font-size: 16px;
			color: var(--color-neutrals17);
			line-height: 22px;
		}
		.shiporder-manage_category-card-content {
			font-size: 13px;
			color: var(--color-neutrals10);
		}
		.shiporder-manage_category-card-content-font-black{
			color: var(--color-neutrals19);
		}
		.shiporder-manage_category-card-footer {
			display: flex;
			justify-content: space-between;
			align-items: center;
			width: 100%;
			margin-top: auto;
		}
		.shiporder-manage_category-card-options {
			display: flex;
		}

		.shiporder-manage_category-card-options.j-cf-config {
			margin-left: auto;
		}

		.shiporder-manage_category-card-options.kc-left {
			margin-left: 0 !important;
		}

		.shiporder-manage_category-card-option{
			margin-right: 14px;
			font-size: 14px;
			cursor: not-allowed;
			color: var(--color-neutrals10);
		}
		.shiporder-manage_category-card-option.active {
			color: var(--color-info05);
			cursor: pointer;
		}
	}

	// 高级功能
	.shiporder-advance_wrapper {
		padding: 0 30px 0 20px;
		.shiporder-advance_item {
			display: flex;
			padding: 20px 0;
			border-bottom: 1px dotted var(--color-neutrals04);
		}
		.shiporder-advance_item-title {
			display: flex;
			width: 118px;
			font-size: 14px;
			color: var(--color-neutrals14);
			line-height: 20px;
			span{
				width: 90px;
			}
			.shiporder-tip {
				margin-top: 2px;
			}
			.shiporder-tip_intro::after,
			.shiporder-tip_intro::before {
				top: 10px;
			}
		}
		.shiporder-advance_item-content {
			flex: 1;
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		.shiporder-advance_item-content-container {
			flex: 1;
			.shiporder-advance_item-content {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 20px 0;
				.tips {
					font-size: 12px;
					color: var(--color-neutrals10);
				}
			}
			.shiporder-advance_item-child {
				padding-left: 2em;
			}
		}
		.shiporder-advance_item-info {
			font-size: 14px;
			color: var(--color-neutrals17);
			line-height: 20px;
			margin-right: 20px;
		}
		.shiporder-advance_item-info-title {
			margin-bottom: 8px;
			.icon-status {
				vertical-align: text-bottom;
			}
			.info-title {
				margin-right: 24px;
			}
		}
		.shiporder-advance_item-fn {
			width: 113px;
			.tips {
				padding-left: 24px;
			}
		}
		.width-border {
			border-bottom: 1px dotted var(--color-neutrals04);
		}
		.minus-20 {
			margin-top: -20px;
		}
		.shiporder-advance_item-btn{
			position: relative;
			box-sizing: border-box;
			width: 100px;
			padding: 0;
			.crm-stock-options {
				display: none;
				position: absolute;
				left: -30px;
				margin-top: 10px;
				border: 1px solid var(--color-neutrals04);
				border-radius: 4px;
			}
			.crm-stock-option {
				padding: 10px 15px;
				cursor: pointer;
				&:hover {
					background-color: var(--color-primary06);
					color: white;
				}
			}
		}
		.icon-status {
			display: inline-block;
			width: 14px;
			height: 14px;
			margin-right: 8px;
			border-radius: 50%;
			vertical-align: middle;
		}
		.icon-selected {
			background: url('@{imgUrl}/ico-yesno.png');
			background-position: 2px;
			background-size: 21px 10px;
			background-repeat: no-repeat;
			border: 1px solid rgb(146, 204, 96);
		}
		.icon-minus {
			background: url('@{imgUrl}/group-21.png');
			background-size: contain;
		}
		.icon-tips {
			display: inline-block;
			height: 14px;
			width: 14px;
			margin-left: 3px;
			border: 1px solid var(--color-neutrals14);
			border-radius: 50%;
			color: var(--color-neutrals14);
			line-height: 14px;
			text-align: center;
			font-size: 12px;
			font-style: normal;
			cursor: pointer;
		}
		.crm-btn-disabled {
			background: var(--color-neutrals04);
			border: 1px solid var(--color-neutrals05);
			border-radius: 4px;
			border-radius: 4px;
		}
	}

}

// 自动收货弹窗
.crm-c-dialog-receive-content {
	.shiporder-base-info_float-setting-item {
		margin-bottom: 10px;
		cursor: pointer;
		&:hover {
			.shiporder-base-info_radio {
				border-color: var(--color-info05);
			}
		}

		label {
			display: flex;
			align-items: center;
		}
	}
	.shiporder-base-info_float-setting-content {
		input {
			display: inline-block;
			width: 68px;
			height: 30px;
			outline: none;
			background: var(--color-neutrals01);
			border: 1px solid var(--color-neutrals06);
			border-radius: 4px;
			margin: 0 6px;
			text-align: center;
		}
		input[type=number]::-webkit-inner-spin-button,
		input[type=number]::-webkit-outer-spin-button {
			-webkit-appearance: none;
			margin: 0;
		}
		.decimal-tips {
			font-size: 12px;
			color: var(--color-neutrals10);
		}
	}

	.shiporder-base-info_radio {
		cursor: pointer;
	}

	.shiporder-base-info_float-setting-radio {
		margin-right: 8px;
	}

	.oprate-btn_switch {
		display: block;
		position: relative;
		overflow: hidden;
		width: 38px;
		height: 20px;
		border-radius: 66px;
		background-color: var(--color-neutrals05);
		font-style: normal;
		cursor: pointer;
		&::before {
			content: '';
			display: inline-block;
			width: 16px;
			height: 16px;
			margin: 2px;
			border-radius: 50%;
			background-color: var(--color-neutrals01);
		}
	}
	.oprate-btn_switch--on {
		text-align: right;
		background-color: var(--color-primary06);
	}
	.oprate-btn_switch--disabled {
		opacity: .5;
		cursor: not-allowed;
	}
}

// 库存停用弹窗
.crm-c-dialog-stopstock {
	.btn-disabled {
		opacity: .65;
		cursor: not-allowed;
		pointer-events: none;
	}
	.stopstock-content {
		padding: 20px 0;
		p {
			margin-top: 10px;
			&:first-child {
				margin-top: 0;
			}
		}
	}
	.stopstock-checkbox {
		display: inline-block;
		width: 15px;
		height: 15px;
		margin-right: 10px;
		background: url("@{imgUrl}/ico-check.png") 0 -30px no-repeat;
		vertical-align: -3px;
		cursor: pointer;
		&.on {
			background-position: 0 0;
		}
	}
	.stopstock-countdown {
		margin-right: 6px;
	}
}
.receive-dialog-content {
	.shiporder-base-info_float-setting-title,
	.shiporder-base-info_float-setting-item {
		margin-bottom: 10px;
	}
	.shiporder-base-info_float-setting-options {
		padding-left: 20px;
	}
}

// 蒙层
.crm-shiporder-mask {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(255,255,255,.85);
	z-index: 99;
	.crm-shiporder-mask_wrapper {
		z-index: 99;
		position: relative;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
	}
	.crm-shiporder-mask_content {
		display: inline-block;
		position: absolute;
		left: 50%;
		top: 40%;
		transform: translateX(-50%) translateY(-50%);
		text-align: center;
	}
	.crm-shiporder-mask_loading {
		position: relative;
		display: inline-block;
		width: 46px;
		height: 46px;
		border-radius: 50%;
		border: 1px solid var(--color-warning05);
		animation: loading 1.2s linear infinite;
		&::after {
			content: '';
			display: block;
			position: absolute;
			left: 50%;
			bottom: 0;
			width: 10px;
			height: 10px;
			background: white;
			transform: translateX(-50%) translateY(5px);
		}
	}
	.crm-shiporder-mask_tips {
		margin-top: 10px;
		color: var(--color-warning05);
		text-align: center;
	}

	@keyframes loading {
 		from {
			transform: rotate(0);
		}
		to {
			transform: rotate(360deg);
		}

	}
}

.app-custom-function-guide {
	.el-dialog__body {
		font-size: 18px;
		color: var(--color-neutrals17);
	}

	&__pic {
		max-width: 50%;
		margin: 20px auto;
		display: block;
	}

	h5, p {
		text-align: center;
	}

	p {
		margin: 20px 0;
	}

	h6 {
		margin-top: 100px;
	}

	h6:first-of-type {
		margin-top: 0;
	}
}

.auto-confirm-receipt-dialog {
	&__number {
		width: 64px;

		.el-input__inner {
			height: 30px;
			line-height: 30px;
		}
	}

	.el-radio {
		margin: 10px;
	}

	&__radio-tips {
		color: var(--color-neutrals10);
		font-size: 12px;
		margin-left: 40px;
	}

	&__tab{
		.el-radio-group {
			display: flex;
		}

		&__content {
			padding: 10px;
			background: var(--color-neutrals02);

			&__inner{
				width: 380px
			}

			&__row{
				display: flex;
				justify-content: space-between;
				margin: 10px;
			}
		}

	}
}


.rts-dialog{
	.fx-radio-group{
		margin-left: 22px;
	}
	
	.rts-dialog__radio{
		margin-top: 10px;
		white-space: normal !important;
	}
    .rts-dialog__apl{
		//width: 300px;
		//padding-left: 26px;
		//font-size: 14px !important;
		width: 300px;
	}
	.rts-dialog__apl__row{
		display: flex;
		justify-content: space-between;
		margin-bottom: 4px;
		&:first-of-type{
			margin-top: 4px;
		}
	}
	.rts-dialog__apl__btn{
		cursor: pointer;
	}
}

.interconnection-setting-dialog {
	.interconnection-setting-dialog-up,
	.interconnection-setting-dialog-down {
		min-height: 100px;
	}

	.interconnection-setting-dialog-up {
		.el-radio {
			margin: 10px 0 0 10px;
		}
	}

	.interconnection-setting-dialog-down {
		&--item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 10px;

			.el-select {
				width: 300px;
			}
		}

		&--item:first-of-type {
			margin-top: 0;
		}
	}
}


.multiOrderDeliveryDialog{
	p{
		line-height: 32px;
		font-size: 13px;
		color: var(--color-neutrals19);
		span{
			display: inline-block;
			width: 25px;
			color: var(--color-danger07);
		}
		input{
			vertical-align: -1.5px;
		}
	}
	.inquire-list-btn {

		margin: 20px 70px;

		a {
			color: blue;
			text-decoration: none
		}
	}
}


// 智能补货 样式
.smartReplenishment_div{
    font-size: 14px;
    color: var(--color-neutrals15);
    .smartReplenishment_illustrate{
        padding: 16px;
        background: var(--color-neutrals03);
        span{
            font-weight: 400;
            color: var(--color-neutrals19);
        }
    }
    .smartReplenishment_type{
        margin-top: 20px;
        .title{
            color: var(--color-neutrals15);
            display: inline-block;
            width: 127px;
        }
		.APLDiv{
			background: var(--color-neutrals02);
			margin-top: 10px;
			padding: 10px;
			.spl_span{
				cursor: pointer;
				color: var(--color-info06);
			}
			.APL-detail{
				.APL-name{
					color: var(--color-neutrals19);
					display: flex;
					justify-content: space-between;
					span{
						cursor: pointer;
						color: var(--color-info06);
					}
				}
				.APL-dec{
					margin-top: 10px;
				}
			}
		}

    }
    .smartReplenishment_default{
        margin-top: 10px;
        padding: 23px 16px;
        background: var(--color-neutrals02);
        border-radius: 2px;
        .left_name{
            display: inline-block;
            width: 127px;
        }
        .line{
            height: 1px;
            background-color:var(--color-neutrals05) !important;
            margin-top: 23px;
            margin-bottom: 23px;
        }
        .smartReplenishment_day{
            margin:0 8px;
            width: 50px;
            display: inline-block;
        }
		.shiporder-tip_icon{
			display: inline-block;
			width: 14px;
            height: 14px;
            border: 1px solid var(--color-neutrals14);
            border-radius: 50%;
            color: var(--color-neutrals14);
            line-height: 14px;
            text-align: center;
            font-size: 12px;
            font-style: normal;
			margin-left: 5px;
            cursor: pointer;
		}
    }
    .smartReplenishment_APL{
        margin-top: 10px;
        padding: 23px 16px;
        background: var(--color-neutrals02);
        border-radius: 2px;
        .apl-con{
            display: inline-block;
            min-width: 250px;
            .apl_top{
                span{
                    float: right;
                }
                .apl_txt{
                    float: left;
                }
                .aplBtn{
                    cursor: pointer;
                    padding: 0 5px;
                }
            }
            div{
                line-height: 30px;
            }
        }


    }
}

.exchange-setting-dialog {
	.text {
		margin-bottom: 20px;
	}

	.fx-radio {
		margin-bottom: 16px !important;
		white-space: normal !important;
	}
}

.freeze-inventory-adjustment-dialog {
	.notice-text {
		margin-bottom: 12px;
	}
	.main-content {
		.text-container {
			margin-left: 1em;

			.text-item {
				margin-bottom: 4px;
			}

			.text-confirm {
				margin-top: 12px;
			}
		}
	}
}

.shiporder-base-info {
	&_warning-setting {
		&-row {
			margin-bottom: 8px;
		}

		&-list {
			display: flex;
			flex-wrap: wrap;

			&-item {
				margin-right: 8px;
				position: relative;

				&__close {
					display: none;
					position: absolute;
					top: -6px;
					right: -10px;
					width: 12px;
					height: 12px;

					&::before,
					&::after {
						content: '';
						position: absolute;
						top: 50%;
						left: 50%;
						width: 1px;
						height: 100%;
						background-color: gray;
						transform-origin: center;
					}

					&::before {
						transform: translate(-50%, -50%) rotate(45deg);
					}

					&::after {
						transform: translate(-50%, -50%) rotate(-45deg);
					}
				}

				&:hover .shiporder-base-info_warning-setting-list-item__close{
					display: inline-block;
				}
			}

			&-btn {
				color: var(--color-info05);
			}
		}

	}
}
