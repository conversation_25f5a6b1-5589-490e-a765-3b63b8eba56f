# ![纷享销客][fxiaoke]库存管理

## 注意事项：

1. `shiporder` 下所有样式，需添加命名空间 `.crm-s-shiporder`。

## 文件结构

```
.
├── /components/                # 公用组件
│   └── components.js
├── /model/                     # 源文件
│   ├── api.js
│   ├── format.js
│   └── model.js
├── /view/                      # 页面类型视图
├── /layout/                    # 页面布局
├── /page/                      # 第三方依赖库
│   └── /none/
│       └── none.js             # 未开启对应库存
│── READMD.md                   # 文档
│── tpl.html                    # 项目包说明
└── index.js                    # 入口文件
```

## 公共组件


## 配置格式

```javascript
var page = {
    topInfo: {
        messages: [] || '',
        hasService: true, 
    },
    layouts: [
        {
            buttons: [{
                label: '',
                action: '',             // 按钮触发的action
                show: true,             // 是否显示该按钮
            }],
            switch: {
                action: '',             // 开关触发事件
                label: '',              // 开关描述
                canSwitch: false,       // 开关是否支持关闭
                options: [{
                    type: 'success',    // 开关状态 none 未开启 success 已开启 close 已停用 error 开启错误
                    value: 1,
                }],
            },
            config: {
                edit: false,            // 是否可编辑filed
                setting: true,          // 是否显示相应的设置
            },
            fields: [
                {
                    name: 'name',       // model对应字段
                    label: '',          // 字段描述
                    type: 'radio',      // 字段类型
                    isHidden: false,    // 是否隐藏
                    isReadonly: false,  // 是否只读
                    // 额外参数
                },
            ],
        },
    ],
}
```