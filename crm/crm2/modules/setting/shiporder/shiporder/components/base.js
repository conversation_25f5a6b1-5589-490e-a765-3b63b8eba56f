/**
 * @description 通用操作，model 通信
 */
define(function (require, exports, module) {
	var Base = Backbone.View.extend({
		initialize: function () {
			this.super = Base.prototype;
			this.options = _.extend({}, Base.prototype.options, this.options);
			this.fieldAttr = this.getAttr(this.options.name);
			this._addEvents();
		},

		// 获取对应的数据
		get: function (key) {
			return this.model.get(key);
		},
		// 设置对应的数据
		set: function (key, value) {
			this.model.set(key, value);
		},

		getAttr: function(name) {
            return this.get('fields')[name] || {};
        },

		isReadonly: function () {
			return this.fieldAttr.isReadonly;
		},

		isHidden: function () {
			return this.fieldAttr.isHidden;
		},

		disabeld: function () {
			this.fieldAttr.isReadonly = true;
		},

		setStatus: function () {
			if (this.isHidden()) {
				this.$el.addClass('shiporder-set--hide');
			} else {
				this.$el.removeClass('shiporder-set--hide');
			}

			if (this.isReadonly()) {
				this.$el.addClass('shiporder-set--disabled')
			} else {
				this.$el.removeClass('shiporder-set--disabled');
			}
		},

		_addEvents: function () {
			this.listenTo(this.model, 'change:' + this.options.name, this.changeValue);
		},

		changeValue: function () {

		},

		/**
		 *@desc 销毁方法 销毁一些公用的东西 子组件可覆盖进行更细致的销毁
		 */
		destroy: function () {
			this.$el.off();
			this.stopListening();
			this.model = null;
			this.$el.empty();
			this.$el = this.el = this.options = null;
		}
	});

	module.exports = Base;
});
