/**
 * @description 复选框
 * 复选框设置项的值，默认为 1 为 选中， 2 为 不选
 * 如果存在不一致映射时，需要在 set/get 中进行相应的映射处理
 */
define(function (require, exports, module) {
	var Base = require('../base');

	module.exports = Base.extend({
		template: require('./tpl-html'),

		events: {
			'click .j-shiporder-checkbox': 'selectHandle',
		},

		render: function () {
			var attr = this.fieldAttr;
			var data = this.getDatas();
			this.$el.html(this.template({
				label: attr.label,
				options: attr.options,
				data: data,
			}));
		},

		_addEvents: function () {
			var me = this;
			var dataList = [];
			_.each(this.fieldAttr.options, function (item) {
				dataList.push(item.name);
				me.listenTo(me.model, 'change:' + item.name, me.changeValue(item.name));
			});
		},

		getDatas: function () {
			var me = this;
			var options = this.fieldAttr.options;
			var data = {};
			_.each(options, function (item) {
				var name = item.name;
				data[name] = me.getData(name);
			});
			return data;
		},

		// 将模型数据映射到组件格式，默认 1 为 选中， 2 为 不选
		formatData: function (value, reverseCal) {
			return value !== 1 ? 2 : 1;
		},

		// 获取对应的数据
		getData: function (key) {
			return this.formatData(this.get(key));
		},
		// 设置对应的数据
		set: function (key, value) {
			this.model.set(key, this.formatData(value, true));
		},

        selectHandle: function (e) {
        	if (this.isReadonly() || this.isHidden()) return;
        	
        	var $target = $(e.currentTarget);
        	var name = $target.attr('data-name');
        	var value = $target.hasClass('shiporder-checkbox_icon--on') ? 2 : 1;
        	this.set(name, value);
        	this.setValue(name, value, $target);
        },

        setValue: function (name, value, $tg) {
        	var $target = $tg || this.$('[data-name='+name+']');
    		$target[value === 1 ? 'addClass' : 'removeClass']('shiporder-checkbox_icon--on');
        },

        changeValue: function (name) {
        	var me = this;
        	return function () {
        		me.setValue(name, me.getData(name));
        	};
        },

        destroy: function () {
        	this.dataList = null;
        	this.super.destroy.call(this);
        },
	});
});
