<div class="shiporder-set">
	<label class="shiporder-set_label">{{{{-obj.label}}}}</label>
	<div class="shiporder-checkboxs">
		## _.each(obj.options, function (item) { ##
			<div class="shiporder-checkbox">
				<i class="j-shiporder-checkbox shiporder-checkbox_icon {{obj.data[item.name] == 1 ? 'shiporder-checkbox_icon--on' : ''}}" data-name="{{item.name}}"></i>
				<span>{{{{-item.label}}}}</span>
			</div>
		## }) ##
	</div>
</div>