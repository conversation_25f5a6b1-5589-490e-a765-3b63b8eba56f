/**
 * @description 单选
 */
define(function (require, exports, module) {
	var Base = require('../base');

	module.exports = Base.extend({
		template: require('./tpl-html'),

		events: {
			'click .shiporder-radio_set': 'selectHandle',
		},

		render: function () {
			var attr = this.fieldAttr;
			var value = this.get(this.options.name);
			this.$el.html(this.template({
				label: attr.label,
				options: attr.options,
				tip: attr.tip,
				value: value,
				parseTipHtml: function (list) {
					var tipHtml = '';
					_.each(list, function (item) {
						if (item.type === 'b') {
							tipHtml += ('<b>' + _.escape(item.label) + '</b>');
						} else {
							tipHtml += ('<p>' + _.escape(item.label) + '</p>');
						}
					});
					return tipHtml;
				},
			}));
		},

        selectHandle: function (e) {
        	if (this.isReadonly() || this.isHidden()) return;
        	
        	var name = this.options.name;
        	var $target = $(e.currentTarget);
        	var value = $target.attr('data-value') - 0;
        	var oValue = this.get(name);
        	if (value !== oValue) {
        		this.set(name, value);
        	}
        },

        setValue: function (value) {
        	var name = this.options.name;
        	this.$('.shiporder-radio_set').removeClass('on');
        	this.$('[data-value='+value+']').addClass('on');
        },

        changeValue: function () {
        	var value = this.get(this.options.name);
        	this.setValue(value);
        },
	});
});