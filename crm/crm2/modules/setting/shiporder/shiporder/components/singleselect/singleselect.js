/**
 * @description 单选
 */
define(function (require, exports, module) {
	var Base = require('../base');
	var Select = require('crm-widget/select/select');

	module.exports = Base.extend({
		template: require('./tpl-html'),

		render: function () {
			var attr = this.fieldAttr;
			this.$el.html(this.template({
				label: attr.label,
				options: attr.options,
			}));
			this.initSelect();
		},

		initSelect: function () {
			var me = this;
			var name = this.options.name;
			this.select && this.select.destroy && this.select.destroy();
        	this.select = new Select({
				$wrap: me.$('.j-shiporder-singleselect'),
				width: 100,
				options: me.getSelectOptions(),
			});
			this.select.setValue(this.get(name));
			this.select.on('change', function (val) {
				me.set(name, val);
			});
		},

		getSelectOptions: function () {
			var range = this.fieldAttr.range;
			var options = [];
			for(var i = range[0]; i <= range[1]; i++) {
				options.push({
					name: i,
					value: i,
				});
			}
			return options;
		},

		setTip: function (text) {
			this.$('.shiporder-action_intro').text(text);
		},

        setValue: function (value, noSetModel) {
        	// if (this.isReadonly() || this.isHidden()) return;
        	this.select && this.select.setValue(value);
        	if (noSetModel) {
        		this.set(this.options.name, value);
        	}
        },

        changeValue: function () {
        	this.setValue(this.get(this.options.name), true);
        },

        destroy: function () {
        	this.select && this.select.destroy && this.select.destroy();
        	this.select = null;
        	this.super.destroy.call(this);
        },
	});
});