/**
 * @description 模块布局
 */
define(function (require, exports, module) {
    var components = require('../components/components');
    var SwitchInfo = require('./switch/switch');
    var crmUtil = FS.crmUtil;

    var View = Backbone.View.extend({
        template: require('./tpl-html'),

        mycomponents: {},

        dataEvents: {},

        events: {
            'click .j-reload-config': 'reloadHandle', // 查询失败重试
            'click .j-shiporder-service': 'openQx',
            'click [data-action]': 'doActionHandle',
        },

        initialize: function (options) {
            this.super = View.prototype;
            this.options = _.extend({}, this.options, options);
            this.events = _.extend({}, this.events, this.events);
            this.forms = {};
            this.widgets = {};
            this.model = this.options.model;
            this._addEvents();
        },

        render: function () {
            var describe = this.getDescribe();
            this.$el.html(this.template(describe));
            this.initWidgest();
            this.initForm();
        },
        // 获取页面布局描述信息
        getDescribe: function () {
            var options = this.options;
            this.setFields(options.layout && options.layout.fields);
            return {
                loading: false,
                errCode: 0,
                topInfo: options.topInfo,
                layout: options.layout,
            };
        },
        // 设置表单设置相关字段
        setFields: function (list) {
            var fields = {};
            _.each(list, function (item) {
                fields[item.name] = item;
            });
            this.model.set('fields', fields);
        },
        setField: function (name, data) {
            var fields = this.model.get('fields') || {};
            fields[name] && _.extend(fields[name], data);
		},
		getField: function (name) {
			var fields = this.model.get('fields') || {};
			return fields[name];
		},
        // 初始化页面相关组件
        initWidgest: function () {
            this.initSwitch();
        },
        // 初始化设置开关
        initSwitch: function () {
            var me = this;
            var layout = this.options.layout || {};
            if (!layout.switchInfo) return;

            this.widgets.switchInfo && this.widgets.switchInfo.destroy();
            this.widgets.switchInfo = new SwitchInfo(_.extend({
                el: me.$('.j-action-switch').get(0),
            }, layout.switchInfo));
            this.widgets.switchInfo.render();
            this.widgets.switchInfo.on('switch', function (action, $el) {
                me.switchHandle(action, $el);
            });
        },
        // 开关设置处理
        switchHandle: function (action, $el) {

        },
        // 初始化表单相关组件
        initForm: function () {
            var model = this.model;
            var forms = this.forms;
            var mycomponents = this.mycomponents;
            this.$('.j-shiporder-comp-wrap').each(function () {
                var obj = $(this).data();
                var Comp = mycomponents[obj.name] || mycomponents[obj.type] || components[obj.type];
                var comp;
                if (Comp) {
                    comp = new Comp({
                        el: this,
                        model: model,
                        type: obj.type,
                        name: obj.name,
                    })
                    comp.render();
                    comp.setStatus && comp.setStatus();
                    forms[obj.name] = comp;
                }
            })
        },
        // 消息提示
        showTip: function (msg, type) {
            if (type) {
                crmUtil.remind(type, msg || $t("操作成功"));
                return;
            }
            crmUtil.alert(msg || $t("操作失败请稍后尝试或联系纷享客服"));
        },
        // 埋点 - 开启库存、开启发货单
        _doLog: function(objName) {
            var logType = {
                delivery: 'ondeliverynoteobj',
                stock: 'onstockobj'
            };
            CRM.util.uploadLog('crmsetting', 's-rule', {
                eventId: logType[objName],
                eventType: 'cl'
            });
        },
        // 显示loading，页面处于加载中
        showLoading: function () {
            this.$('.j-action-loading').show();
            this.$('.j-action-error').hide();
            this.$('.j-fields').hide();
        },
        hideLoading: function () {
            this.$('.j-action-loading').hide();
        },
        // 显示查询失败
        showErrMsg: function () {
            this.$('.j-action-loading').hide();
            this.$('.j-action-error').show();
            this.$('.j-fields').hide();
        },
        hideErrMsg: function () {
            this.$('.j-action-error').hide();
        },
        showContent: function () {
            this.hideLoading();
            this.hideErrMsg();
            this.$('.j-fields').show();
        },
        // 添加模型事件监听
        _addEvents: function () {
            if (!_.isEmpty(this.modelEvents)) {
                this.listenTo(this.model, this.modelEvents);
            }
        },
        // 重新加载处理
        reloadHandle: function () {
            this.render();
        },
        // 打开纷享客服服务号的企信
        openQx: function () {
            setTimeout(function () {
                FS.MEDIATOR.trigger('qx.shareGroup.open')
            }, 0);
        },
        // 相关操作处理
        doActionHandle: function (e) {
            var $target = $(e.target);
            var action = $target.attr('data-action');
            this.doAction(action);
        },
        doAction: function (action) {
            
        },
        // 销毁
        destroy: function () {
            this.$el.off();
            this.$el.empty();
            _.each(this.widgets, function (a) {
                a && a.destroy && (a.destroy(), a = null);
            });
            _.each(this.forms, function (a) {
                a && a.destroy && (a.destroy(), a = null);
            });
            this.widgets = this.forms = null;
        },
    });

    module.exports = View;
});
