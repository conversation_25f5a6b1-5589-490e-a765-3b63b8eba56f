/**
 * @description 模块开关
 * 支持类型有 info 信息展示 on 开 off 关 unopen 未开启
 */
define(function (require, exports, module) {
	module.exports = Backbone.View.extend({
		template: require('./tpl-html'),
		typeTemplate: require('./type-html'),

		options: {
			label: '',
			type: 'off',
            desc: $t('已启用'),
            unopenBtn: $t("开启"),
		},

		events: {
			'click .j-switch': 'actionHandle',
		},

		render: function () {
			this.$el.html(this.template(this.options));
		},

		renderType: function () {
			this.$('.switch_oprate').html(this.typeTemplate(this.options));
		},

		setType: function (type, data) {
			this.options.type = type;
			if (data) {
				if (type === 'info') {
					this.options.desc = data;
				}
				if (type === 'unopnen') {
					this.options.unopenBtn = data;
				}
			}
			this.renderType();
		},

		setTip: function (tip) {
			this.$('.j-switch-tip').html(tip);
		},

		actionHandle: function (e) {
			var $target =$(e.currentTarget); 
			var action = $target.attr('data-type');
			this.trigger('switch', action, $target);
		},

		destroy: function () {
			this.$el.remove();
		},
	});
});