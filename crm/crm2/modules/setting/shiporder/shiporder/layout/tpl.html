<div class="shiporder-con_box">
    ## if (obj.topInfo) { ##
        <div class="crm-intro">
            ## if (_.isString(obj.topInfo.messages)) { ##
                {{{{-obj.topInfo.messages}}}}
            ## } else { ##
                <h3>{{$t("说明：")}}</h3>
                <ul>
                    ## _.each(obj.topInfo.messages, function (msg, index) { ##
                        <li>{{index + 1}}. {{{{-msg}}}}</li>
                    ## }) ##
                </ul>
                ## if (obj.hasService) { ##
                <a class="fs-service j-shiporder-service" href="javascript:;">{{$t("客服")}}</a>
                ## } ##
            ## } ##
        </div>
    ## } ##
    <div class="so-actions">
        <div class="crm-loading tab-loading j-action-loading" style="{{obj.loading ? '' : 'display: none;'}}"></div>
        <p class="j-action-error" style="{{obj.errCode !== 0 ? '' : 'display: none;'}}">
            <span class="so-error">{{$t("获取相关配置失败")}}</span>
            <a href="javascript:;" class="j-reload-config">{{$t("重试")}}</a>
        </p>
        <div class="j-fields" style="{{!obj.loading && (obj.errCode === 0) ? '' : 'display: none;'}}">
            ## if (obj.layout.switchInfo) { ##
                <div class="j-action-switch"></div>
            ## } ##
            <div class="so-settings-wrapper j-settings">
                ## _.each(obj.layout.fields, function (b) { ##
                    <div class="j-shiporder-comp-wrap" data-type="{{b.type}}" data-name="{{b.name}}"></div>
                ## }) ##
                <div class="shiporder-btns">
                    ## _.each(obj.layout.buttons, function (btn) { ##
                        <a href="javascript:;" class="crm-btn crm-btn-primary j-layout-btn" data-action="{{btn.action}}" style="{{btn.show ? '' : 'display: none;'}}">{{btn.label}}</a>
                    ## }) ##
                </div>
            </div>
        </div>
    </div>
</div>
