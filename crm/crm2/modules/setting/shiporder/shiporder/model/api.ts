/**
 * @description 管理页相关api
 * TODO TS迁移
 *
 */
const crmUtil = FS.crmUtil;
const stockApi = require('crm-modules/common/stock/api');

interface CurOpts {
	submitSelector?: any;
	errorAlertModel?: number;
}

interface ResData {
	resultCode: number;
	token: string;
	errMessage?: string;
	errMsg?: string;
}

interface WareHouse {
	purchaseReceivedWarehouseId: string;
	purchaseReceivedWarehouseName: string;
}

export interface WareHouseOption {
	value: string;
	label: string;
}

export default {
	// 通用设置
	_queryApi: function (options) {
		var me = this;
		var opts = _.extend(
			{
				url: '',
				type: 'post',
				success: function (res) {
					var data;
					if (res.Result.StatusCode == 0) {
						data = res.Value;
					} else {
						me.set({
							errCode: 500
						});
					}
					options.successCb && options.successCb(data);
				}
			},
			options || {}
		);
		return crmUtil.FHHApi(_.omit(opts, 'successCb'), {
			errorAlertModel: 2
		});
	},
	_operateApi: function (options) {
		var opts = _.extend(
			{
				url: '',
				type: 'post'
			},
			options || {}
		);
		const curOpts: CurOpts = {
			submitSelector: opts.submitSelector
		};
		// 默认展示错误信息，但是后台存在特殊 errCode 识别处理时，请设置为 1
		curOpts.errorAlertModel = opts.errorAlertModel || 2;
		// 支持多种方式
		if (opts.successCb && !opts.success) {
			opts.success = function (res) {
				if (res.Result.StatusCode == 0) {
					opts.successCb(res.Value);
				}
			};
		}
		return crmUtil.FHHApi(
			_.omit(opts, 'errorAlertModel', 'submitSelector', 'successCb'),
			curOpts
		);
	},
	_commonApi(options = {}, curOpts: CurOpts = {}) {
		var opts = _.extend(
			{
				url: '',
				type: 'post',
				errorCb: $.noop
			},
			options
		);
		// 默认展示错误信息，但是后台存在特殊 errCode 识别处理时，请设置为 1
		var errorAlertModel =
			curOpts.errorAlertModel || opts.errorAlertModel || 2;
		curOpts.errorAlertModel = 1;
		// 支持多种方式
		if (opts.successCb && !opts.success) {
			opts.success = function (res) {
				if (res.Result.StatusCode == 0) {
					opts.successCb(res.Value);
				} else {
					if (errorAlertModel !== 1) {
						crmUtil.alert(res.Result.FailureMessage);
					}
					opts.errorCb(res);
				}
			};
		}
		if (!opts.error) {
			opts.error = function () {
				opts.errorCb();
			};
		}
		return crmUtil.FHHApi(
			_.omit(
				opts,
				'errorAlertModel',
				'submitSelector',
				'successCb',
				'errorCb'
			),
			curOpts
		);
	},
	/**
	 * @description 多段式异步请求
	 * @param {async function} requestFn 异步请求
	 * @param {object} params 异步请求参数
	 * @param {function} formatData resultCode token 格式化处理
	 * 针对目前部分应用，后台处理逻辑过重，异步处理较多的情况（后台同步调整）
	 * 暂不支持取消请求
	 */
	requestManySections(requestFn, params, formatData) {
		return new Promise((resolve, reject) => {
			let paramsData = _.clone(params || {});
			let count = 0;
			let formatDataFn = _.isFunction(formatData)
				? formatData
				: function (data: ResData) {
						return {
							resultCode: data.resultCode,
							token: data.token
						};
				  };

			function requestData() {
				count++;
				requestFn(paramsData).then((data: ResData) => {
					let { resultCode, token } = formatDataFn(data);
					// resultCode 0 进行中
					if (+resultCode === 0) {
						paramsData.token = token;
						next();
					} else if (+resultCode === 1) {
						// 异步请求处理成功
						resolve(data);
					} else {
						// 异步请求处理错误
						reject({
							errCode: 2,
							errMsg: data.errMessage || data.errMsg || '请求错误'
						});
					}
				}, reject);
			}

			function next() {
				if (paramsData.token) {
					// 轮询时间固定为 3s
					setTimeout(requestData, 3000);
				} else {
					requestData();
				}
			}

			next();
		});
	},
	// 判断用户开启库存方式
	fetchStockType: function (cb) {
		var me = this;
		return this._queryApi({
			url: '/EM1HNCRM/API/v1/object/stock/service/query_enabled_stock_type',
			successCb: function (data) {
				if (data) {
					var stockType = data.stockType - 0;
					me.set({
						hasStockPackage: !!data.purchaseStockModuleLicense,
						// stockType: 4,
						// isForErp: false
						stockType: stockType,
						isForErp: data.isForErp
					});
					cb && cb(stockType);
				}
			}
		});
	},
    // 判断用户是否购买库存
    fetchStockOpen() {
        var me = this;
        return crmUtil.FHHApi({
            url: '/EM1HNCRM/API/v1/object/stock/service/is_purchase_stock_license',
            type: 'post',
            success: (res) => {
                console.log(res);
                if (res && res.Value) {
                    me.set({
                        isPurchase: res.Value.isPurchase
                    });
                }
            }
        })
    },
	// 判断用户是否开启了订货通
	fetchDHTStatus: function () {
		var me = this;
		// me.set('enableSetStockView', CRM.control.isOrder); // 旧逻辑
		return crmUtil.FHHApi(
			{
				url: '/FHH/EM1HSailAdmin/sail-admin/config/isDHTOpen',
				type: 'post',
				success: function (res) {
					if (!res.Error) {
						me.set('enableSetStockView', res.Value);
					}
				}
			},
			{
				autoPrependPath: false,
				errorAlertModel: 1
			}
		);
	},
	/**
	 * ----------------------------------------------------------------------------------------------------------------
	 * 纷享库存
	 * ----------------------------------------------------------------------------------------------------------------
	 */
	/**
	 * -------------------------
	 * 发货单
	 * -------------------------
	 */
	// 获取发货单状态
	fetchDeliveryNote: function (cb) {
		var me = this;
		return this._queryApi({
			url: '/EM1HNCRM/API/v1/object/delivery_note/service/query_delivery_note_config',
			successCb: function (data) {
				if (data) {
					me.set({
						errCode: 0,
						deliveryStatus: data.switchStatus - 0 || 0,
						hasSalesOrderNeedUpdate: data.hasSalesOrderNeedUpdate,
						isPaid: data.displayAdvancedLogisticsQuery,
						expressQueryFuncId: data.expressQueryFuncId - 0 || 1,
						isAllowModifySalesOrderProduct:
							data.isAllowModifySalesOrderProduct - 0 || 1
					});
				}
				cb && cb(me.toJSON());
			}
		});
	},
	// 开启发货单
	enableDeliveryNote: function (options) {
		return this._operateApi(
			_.extend(
				{
					url: '/EM1HNCRM/API/v1/object/delivery_note/service/enable_delivery_note',
					errorAlertModel: 1
				},
				options
			)
		);
	},
	// 保存发货单设置
	setDeliveryConfig: function (cb, selector) {
		var data = this.toJSON();
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/delivery_note/service/update_delivery_note_config',
			data: {
				functionId: data.expressQueryFuncId + '',
				isAllowModifySalesOrderProduct:
					data.isAllowModifySalesOrderProduct + ''
			},
			submitSelector: selector,
			successCb: function () {
				cb && cb();
			}
		});
	},
	/**
	 * -------------------------
	 * 库存
	 * -------------------------
	 */
	// 获取库存状态 (此时已获取发货单状态)
	fetchStock: function (cb) {
		var me = this;
		return this._queryApi({
			url: '/EM1HNCRM/API/v1/object/stock/service/query_stock_config',
			successCb: function (data) {
				if (data) {
					me.set({
						errCode: 0,
						stockStatus: data.stockSwitch - 0 || 1,
						validateOrderType: data.validateOrderType - 0 || 1,
						stockViewType: data.stockViewType - 0 || 1,
						orderWarehouseType: data.orderWarehouseType - 0 || 1,
						stockWarningType: data.stockWarningType - 0 || 1,
						isNotShowZeroStockType:
							data.isNotShowZeroStockType - 0 || 1,
						safetyStockType: data.safetyStockType - 0 || 1,
						salesOrderProductDecimal:
							data.salesOrderProductDecimal - 0,
						stockDecimal: data.stockDecimal - 0,
						poSwitch: data.isPurchaseOrderEnable ? 2 : 0,
						poDecimal: data.purchaseOrderDecimal - 0,
						isOnlyShowOnSaleStockType:
							data.isOnlyShowOnSaleStockType - 0 || 2,
						hasUiEventModule: !!data.hasUiEventModule,
						showStockForOrder: +data.isSalesOrderShowStock === 1
					});
				}
				cb && cb(me.toJSON());
			}
		});
	},
	// 改变库存状态，默认为开启
	changeStockStatus: function (options) {
		return this._operateApi(
			_.extend(
				{
					url: '/EM1HNCRM/API/v1/object/stock/service/save_stock_switch',
					errorAlertModel: 1
				},
				options
			)
		);
	},
	// 保存库存设置
	setStockConfig: function (cb, selector) {
		var data = this.toJSON();
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/stock/service/save_stock_config',
			data: {
				validateOrderType: data.validateOrderType,
				stockViewType: data.stockViewType,
				orderWarehouseType: data.orderWarehouseType,
				stockWarningType: data.stockWarningType,
				isNotShowZeroStockType: data.isNotShowZeroStockType,
				safetyStockType: data.safetyStockType,
				stockDecimal: data.stockDecimal,
				isOnlyShowOnSaleStockType: data.isOnlyShowOnSaleStockType
			},
			errorAlertModel: 2,
			submitSelector: selector,
			successCb: function () {
				cb && cb();
			}
		});
	},
	// 开启销售订单库存设置
	enableOrderShowStock: function (options) {
		return this._operateApi(
			_.extend(
				{
					url: '/EM1HNCRM/API/v1/object/stock/service/enable_sales_order_show_stock',
					errorAlertModel: 1
				},
				options
			)
		);
	},
	/**
	 * -------------------------
	 * 批次和序列号
	 * -------------------------
	 */
	// 查询批次与序列号配置信息
	fetchBatch: function (cb) {
		var me = this;
		return this._queryApi({
			url: '/EM1HNCRM/API/v1/object/batch_sn/service/query_batch_sn_config',
			successCb: function (data) {
				if (data) {
					me.set({
						batchSNSwitch: Number(data.batchSNSwitch),
						willExpireStockWarningType: Number(
							data.willExpireStockWarningType
						)
					});
				}
				cb && cb(me.toJSON());
			}
		});
	},
	// // 开启批次与序列号管理
	// enableBatch: function (options) {
	// 	return this._operateApi(
	// 		_.extend(
	// 			{
	// 				url: '/EM1HNCRM/API/v1/object/batch_sn/service/enable_batch_sn',
	// 				errorAlertModel: 2
	// 			},
	// 			options
	// 		)
	// 	);
	// },
	// // 保存批次和序列号相关设置
	// setBatchConfig: function (cb, selector) {
	// 	var data = this.toJSON();
	// 	return this._operateApi({
	// 		url: '/EM1HNCRM/API/v1/object/batch_sn/service/save_batch_sn_config',
	// 		data: {
	// 			willExpireStockWarningType: data.willExpireStockWarningType
	// 		},
	// 		errorAlertModel: 2,
	// 		submitSelector: selector,
	// 		successCb: function () {
	// 			cb && cb();
	// 			crmUtil.remind(1, $t('操作成功'));
	// 		}
	// 	});
	// },
	/**
	 * -------------------------
	 * 采购
	 * -------------------------
	 */
	// 查询采购模块
	fetchPO: function (cb) {
		var me = this;
		return this._queryApi({
			url: '/EM1HNCRM/API/v1/object/purchase_order/service/is_purchase_order_enable',
			successCb: function (data) {
				if (data) {
					me.set({
						errCode: 0,
						poSwitch: data.switchStatus - 0 || 0,
						poDecimal: data.purchaseOrderDecimal - 0,
						stockDecimal: data.stockDecimal - 0
					});
				}
				cb && cb(me.toJSON());
			}
		});
	},
	// 开启采购模块
	enablePO: function (options) {
		return this._operateApi(
			_.extend(
				{
					url: '/EM1HNCRM/API/v1/object/purchase_order/service/enable_purchase_order',
					errorAlertModel: 2
				},
				options
			)
		);
	},
	// 设置采购模块信息
	setPOConfig: function (cb, selector) {
		var data = this.toJSON();
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/purchase_order/service/save_purchase_order_config',
			data: {
				purchaseOrderDecimal: data.poDecimal
			},
			errorAlertModel: 2,
			submitSelector: selector,
			successCb: function () {
				cb && cb();
			}
		});
	},
	/**
	 * ----------------------------------------------------------------------------------------------------------------
	 * ERP库存
	 * ----------------------------------------------------------------------------------------------------------------
	 */
	// 获取退换货管理相关信息
	fetchExchangeManageConfig: function (cb) {
		var me = this;
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/exchange_return_note/service/query_exchange_return_note_config',
			errorAlertModel: 2,
			successCb: function (data) {
				if (data) {
					me.set({
						errCode: 0,
						stockStatus: data.isStockEnable ? 2 : 1,
						exchangeReturnNoteStatus:
							+data.exchangeReturnNoteSwitch || 1
					});
				}
				cb && cb(me.toJSON());
			}
		});
	},
	// 开启退换货管理
	enableExchangeReturn: function (options) {
		return this._operateApi(
			_.extend(
				{
					url: '/EM1HNCRM/API/v1/object/exchange_return_note/service/enable_exchange_return_note',
					errorAlertModel: 1
				},
				options
			)
		).done(function (data) {
			if (!data.Value) {
				crmUtil.remind(3, data.Result.FailureMessage);
			}
		});
	},
	/**
	 * ----------------------------------------------------------------------------------------------------------------
	 * ERP库存
	 * ----------------------------------------------------------------------------------------------------------------
	 */
	// 查询ERP库存信息
	fetchErpInfo: function (cb) {
		var me = this;

		return this._queryApi({
			url: '/EM1HNCRM/API/v1/object/erp_stock_biz/service/query_erp_stock_config',
			successCb: function (data) {
				if (data) {
					me.set({
						errCode: 0,
						erpStock: data.enable,
						erpValidateOrderType: data.validateOrderType - 0 || 2,
						erpIsNotShowZeroStockType:
							data.isNotShowZeroStockType - 0 || 2,
						erpStockDecimal: data.stockDecimal - 0,

						validateOrderType: data.validateOrderType,
						isNotShowZeroStockType: data.isNotShowZeroStockType,
						stockDecimal: data.stockDecimal
					});
				}
				cb && cb(data);
			}
		});
	},
	// 开启ERP库存
	enableErpStock: function (cb, selector) {
		var me = this;

		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/erp_stock_biz/service/enable_erp_stock',
			submitSelector: selector,
			successCb: function (data) {
				cb && cb(data.enableStatus);
			}
		});
	},
	// 关闭ERP库存
	closeErpStock: function (cb, selector) {
		var me = this;

		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/erp_stock_biz/service/close_erp_stock',
			submitSelector: selector,
			successCb: function (data) {
				cb && cb(data.enableStatus);
			}
		});
	},
	// 保存ERP库存设置
	setErpStockConfig: function (cb, selector) {
		var me = this;

		var data = this.toJSON();
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/erp_stock_biz/service/save_erp_stock_config',
			data: {
				validateOrderType: data.validateOrderType,
				isNotShowZeroStockType: data.isNotShowZeroStockType,
				stockDecimal: data.stockDecimal
			},
			submitSelector: selector,
			successCb: function () {
				cb && cb();
			}
		});
	},
	/**
	 * ----------------------------------------------------------------------------------------------------------------
	 * 新版库存管理改造
	 * ----------------------------------------------------------------------------------------------------------------
	 */
	// 库存模块初始设置
	// initShipOrder: function (param, cb, selector) {
	// 	var me = this;
	// 	return this._operateApi({
	// 		url: "/EM1HNCRM/API/v1/object/stock_module/service/init",
	// 		data: param,
	// 		submitSelector: selector,
	// 		errorAlertModel: 2,
	// 		successCb: function (data) {
	// 			crmUtil.remind(1, $t("操作成功"));
	// 			cb && cb(data);
	// 		}
	// 	})
	// },
	initShipOrder(param) {
		return this.requestManySections((params) => {
			return new Promise((resolve, reject) => {
				this._commonApi({
					url: '/EM1HNCRM/API/v1/object/stock_module/service/async_init',
					data: params,
					successCb(data) {
						resolve(data);
					},
					errorCb(err) {
						reject(err);
					}
				});
			});
		}, param).then((data) => {
			crmUtil.remind(1, $t('操作成功'));
			return data;
		});
	},

	/**
	 * @description 获取各种订单业务类型对应的发货模式
	 * @return {Promise<unknown>}
	 */
	getQueryAllConfigData() {
		return new Promise((resolve) => {
			this._queryApi(
				{
					url: '/EM1HNCRM/API/v1/object/biz_conf/service/query_all_config_data',
					data: {
						bizType: 'record_type',
						describeApiName: 'SalesOrderObj'
					},
					errorAlertModel: 2,
					successCb: resolve
				},
				{
					autoPrependPath: false
				}
			);
		});
	},
	/**
	 * @description 初始化，订单业务类型默认对应的发货模式
	 * @return {Promise<unknown>}
	 */
	initOrderRecordTypeRelateDeliveryMode() {
		return new Promise((resolve) => {
			this._queryApi(
				{
					url: '/EM1HNCRM/API/v1/object/stock_module/service/init_order_warehouse_type_for_order_record_type',
					data: {},
					errorAlertModel: 2,
					successCb: resolve
				},
				{
					autoPrependPath: false
				}
			);
		});
	},

	// 获取基础设置配置信息
	getBaseConfig: function () {
		return new Promise((resolve) => {
			this._queryApi(
				{
					url: '/EM1HNCRM/API/v1/object/stock_module/service/get_base_config',
					errorAlertModel: 2,
					successCb: (data) => {
						const stockStatus = Number(data.stockSwitch) || 1;
						const modelData = this.toJSON();
						let titleLabel = modelData.titleLabel;
                        if (data.stockType == 4) {
                            titleLabel = $t("crm.发货单");
                        } else if (data.isErpStockEnable == true) {
                            titleLabel = $t("对接版·C类");
                        } else {
                            titleLabel = data.isForErp ? $t("对接版·B类") : $t("完整版·A类");
                        }
                        data.stockStatus = stockStatus;
						this.set({
              isErpStockEnable: data.isErpStockEnable,
							isAllowModifySalesOrderProduct:
								data.isAllowModifySalesOrderProduct,
							orderWarehouseType: data.orderWarehouseType,
							validateOrderType: data.validateOrderType,
							isNotShowZeroStockType: data.isNotShowZeroStockType,
							isOnlyShowOnSaleStockType:
								data.isOnlyShowOnSaleStockType,
							safetyStockType: data.safetyStockType,
							// stockWarningType: data.stockWarningType,
							salesOrderProductDecimal:
								data.salesOrderProductDecimal,
							stockDecimal: data.stockDecimal,
							purchaseOrderDecimal: data.purchaseOrderDecimal,
							deliveryNoteDecimal: data.deliveryNoteDecimal,
							isPurchaseOrderEnable: data.isPurchaseOrderEnable,
							stockSwitch: stockStatus,
							stockStatus: stockStatus,
							isForErp: data.isForErp,
							titleLabel: titleLabel,
							hasUiEventModule: data.hasUiEventModule,
							isSalesOrderShowStock: data.isSalesOrderShowStock,
							stockViewType: data.stockViewType,
							expressFuncCode: data.expressFuncCode,
							allowOrderWarehouseBlank:
								data.allowOrderWarehouseBlank,
							batchSNSwitch: data.batchSNSwitch,
							orderWarehouseTypeConfigRangeCode:
								data.orderWarehouseTypeConfigRangeCode,
							stockCheckNoteUpdateStockType:
								data.stockCheckNoteUpdateStockType,
							requisitionNoteAutomaticInbound:
								data.requisitionNoteAutomaticInbound,
							requisitionNoteAutomaticOutbound:
							data.requisitionNoteAutomaticOutbound
						});
            this.initVersion()
						resolve(data);
					}
				},
				{
					autoPrependPath: false
				}
			);
		});
	},
	// 仓库设置
	setOrderWarehouseType: function (param, selector, cb) {
		return new Promise((resolve) => {
			this._operateApi({
				url: '/EM1HNCRM/API/v1/object/stock_module/service/set_order_warehouse_type',
				data: param,
				submitSelector: selector,
				errorAlertModel: 2,
				successCb: function (data) {
					crmUtil.remind(1, $t('操作成功'));
					cb && cb();
					resolve(data);
				}
			});
		});
	},
	// 订单校验
	setValidateOrderType: function (val, selector, cb) {
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/stock_module/service/set_validate_order_type',
			data: {
				validateOrderType: val
			},
			submitSelector: selector,
			errorAlertModel: 2,
			successCb: function (data) {
				crmUtil.remind(1, $t('操作成功'));
				cb && cb();
			}
		});
	},
	// 库存显示
	setShowStockType: function (data, selector, cb) {
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/stock_module/service/set_show_stock_type',
			data: {
				isNotShowZeroStockType: data.isNotShowZeroStockType,
				isOnlyShowOnSaleStockType: data.isOnlyShowOnSaleStockType
			},
			submitSelector: selector,
			errorAlertModel: 2,
			successCb: function (data) {
				crmUtil.remind(1, $t('操作成功'));
				cb && cb();
			}
		});
	},
	// 库存安全
	setSafetyStockType: function (val, selector, cb) {
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/stock_module/service/set_safety_stock_type',
			data: {
				safetyStockType: val
			},
			submitSelector: selector,
			errorAlertModel: 2,
			successCb: function (data) {
				crmUtil.remind(1, $t('操作成功'));
				cb && cb();
			}
		});
	},
	// 库存预警
	setStockWarningType: function (val, selector, cb) {
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/stock_module/service/set_stock_warning_type',
			data: {
				stockWarningType: val
			},
			submitSelector: selector,
			errorAlertModel: 2,
			successCb: function (data) {
				crmUtil.remind(1, $t('操作成功'));
				cb && cb();
			}
		});
	},
	// 可用库存显示
	enableSalesOrder: function (cb) {
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/stock_module/service/enable_sales_order_show_stock',
			errorAlertModel: 2,
			successCb: function () {
				crmUtil.remind(1, $t('操作成功'));
				cb && cb();
			}
		});
	},
	// 查询自动收货信息
	getReceivingInfo: function (cb) {
		var me = this;
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/stock_module/service/get_automatic_receiving_conf',
			errorAlertModel: 1,
			successCb: function (data) {
				console.log('自动收货信息', data);
				var params = {
					daysAfterShipment: data.daysAfterShipment,
					autoReceiveStatus: data.status
				};
				me.set(params);
				cb && cb(me.toJSON());
			}
		});
	},
	// 设置自动收货
	setReceiving: function (param, cb) {
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/stock_module/service/set_automatic_receiving',
			data: param,
			errorAlertModel: 2,
			successCb: function (data) {
				crmUtil.remind(1, $t('操作成功'));
				cb && cb();
			}
		});
	},
	// 设置精度
	setDecimal: function (param, selector,cb) {
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/stock_module/service/set_decimal',
			data: param,
			submitSelector: selector,
			errorAlertModel: 2,
			successCb: function(data) {
				console.log('精度设置', data);
				crmUtil.remind(1, $t('操作成功'));
				if (cb) {
					cb(data);
				}
			}
		});
	},
	// 获取扫码设置信息
	getScanInfo: function (cb) {
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/stock_scan_code/service/query_scan_code_config',
			errorAlertModel: 1,
			successCb: function (data) {
				cb && cb(data.scanCodeType);
			}
		});
	},
	// 设置扫码设置信息
	setScanInfo: function (value, cb) {
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/stock_scan_code/service/save_scan_code_config',
			data: {
				scanCodeType: value
			},
			errorAlertModel: 2,
			successCb: function () {
				crmUtil.remind(1, $t('操作成功'));
				cb && cb();
			}
		});
	},
	// 设置高级物流查询
	setExpressFun: function (value) {
		return this._operateApi({
			url: '/EM2HNCRM/API/v1/object/stock_module/service/set_express_func',
			data: {
				expressFuncCode: value
			},
			errorAlertModel: 2,
			successCb: function () {
				crmUtil.remind(1, $t('操作成功'));
			}
		});
	},
	// 库存数据初始化
	initStockData: function (cb, selector) {
		var me = this;
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/stock_special_operation/service/delete_all_object_data',
			errorAlertModel: 2,
			submitSelector: selector,
			successCb: function () {
				crmUtil.remind(1, $t('操作成功'));
				cb && cb();
			}
		}).then(function (res) {
			var data = res.Result;
			if (data.FailureCode === 1035) {
				me.queryStockStatus();
			} else {
				return crmUtil.remind(3, data.FailureMessage);
			}
		});
	},
	// 库存数据查询
	getStockData: function (cb, keys) {
		const params = keys || [
			'dht_stock_switch',
			'delivery_note_status',
			'purchase_order_status',
			'dht_exchange_return_note_switch'
		];
		const result = stockApi.getStockConfigs(params);
		return result.then((data) => {
			cb && cb(data);
			return data;
		});
	},
	// 查询操作权限
	getPrivilege: function (cb) {
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/stock_special_operation/service/get_privilege',
			errorAlertModel: 1,
			successCb: function (data) {
				cb && cb(data);
			}
		});
	},
	// 库存切换
	changeStockType: function (cb, selector) {
		var me = this;
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/stock_special_operation/service/change_stock_type',
			errorAlertModel: 2,
			data: {
				tenantId: String(CRM.enterpriseId)
			},
			submitSelector: selector,
			successCb: function () {
				crmUtil.remind(1, $t('操作成功'));
				cb && cb();
			}
		}).then(function (res) {
			var data = res.Result;
			if (data.FailureCode === 1035) {
				me.queryStockStatus(cb);
			} else {
				return crmUtil.remind(3, data.FailureMessage);
			}
		});
	},
	// 判断是否显示蒙层
	isShowStockLoading: function () {
		var me = this;
		this.isStockAvailable(function (data) {
			if (data && data.result) {
				me.queryStockStatus();
			}
		});
	},
	// 判断库存是否进行特殊操作
	isStockAvailable: function (cb) {
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/stock_special_operation/service/is_special_operation_working',
			errorAlertModel: 2,
			successCb: function (data) {
				cb && cb(data);
			}
		});
	},
	// 库存重启
	restartStock: function (cb) {
		var me = this;
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/stock_special_operation/service/restart_stock',
			errorAlertModel: 2,
			successCb: function (data) {
				cb && cb(data);
			}
		}).then(function (res) {
			var data = res.Result;
			if (data.FailureCode === 1035) {
				me.queryStockStatus(function () {
					cb && cb();
				});
			} else {
				return crmUtil.remind(3, data.FailureMessage);
			}
		});
	},
	// 轮询库存切换状态
	queryStockStatus: function (cb) {
		var me = this;
		var MAXTIMES = 10; // 最多轮询次数
		var counter = 0;
		me.set({
			maskTips: '正在进行高危操作...',
			showMask: true
		});
		var stockInterval = setInterval(function () {
			me.isStockAvailable(function (res) {
				counter++;
				if (!res.result || counter === MAXTIMES) {
					clearInterval(stockInterval);
					me.set({
						showMask: false
					});
					crmUtil.remind(1, $t('操作成功'));
					cb && cb();
				}
			});
		}, 10 * 1000);
	},
	// 设置是否允许编辑订单
	setEditOrder: function (param, cb) {
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/stock_module/service/set_is_allow_modify_sales_order_product',
			data: param,
			errorAlertModel: 2,
			successCb: function (data) {
				crmUtil.remind(1, $t('操作成功'));
				cb && cb();
			}
		});
	},

	// 设置盘点单手动或者自动生成盘盈入库单/盘亏出库单
	setStockCheckNoteUpdateStockType: function (param, cb) {
		return this._operateApi({
			url: '/EM1HNCRM/API/v1/object/stock_module/service/set_stock_check_note_update_stock_type',
			data: param,
			errorAlertModel: 2,
			successCb: function (data) {
				crmUtil.remind(1, $t('操作成功'));
				cb && cb();
			}
		});
	},

	// 获取CPQ组合发货信息
	getCPQDeliveryWay(cb) {
		return stockApi.getCpqDeliveryWay();
	},
	setStockConfigs(key, status, cb) {
		return this._operateApi({
			url: '/EM2HNCRM/API/v1/object/stock_config/service/set_config_value',
			data: {
				key: key,
				value: String(status), // '1' 和 '2'
				oldValue: '' // 暂不需要，后面业务有调整再说
			},
			errorAlertModel: 2,
			successCb: function (res) {
				cb && cb(res);
			}
		});
	},
	// 开启快消模块多单位优化
	enableStockSaleMulti: function (cb) {
		return this._operateApi({
			url: '/EM2HNCRM/API/v1/object/stock_multi_unit/service/enable_stock_sales_module_optimize',
			errorAlertModel: 2,
			successCb: function (data) {
				cb && cb(data);
			}
		});
	},
	
	/**
	 * @description B类库存开启仓位
	 */
	enableParentWareHouse(cb) {
		return this._operateApi({
			url: '/EM2HNCRM/API/v1/object/warehouse/service/open_ware_position',
			errorAlertModel: 2,
			successCb: (data) => {
				if (!data.result) return;

				this.set('isOpenWareHousePosition', true);
				cb && cb(data);
			}
		});
	},
};
