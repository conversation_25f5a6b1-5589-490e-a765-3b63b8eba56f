/**
 * @description 数据格式处理
 * TODO TS迁移
 */
export default {
	/**
	 * 转换，获取订货模式中根据订单业务类型设置的值，的数据
	 * @param res
	 * @return {*}
	 */
	setOrderRecordTypeRelateDeliveryMode(res) {
		/*****数据处理*****/
		if (
			!res ||
			!res.configDataMap ||
			!res.configDataMap.order_record_type_relate_delivery_mode ||
			!res.bizTypeValueList
		) {
			return;
		}
		const _enum = {
			1: '单一仓库订货',
			2: '合并仓库订货',
			3: '无仓库订货'
		};
		const orderRecordTypeRelateDeliveryMode =
			res.configDataMap.order_record_type_relate_delivery_mode;
		const { bizTypeValueList } = res;
		const orderRecordTypeRelateDeliveryModeMap = {};
		bizTypeValueList.forEach((i) => {
			const value = orderRecordTypeRelateDeliveryMode.find(
				(item) => item.bizTypeValue === i.apiName
			);
			orderRecordTypeRelateDeliveryModeMap[i.apiName] = Object.assign(
				{
					configLabel: _enum[Number(value.configValue)]
				},
				i,
				value
			);
		});
		/****************/
		return this.set(
			'orderRecordTypeRelateDeliveryMode',
			orderRecordTypeRelateDeliveryModeMap
		);
	}
};
