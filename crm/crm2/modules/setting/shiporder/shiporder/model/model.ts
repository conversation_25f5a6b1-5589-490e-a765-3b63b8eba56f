/**
 * @description 管理页相关数据处理
 * TODO TS迁移
 */
import api from "./api";
import format from "./format";
import { UpstreamEnterpriseInfos } from "crm-modules/common/stock/api";

export interface DeliveryNoteSupportSignatures {
	// 发货单电子签
	// 1：关闭，2：开启
	SSstatus: "1" | "2";
	// 0:不选择，1：发货单外部负责人，2：收货人
	SSupStreamSigner: "0" | "1";
	// 0:不选择，1：发货单外部负责人，2：收货人
	SSdownStreamSigner: "0" | "1";
	// 打印模板id
	SSprintTemplateId: string;
	// 电子签厂商
	SSsignAppType: string;
}

interface Attributes  extends DeliveryNoteSupportSignatures{
	_reloadNum: number; // 用于刷新页面
	/** 通用 */
	title: string;
	titleLabel: string;
	// curTab: 0,						// 当前tab
	hasStockPackage: boolean; // 是否购买库存产品包，能否开启库存
	isPurchase: boolean; // 是否购买库存包
	stockType: 0 | 1 | 2 | 3 | 4; // 库存管理版本 1 均未开启 2 开启库存 3 开启C类库存 4 只能且开了发货单
	isForErp: boolean; // isForErp 是否纷享库存对接ERP  false: A类(完整版) true: B类(对接版)
	isErpStockEnable: boolean; // 是否是对接版·C类
	// consumerDealerProductVersion: false, 	// 是否快消经销商版
	// doStockType: 0,					// 所选开启库存类型 0: A类(完整版) 1: B类(对接版)
	fields: any; // 设置相关字段
	/**纷享库存管理 */
	// 发货单
	deliveryStatus: 0 | 1 | 2 | 3; // 发货单启用状态， 0: 未开启 1: 开启失败, 2: 开启成功, 3: 开启中
	deliveryErrMsg: string; // 发货单开启错误原因
	// hasSalesOrderNeedUpdate: false,	// 是否存在需要修改状态的销售订单
	isPaid: boolean; // 是否付费用户
	expressQueryFuncId: 1 | 2; // 物流查询功能 1: 标准  2: 高级
	isAllowModifySalesOrderProduct: 1 | 2; // 是否允许编辑销售订单的数量和种类 1：不允许 2：允许
	deliveryNoteDecimal: number; // 发货单精度
	// 库存 stockSwitch 和 stockStatus 是一回事，不知道为啥有两个
	stockSwitch: 1 | 2 | 3; // 库存启用状态： 1: 未开启  2: 已开启, 3: 停用
	stockStatus: 1 | 2 | 3; // 库存启用状态： 1: 未开启  2: 已开启, 3: 停用
	hasUiEventModule: boolean; // 是否支持 UI 事件
	showStockForOrder: boolean; // 是否开启订单提交设置
	validateOrderType: number; // 订单校验
	orderWarehouseType: number; // 订货仓库设置
	stockViewType: number; // 订货设置
	enableSetStockView: boolean; // 是否开启了订货通
	stockWarningType: 1 | 2 | 3 | 4; // 库存预警    1: 不开启，2：开启低库存预警，3：开启高库存预警，4：开启高低库存预警
	isNotShowZeroStockType: number; // 不显示可用库存和实际库存均为0的库存记录
	isOnlyShowOnSaleStockType: 1 | 2; // 不显示已下架产品的库存记录:  1:是 2:否
	safetyStockType: number; // 安全库存统一设置
	salesOrderProductDecimal: number; // 销售订单产品精度
	stockDecimal: number; // 库存精度
	scanCodeType: 1 | 2 | 3; // 扫码设置 1: 产品条码 2：批次 3：序列号
	/** 批次库存管理 */
	batchSNSwitch: number; // 批次与序列号启用状态
	willExpireStockWarningType: string; // 临到期库存预警 1:不开启预警 2:开启预警
	/** 采购模块 */
	poSwitch: 0 | 1 | 2; // 采购单启用状态 0: 未开启 1: 开启失败 2: 开启成功
	poDecimal: number; // 采购订单精度，采购订单精度不可大于库存精度
	/** 退换货单管理 */
	exchangeReturnNoteStatus: 1 | 2; // 退换货管理开启状态：1 未开启 2 已开启
	/**ERP库存管理 */
	erpStock: boolean; // ERP 库存是否开启
	erpValidateOrderType: number; // ERP 库存不足是否允许提交
	erpIsNotShowZeroStockType: number; // ERP 是否显示0库存数据
	erpStockDecimal: number; // ERP库存精度
	/**自动收货 */
	daysAfterShipment: number; // 发货单确认后的天数 默认7天
	autoReceiveStatus: 1 | 2; // 自动收货开关 1: 启用 2: 关闭
	autoConfirmReceiptType: 1 | 2; // 参数：1 发货单确认后X天后，自动收货。2：发货单物流签收后，系统自动确认收货。
	/**蒙层 */
	showMask: boolean;
	maskTips: string;
	/* 仓库设置 */
	isOpenWareHousePosition: boolean; // B类库存，是否开启仓位
	/* CPQ */
	// cpqStatus: false, // 产品暂时又不需要了
	cpqDeliveryWay: 1 | 2; // 1 开启 2 不开启
	// 插件控制
	// hasPluginForStockCostAdjustmentSwitch: false, // 是否显示插件 - 成本管理
	hasMultiOrderDeliveryGray: boolean; // 是否在多订单发货功能灰度内
	multiOrderDeliverySwitch: 1 | 2; // 多订单发货 1 开启 2 未开启
	// FIXME
	orderRecordTypeRelateDeliveryMode: any; // 订货模式，根据订单业务类型设置
	kingDeeK3CSyncPluginSwitch: 1 | 2; // 金蝶k3c对接插件开关 1-是  2-否
	kingDeeK3CSyncType: 1 | 2; // 金蝶k3c对接插件同步机制 1-同步库存  2-同步库存明细

	// 互联插件
	// 企业在互联关系中是否上游
	isUpstreamEnterprise: boolean;
	// 企业在互联关系中是否下游
	isDownstreamEnterprise: boolean;
	// 上游是否展示互联插件
	upstreamDisplayStockInterconnection: boolean;
	// 下游是否展示互联插件
	downstreamDisplayStockInterconnection: boolean;
	// 当前身份，上游或下游或无
	currentIdentity: "up" | "down" | "none";
	// 上游列表
	correspondingUpstreamList: UpstreamEnterpriseInfos | [];

	// 下游采购联动入库仓库
	// id
	// purchaseReceivedWarehouseId: string;
	// 仓库名称
	// purchaseReceivedWarehouseName: string;

	// 上游采购业务联动参数设置
	autoGenerateConfig: 0 | 1;

	// 负库存开关
	negativeInventorySwitch: "1" | "2";
	// 是否显示负库存面板
	negative_inventory_allowed_plugin_switch: boolean;
	// 序列号多选
	openMultiSn: "1" | "2";
	// K3C库存可用量
	stock_instant_available_quantity_switch: "1" | "2";
	// http://wiki.firstshare.cn/pages/viewpage.action?pageId=121161972
	// 基于发货单退货开关 1-未开启 2-开启
	delivery_note_returned_goods_switch: "1" | "2";
	// 无源单退货开关 1-未开启 2-开启
	non_note_returned_goods_switch: "1" | "2";

	//  1-强耦合 2-弱耦合
	delivery_note_interaction_model: "1" | "2";
	// 1-自动填充  2-不自动填充
	stock_order_product_fill: "1" | "2";
	isVersionA: boolean;
	isVersionB: boolean;
	isVersionDeliveryOnly: boolean;
	// 订单退货：multiple-基于多订单退货，one-仅可基于一张销售订单退货
	return_goods_invoice_of_sales_order_type: "one" | "multiple";
	// 是否是新版退货单
	// 采购订单计算： 1 用户手动计算；2 系统自动计算
	purchase_order_calculation: "1" | "2",
	// 盘点产品来源： 1 库存；2 产品
	stock_check_note_product_source_type: "1" | "2",
	// 发货单校验/发货通知单： 1 开启发货通知单；2 不开启发货通知单
	delivery_stock_related_check: "1" | "2",
	// 成本管理1-关闭 2-开启
	cost_management_calculate: '1'
	// 渠道库存支持多级订货开关: (true 开启，false 关闭)
	isDistributionStockSupportMultiOrderingEnable: boolean,
	// 采购订单迭代升级-允许删除未入库的采购订单产品 （1:不允许、2:允许）
	enable_edit_purchase_order_when_in_stock: "1"|"2"
}

// A类B类仅开发货单
type Version = "A" | "B" | "DeliveryOnly";

const defaults: Attributes = {
	_reloadNum: 0,
	title: $t("stock.stock_management.stock_management"),
	titleLabel: "",
	hasStockPackage: false,
	isPurchase: false,
	stockType: 0,
	isForErp: false,
	isErpStockEnable: false,
	fields: null,
	deliveryStatus: 0,
	deliveryErrMsg: $t(
		"开启发货单失败请重试。如有疑问请联系纷享客服400-1869-000"
	),
	isPaid: false,
	expressQueryFuncId: 1,
	isAllowModifySalesOrderProduct: 1,
	deliveryNoteDecimal: 2,
	stockSwitch: 1,
	stockStatus: 1,
	hasUiEventModule: false,
	showStockForOrder: false,
	validateOrderType: 1,
	orderWarehouseType: 1,
	stockViewType: 1,
	enableSetStockView: false,
	stockWarningType: 1,
	isNotShowZeroStockType: 2,
	isOnlyShowOnSaleStockType: 1,
	safetyStockType: 1,
	salesOrderProductDecimal: 2,
	stockDecimal: 2,
	scanCodeType: 1,
	batchSNSwitch: 1,
	willExpireStockWarningType: "1",
	poSwitch: 0,
	poDecimal: 2,
	exchangeReturnNoteStatus: 1,
	erpStock: false,
	erpValidateOrderType: 2,
	erpIsNotShowZeroStockType: 2,
	erpStockDecimal: 2,
	daysAfterShipment: 7,
	autoReceiveStatus: 2,
	autoConfirmReceiptType: 1,
	showMask: false,
	maskTips: $t("正在加载..."),
	isOpenWareHousePosition: false,
	cpqDeliveryWay: 2,
	hasMultiOrderDeliveryGray: false,
	multiOrderDeliverySwitch: 2,
	orderRecordTypeRelateDeliveryMode: {},
	kingDeeK3CSyncPluginSwitch: 2,
	kingDeeK3CSyncType: null,
	isUpstreamEnterprise: false,
	isDownstreamEnterprise: false,
	upstreamDisplayStockInterconnection: false,
	downstreamDisplayStockInterconnection: false,
	autoGenerateConfig: 0,
	currentIdentity: "none",
	correspondingUpstreamList: [],
	negativeInventorySwitch: "2",
	negative_inventory_allowed_plugin_switch: false,
	openMultiSn: "1",
	stock_instant_available_quantity_switch: "1",
	delivery_note_returned_goods_switch: "1",
	non_note_returned_goods_switch: "1",
	delivery_note_interaction_model: "1",
	stock_order_product_fill: "1",
	isVersionA: false,
	isVersionB: false,
	isVersionDeliveryOnly: false,
	SSstatus: "1",
	SSupStreamSigner: "0",
	SSdownStreamSigner: "0",
	SSprintTemplateId: "",
	SSsignAppType: "",
	return_goods_invoice_of_sales_order_type: "multiple",
	purchase_order_calculation: "1",
	stock_check_note_product_source_type: "1",
	delivery_stock_related_check: "1",
	cost_management_calculate: '1',
	isDistributionStockSupportMultiOrderingEnable: false,
	enable_edit_purchase_order_when_in_stock: "1"
};

const config = {
	defaults,

	/**
	 * @description 获取各种订单业务类型对应的发货模式, 用于设置订货模式项。
	 * @return {Promise<unknown>}
	 */
	getAllConfigData() {
		// 如果没有开启库存的话，跳过这一步
		if (this.get("stockStatus") !== 2) {
			return Promise.resolve();
		}
		return this.getQueryAllConfigData().then((res) => {
			return new Promise((resolve) => {
				// 还没有初始化
				if (
					!res.configDataMap ||
					!res.configDataMap.order_record_type_relate_delivery_mode
				) {
					// 去初始化。
					this.initOrderRecordTypeRelateDeliveryMode().then(() => {
						// 再调一遍，取回各种订单业务类型对应的订货模式
						resolve(this.getQueryAllConfigData());
					});
				} else {
					// 初始化了就返回
					resolve(res);
				}
			});
		});
	},

	// 版本标志
	initVersion() {
		this.set({
			isVersionA: false,
			isVersionB: false,
			isVersionDeliveryOnly: false,
		});
		// 没开库存 ==> 只开发货单
		if (this.get("stockSwitch") === 1) {
			this.set("isVersionDeliveryOnly", true);
			// 开了库存 ==> A、B 两种情况
		} else {
			if (this.get("isForErp")) {
				this.set("isVersionB", true);
			} else {
				this.set("isVersionA", true);
			}
		}
	},

	// 整个页面刷新
	reload() {
		this.set("_reloadNum", this.get("_reloadNum") + 1);
	},

	// 库存预警开关文案
	getWarningTypeDesc(){
		const stockWarningType = this.get("stockWarningType")
		const recordRemindIsOpen = this.get("recordRemindIsOpen")

		let warningMessage;
		// 旧的
		if(!recordRemindIsOpen){
			switch (stockWarningType) {
				case "1":
					warningMessage = $t("stock.stock_manage.warn.text_no") // 不开启库存预警
					break;
				case "2":
					warningMessage = $t("stock.stock_manage.warn.text_1") // 开启低库存预警，当可用库存小于安全库存时，收到预警
					break;
				case "3":
					warningMessage = $t("stock.stock_manage.warn.text_2") // 开启高库存预警，当可用库存大于最高库存时，收到预警
					break;
				default:
					warningMessage = $t("stock.stock_manage.warn.text_3") // 开启低库存预警与高库存预警，当可用库存小于安全库存或大于最高库存时，收到预警
					break;
			}
			// 新增
		}else{
			switch (stockWarningType) {
				case "1":
					warningMessage = $t("stock.stock_manage.warn.title") // 开启临到期库存预警
					break;
				case "2":
					warningMessage = $t("stock.stock_manage.warn.text_4") // 开启低库存预警与临到期库存预警，当可用库存小于安全库存或临到期时，收到预警
					break;
				case "3":
					warningMessage = $t("stock.stock_manage.warn.text_5") // 开启高库存预警与临到期库存预警，当可用库存大于最高库存或临到期时，收到预警
					break;
				default:
					warningMessage = $t("stock.stock_manage.warn.text_6") // 开启低库存预警、高库存预警、临到期库存预警，当可用库存小于安全库存、大于最高库存、临到期时，收到预警
					break;
			}
		}

		return warningMessage
	},

	...api,

	...format,
};

export default Backbone.Model.extend(config);
