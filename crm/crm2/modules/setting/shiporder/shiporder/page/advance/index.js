/**
 * @description 未开启库存，选择想开启库存类型
 * @TODO 国际语言适配
 */
define(function (require, exports, module) {
	var util = FS.crmUtil;
	var Dialog = require('crm-widget/dialog/dialog');
	var api = require('crm-modules/common/stock/api');

    module.exports = Backbone.View.extend({
		template: require("./tpl-html"),
		switchTpl: require("./switchTpl-html"),
		stopStockTpl: require("./stopstock-html"),

		events: {
			"click .j-init-data": "_handleInitData",
			"click .j-enable-purchase": "_handleEnablePurchase",
			"click .j-enable-delivery": "_handleEnableDelivery",
            "click .j-enable-exchange": "_handleEnableExchangeReturn",
			"click .j-switch-stock": "_handleSwitchStock",
			"click .j-stock-status": "_showStockOptions",
			"click .j-stock-option": "_handleSwitchStockStatus",
			"click .j-restart-stock": "_handleRestartStock",
		},

		isLoading: false,

		initialize: function() {
			this.model.isShowStockLoading();
        },

		render: function() {
			var me = this;
			var data = this.model.toJSON();
			this.model
				.getPrivilege(function(res) {
					var obj = res.privilegeVO || {};
					_.extend(data, obj);
					me.model.set('canDeleteAllStockData', obj.canDeleteAllStockData);
					me.model.set('canChangeStockType', obj.canChangeStockType);
					me.model.set('canRestartStock', obj.canRestartStock);
					me.doRender(data);
				})
				.done(function() {
					me.renderStockData();
				});
        },

		doRender: function(param) {
			var data = param || this.model.toJSON();
			this.$el && this.$el.html(this.template(data));
		},

		renderStockData: function() {
			var me = this;
			this.model.getStockData(function(param) {
				var stockStatus = Number(param.dht_stock_switch) || 0;
				me.model.set('stockStatus', stockStatus)
				param.canRestartStock = me.model.get('canRestartStock');
				param.isForErp = me.model.get('isForErp');
				if (param.canRestartStock && stockStatus === 3) {
					param.tips = param.isForErp ? '重启库存模块将会清除以下对象的全部数据：仓库、库存、发货单、批次、批次库存、序列号、退换货单。'
						: '重启库存模块将会清除以下对象的全部数据：仓库、库存、发货单、批次、批次库存、序列号、入库单、出库单、调拨单、盘点单、供应商、采购订单、退换货单、出入库明细。'
				}
				me.doRender();
				$(".j-advance_switch").html(me.switchTpl(_.extend({}, param, {
					stockType: me.model.get('stockType'),
                    isPurchase: me.model.get('isPurchase')
				})));
			});
		},

		_handleInitData: function(e) {
			var me = this;
			var isForErp = this.model.get('isForErp');
			var confirm;
			var tips = '';
			tips += '<p>您将进行库存模块的数据初始化操作，请确认您已知晓以下注意事项:</p>';
			tips += isForErp ?
				'<p style="margin: 20px 0;">1. 仓库、库存、发货单、批次、批次库存、序列号、退换货单等对象的数据将会被完全清除，不可恢复。</p>'
				:
				'<p style="margin: 20px 0;">1. 仓库、库存、发货单、批次、批次库存、序列号、入库单、出库单、调拨单、盘点单、供应商、采购订单、退换货单、出入库明细等对象的数据将会被完全清除，不可恢复。</p>';
			tips += '<p>2. 清除库存数据如果导致部分订单数据出现异常，将不会针对这部分订单数据做任何修复。</p>'
			confirm = util.confirm(tips, $t("数据初始化"), function () {
				var $target = $(e.currentTarget);
				confirm.destroy();

				me.model.set('showMask', true);
				api.initStockData.circle({submitSelector: $(e.currentTarget)},(data,stop)=>{
					if(data.resultCode == 1) stop(data)
				}).res(res=>{
					me.isLoading = false;
					me.model.set('showMask', false);
					var msg = (res.Value && res.Value.Result.FailureMessage) || null;
					if (msg) {
						FS.crmUtil.remind(3, msg);
					} else {
						FS.crmUtil.remind(1, $t("操作成功"));
					}
					$target
						.removeClass("crm-btn-primary")
						.addClass("crm-btn-disabled");
				}).catch((err)=>{
					me.isLoading = false;
					me.model.set('showMask', false);
					FS.crmUtil.remind(3, $t( err || '启用失败请稍后刷新重试或联系纷享客服'));
				})
			});
		},

		_handleEnablePurchase: function(e) {
			if (this.isLoading) {
				return;
			}
			this.isLoading = true;
			var me = this;
			me.model.set('showMask', true);
			api.enablePO.circle({submitSelector: $(e.currentTarget)},(data,stop)=>{
				if(data.resultCode == 1) stop(data)
			}).res(res=>{
				me.isLoading = false;
				me.model.set('showMask', false);
				var msg = (res.Value && res.Value.Result.FailureMessage) || null;
				if (msg) {
					FS.crmUtil.remind(3, msg);
				} else {
					FS.crmUtil.remind(1, $t("操作成功"));
				}
				me.render()
			}).catch((err)=>{
				me.isLoading = false;
				me.model.set('showMask', false);
				FS.crmUtil.remind(3, $t( err || '启用失败请稍后刷新重试或联系纷享客服'));
			})

			// this.model
				// .enablePO({
				// 	submitSelector: $(e.currentTarget)
				// })
			// 	.done(function() {
			// 		me.renderStockData();
			// 	});
		},

		_handleEnableDelivery: function(e) {
			var me = this;
			this.model
				.enableDeliveryNote({
					submitSelector: $(e.currentTarget)
				})
				.done(function() {
					me.renderStockData();
				});
		},

		_handleEnableExchangeReturn: function(e) {
			if (this.isLoading) {
				return;
			}
			this.isLoading = true;
			this.model.set('showMask', true);
			var me = this;
			api.enableExchangeReturn.circle({
				submitSelector: $(e.currentTarget)
			},(data,stop)=>{
				if(data.resultCode == 1) stop(data)
			}).res(res=>{
				me.isLoading = false;
				me.model.set('showMask', false);
				var msg = (res.Value && res.Value.Result.FailureMessage) || null;
				if (msg) {
					FS.crmUtil.remind(3, msg);
				} else {
					FS.crmUtil.remind(1, $t("操作成功"));
				}
				me.renderStockData();
			}).catch((err)=>{
				me.isLoading = false;
				me.model.set('showMask', false);
				FS.crmUtil.remind(3, $t( err || '启用失败请稍后刷新重试或联系纷享客服'));
			})
        },

        _handleSwitchStock: function (e) {
			var me = this;
			var isForErp = me.model.get('isForErp');
			var confirm;
			var tips = '';
			tips += '<p>您将进行库存模式切换的操作，请确认您已知晓以下注意事项:</p>';
			tips += isForErp ?
				'<p style="margin: 20px 0;">1. 仓库、库存、发货单、批次、批次库存、序列号、退换货单等对象的数据将会被完全清除，不可恢复。</p>'
				:
				'<p style="margin: 20px 0;">1. 仓库、库存、发货单、批次、批次库存、序列号、入库单、出库单、调拨单、盘点单、供应商、采购订单、退换货单、出入库明细等对象的数据将会被完全清除，不可恢复。</p>';
			tips += '<p>2. 清除库存数据如果导致部分订单数据出现异常，将不会针对这部分订单数据做任何修复。</p>'
			// 		库存模式切换
			confirm = util.confirm(tips, $t("stock.stock_manage.info.text1"), function () {
				confirm.destroy();
				
				me.model.set('showMask', true);
				api.changeStockType.circle({submitSelector: $(e.currentTarget)},(data,stop)=>{
					if(data.resultCode == 1) stop(data)
				}).res(res=>{
					me.model.set('showMask', false);
					var msg = (res.Value && res.Value.Result.FailureMessage) || null;
					if (msg) {
						FS.crmUtil.remind(3, msg);
					} else {
						FS.crmUtil.remind(1, $t("操作成功"));
					}
					me.model.set('isForErp', !isForErp);
					me.render();
				}).catch((err)=>{
					me.model.set('showMask', false);
					FS.crmUtil.remind(3, $t( err || '启用失败请稍后刷新重试或联系纷享客服'));
				})

			});
		},

		_showStockOptions: function () {
			var $target = $('.j-stock-options');
			if ($target.is(':hidden')) {
				$target.show()
			} else {
				$target.hide()
			}
		},

		_handleSwitchStockStatus: function (e) {
			var $target = $(e.currentTarget);
			var isForErp = !!$target.data('type');
			var status = $target.data('status');
			var me = this;
			if (+status === 3) {
				return this._handleStopStock();
			}
			if (this.isLoading) {
				return;
			}
			this.isLoading = true;
			me.model.set('showMask', true);
			api.changeStockStatus.circle({stockSwitch: status,isForErp: isForErp},(data,stop)=>{
				if(data.resultCode == 1) stop(data)
			}).res(res=>{
				me.isLoading = false;
				me.model.set('showMask', false);
				var msg = (res.Value && res.Value.Result.FailureMessage) || null;
				if (msg) {
					FS.crmUtil.remind(3, msg);
				} else {
					FS.crmUtil.remind(1, $t("操作成功"));
				}
				me.render()
			}).catch((err)=>{
				me.isLoading = false;
				me.model.set('showMask', false);
				FS.crmUtil.remind(3, $t( err || '启用失败请稍后刷新重试或联系纷享客服'));
			})

		},

		_handleStopStock: function () {
			var me = this;
			var dialog = new Dialog({
				title: $t("库存停用"),
				classPrefix: 'crm-c-dialog crm-c-dialog-stopstock',
				width: 650,
				showBtns: true,
				showScroll: false,
				content: me.stopStockTpl(),
			});
			var timer;
			var destroy = function () {
				timer && clearInterval(timer);
				dialog && dialog.destroy && dialog.destroy();
				dialog = timer = null;
			};
			dialog.on('hide', destroy);
			dialog.on('dialogCancel', destroy);
			dialog.on('dialogEnter', function (e) {
				me.model.set('showMask', true);
				api.changeStockStatus.circle({stockSwitch: 3,isForErp: me.model.get('isForErp')},(data,stop)=>{
					if(data.resultCode == 1) stop(data)
				}).res(res=>{
					destroy();
					FS.crmUtil.remind(1, $t("操作成功"));
					me.model.set('showMask', false);
					me.render()
				}).catch((err)=>{
					me.model.set('showMask', false);
					FS.crmUtil.remind(3, $t( err || '启用失败请稍后刷新重试或联系纷享客服'));
				})
			});
			dialog.on('agree', function (e) {
				var t = this;
				var $target = $(e.target);
				if (t._canAgree) {
					$target.toggleClass('on');
					var $enter = $('[action-type="dialogEnter"]', t.element);
					if ($target.hasClass('on')) {
						$enter.removeClass('btn-disabled');
					} else {
						$enter.addClass('btn-disabled');
					}
				}
			});
			dialog.on('show', function (e) {
				var t = this;
				// 初始设置
				t._canAgree = false;
				$('[action-type="dialogEnter"]', t.element).addClass('btn-disabled');

				// 倒计时设置
				var countdown = 30;
				var $countdown = $('.stopstock-countdown', t.element)
				timer && clearInterval(timer);
				timer = setInterval(function () {
					countdown--;
					if (countdown === 0) {
						t._canAgree = true;
						$('.j-tips-checkbox', t.element).removeAttr("disabled")
						timer && clearInterval(timer);
						timer = null;
						$countdown.text('');
					} else {
						$countdown.text(countdown + 's');
					}
				}, 1000);
			});
			dialog.show();
		},

		_handleRestartStock: function (e) {
			var me = this;
			var isForErp = me.model.get('isForErp');
			var confirm;
			var tips = '';
			tips += '<p>您将进行库存模块重启的操作，请确认您已知晓以下注意事项:</p>';
			tips += isForErp ?
				'<p style="margin: 20px 0;">1. 仓库、库存、发货单、批次、批次库存、序列号、退换货单等对象的数据将会被完全清除，不可恢复。</p>'
				:
				'<p style="margin: 20px 0;">1. 仓库、库存、发货单、批次、批次库存、序列号、入库单、出库单、调拨单、盘点单、供应商、采购订单、退换货单、出入库明细等对象的数据将会被完全清除，不可恢复。</p>';
			tips += '<p>2. 清除库存数据如果导致部分订单数据出现异常，将不会针对这部分订单数据做任何修复。</p>'
			// 库存模块重启
			confirm = util.confirm(tips, $t("stock.stock_manage.info.text2"), function () {
				confirm.destroy();

				me.model.set('showMask', true);
				api.restartStock.circle({},(data,stop)=>{
					if(data.resultCode == 1) stop(data)
				}).res(res=>{
					FS.crmUtil.remind(1, $t("操作成功"));
					me.model.set('showMask', false);
					me.render();
				}).catch((err)=>{
					me.model.set('showMask', false);
					FS.crmUtil.remind(3, $t( err || '启用失败请稍后刷新重试或联系纷享客服'));
				})
			});
		},

		destroy: function() {
			this.$el.off();
			this.$el.empty();
			this.$el = this.el = this.options = null;
		}
	});
});

