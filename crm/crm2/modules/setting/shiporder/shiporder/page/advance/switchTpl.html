<div class="shiporder-advance_item-title">
	<span>{{ $t("功能开关") }}</span>
	## if (obj.tips) { ##
			<div class="shiporder-tip">
					<i class="shiporder-tip_icon">?</i>
					<div class="shiporder-tip_intro">
					{{ $t(obj.tips) }}
					</div>
			</div>
	## } ##
</div>
<div class="shiporder-advance_item-content-container">
	<div class="shiporder-advance_item-content width-border minus-20">
			<div class="shiporder-advance_item-info">
					<div class="shiporder-advance_item-info-title">
							<span class="info-title">{{ $t("发货单") }}</span>
							## if (obj.delivery_note_status == 2) { ##
							<i class="icon-status icon-selected"></i>{{ $t("已开启") }}
							## } else { ##
							<i class="icon-status icon-minus"></i>{{ $t("未开启") }}
							## } ##
					</div>
					## if (obj.delivery_note_status == 2) { ##
							<p class="tips">{{ $t("已初始化发货单对象") }}</p>
					## } else {##
							<p class="tips">{{ $t("开启后，会初始化发货单对象") }}</p>
					## } ##
			</div>
			<div class="shiporder-advance_item-btn">
					## if (obj.delivery_note_status == 0) { ##
					<div class="crm-btn crm-btn-primary j-enable-delivery">{{ $t("启用") }}</div>
					## } ##
			</div>
	</div>
          <div class="shiporder-advance_item-content">
                  <div class="shiporder-advance_item-info">
                          <div class="shiporder-advance_item-info-title">
                                  <span class="info-title">{{ $t("库存") }}</span>
                                  ## if (obj.dht_stock_switch == 1) { ##
                                  <i class="icon-status icon-minus"></i>{{ $t("未开启") }}
                                  ## } else if(obj.dht_stock_switch == 2) { ##
                                  <i class="icon-status icon-selected"></i>{{ $t("已开启") }}
                                  ## } else { ##
                                  <i class="icon-status icon-minus"></i>{{ $t("已停用") }}
                                  ## } ##
                                  ## if (obj.isPurchase == false) { ##
                                  <i class="icon-status icon-minus" style="margin-left: 24px"></i>{{ 'License' + $t("已过期") }}
                                  ## } ##
                          </div>
                          ## if (obj.dht_stock_switch == 2) { ##
                          <p class="tips">{{obj.isForErp ? $t("已初始化仓库、库存两个对象") : $t("已初始化仓库、库存、入库单、出库单、调拨单、盘点单、出入库明细等对象") }}</p>
                          ## } else {##
                          <p class="tips">{{obj.isForErp ? $t("开启后，会初始化仓库、库存两个对象") : $t("开启后，会初始化仓库、库存、入库单、出库单、调拨单、盘点单、出入库明细等对象") }}</p>
                          ## } ##
                  </div>
                  <div class="shiporder-advance_item-btn">
                          ## if (obj.dht_stock_switch == 1 && obj.isPurchase) { ##
                          <div class="crm-btn j-stock-status crm-btn-primary">{{ $t("启用库存") }}</div>
                          <div class="crm-stock-options j-stock-options">
                                  <div class="crm-stock-option j-stock-option" data-type="false" data-status="2">{{ $t("开启完整版库存") }}</div>
                                  <div class="crm-stock-option j-stock-option" data-type="true" data-status="2">{{ $t("开启对接版库存") }}</div>
                          </div>
                          ## } else if (obj.dht_stock_switch == 2) { ##
                          <div class="crm-btn j-stock-option crm-btn-primary" data-status="3">{{ $t("停用库存") }}</div>
                          ## } else { ##
                          <div class="crm-btn j-restart-stock {{obj.canRestartStock ? 'crm-btn-primary' : 'crm-btn-disabled'}}" data-status="3">{{ $t("重启库存") }}</div>
                          ## } ##
                  </div>
          </div>
							## if (obj.dht_stock_switch == 2) { ##
											## if (!obj.isForErp) { ##
															<div class="shiporder-advance_item-content shiporder-advance_item-child">
																			<div class="shiporder-advance_item-info">
																							<div class="shiporder-advance_item-info-title">
																											<span class="info-title">{{ $t("采购") }}</span>
																											## if (obj.purchase_order_status == 2) { ##
																											<i class="icon-status icon-selected"></i>{{ $t("已启用") }}
																											## } else { ##
																											<i class="icon-status icon-minus"></i>{{ $t("未启用") }}
																											<span class="tips">{{ $t("必须先开启库存") }}</span>
																											## } ##
																							</div>
																							## if (obj.purchase_order_status == 2) { ##
																							<p class="tips">{{ $t("已初始化采购订单、采购退货单等对象") }}</p>
																							## } else { ##
																							<p class="tips">{{ $t("开启后，会初始化采购订单、采购退货单等对象") }}</p>
																							## } ##
																			</div>
																			<div class="shiporder-advance_item-btn">
																							## if (+obj.purchase_order_status !== 2) { ##
																							<div class="crm-btn crm-btn-primary j-enable-purchase">{{ $t("启用") }}</div>
																							## } ##
																			</div>
															</div>
											## } ##
							<div class="shiporder-advance_item-content shiporder-advance_item-child">
											<div class="shiporder-advance_item-info">
															<div class="shiporder-advance_item-info-title">
																			## if (obj.dht_exchange_return_note_switch == 1) { ##
																			<!-- <i class="icon-status icon-minus"></i>{{ $t("未启用") }}
																			<span class="tips">{{ $t("必须先开启库存") }}</span> -->
																			## } else { ##
																			<span class="info-title">{{ $t("退换货") }}</span>
																			<i class="icon-status icon-selected"></i>{{ $t("已启用") }}
																			## } ##
															</div>
															## if (obj.dht_exchange_return_note_switch == 1) { ##
															<!-- <p class="tips">{{ $t("开启后，会初始化退款单、退换货单两个对象") }}</p> -->
															<!-- <p class="tips">{{ $t("stock.stock_manage.warn.text10") }}</p> -->
															## } else { ##
															<p class="tips">{{ $t("已初始化退款单、退换货单两个对象") }}</p>
															## } ##
											</div>
											<!-- <div class="shiporder-advance_item-btn">
															## if (obj.dht_exchange_return_note_switch == 1) { ##
															<div class="crm-btn crm-btn-primary j-enable-exchange">启用</div>
															## } ##
											</div> -->
							</div>
							## } ##
</div>