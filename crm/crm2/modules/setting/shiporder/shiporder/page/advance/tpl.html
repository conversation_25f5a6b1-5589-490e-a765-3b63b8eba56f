<div class="shiporder-advance_wrapper">
	<div class="shiporder-advance_item">
		<div class="shiporder-advance_item-title">
			<span>{{ $t("数据初始化") }}</span>
			<div class="shiporder-tip">
				<i class="shiporder-tip_icon">?</i>
				<div class="shiporder-tip_intro">
					## if (obj.stockType === 4) { ##
					{{ $t("数据初始化将会清除发货单的全部数据") }}
					## } else if (obj.isForErp) { ##
					{{ $t("数据初始化将会清除以下对象的全部数据：仓库、库存、发货单、批次、批次库存、序列号、退换货单") }}
					## } else {##
                        {{ $t("数据初始化将会清除以下对象的全部数据：仓库、库存、发货单、批次、批次库存、序列号、入库单、出库单、调拨单、盘点单、供应商、采购订单、退换货单、出入库明细") }}
                    ## }##
                </div>
            </div>
        </div>
        <div class="shiporder-advance_item-content">
            <div class="shiporder-advance_item-info">
                <p>{{ $t("适用场景：试用纷享一段时间后想要清除测试数据") }}</p>
                <p>{{ $t("操作步骤：联系纷享服务中心") }}：400-112-2778，{{ $t("经审核后开通高危操作权限，此时到次日凌晨前，用户可在权限失效前自行操作") }}</p>
            </div>
            <div class="shiporder-advance_item-btn crm-btn j-init-data {{obj.canDeleteAllStockData ? 'crm-btn-primary' : 'crm-btn-disabled'}}">{{ $t("数据初始化") }}</div>
        </div>
    </div>
    ## if (Number(obj.stockStatus) === 2) { ##
        <div class="shiporder-advance_item">
            <div class="shiporder-advance_item-title">
                <span>{{ $t("库存切换") }}</span>
                <div class="shiporder-tip">
                    <i class="shiporder-tip_icon">?</i>
                    <div class="shiporder-tip_intro">
                        ## if (obj.isForErp) { ##
                        {{ $t("切换完整版库存，将会清除以下对象的全部数据：仓库、库存、发货单、批次、批次库存、序列号、退换货单") }}
                        ## } else {##
                        {{ $t("切换到对接版库存，将会清除以下对象的全部数据：仓库、库存、发货单、批次、批次库存、序列号、入库单、出库单、调拨单、盘点单、供应商、采购订单、退换货单、出入库明细") }}
                        ## }##
                    </div>
                </div>
            </div>
            <div class="shiporder-advance_item-content">
                <div class="shiporder-advance_item-info">
                    <p>{{ $t("当前模式") }}：{{obj.isForErp ? $t("对接版") : $t("完整版") }}</p>
                    <p>{{ $t("操作步骤：联系纷享服务中心") }}：400-112-2778，{{ $t("经审核后开通高危操作权限，此时到次日凌晨前，用户可在权限失效前自行操作") }}</p>
                </div>
                <div class="shiporder-advance_item-btn crm-btn j-switch-stock {{obj.canChangeStockType ? 'crm-btn-primary' : 'crm-btn-disabled'}}">
                    {{obj.isForErp ? $t("切换为完整版") : $t("切换为对接版") }}</div>
            </div>
        </div>
    ## } ##
    <div class="shiporder-advance_item j-advance_switch">

    </div>
</div>
