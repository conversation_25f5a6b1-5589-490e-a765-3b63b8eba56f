## _.each(obj.options, function (item) { ##
    <div class="shiporder-base-info_float-setting-item shiporder-set--flex fx-checkbox shiporder-base-info_warning-setting-row">
		<label>
			<input class="shiporder-base-info_radio j-setting-option" type="checkbox"
				name="{{item.name}}" data-value="{{item.value}}"
				{{obj[item.name] ? 'checked' : ''}} />
			<span class="fx-checkbox-icon"></span>
			<div class="shiporder-base-info_float-setting-content">
				{{ item.title }}
				<i class="icon-tips">{{ item.tips }}</i>
			</div>
		</label>
    </div>
## }) ##
		<div class='shiporder-base-info_warning-setting-row'>
			<div class='shiporder-base-info_warning-setting-row'>{{$t('stock.stock_manage.warn.text_7')}}</div>
			  <ul class='shiporder-base-info_warning-setting-list j-warning-setting-list-del'>
					## _.each(obj.recordRemindRolesMap,function (name,id){ ##
					<li class='shiporder-base-info_warning-setting-list-item' >{{name}} <span class='shiporder-base-info_warning-setting-list-item__close' data-id='{{id}}'></span></li>
					## }) ##
					<li class='shiporder-base-info_warning-setting-list-btn j-waring-pick-role'>{{'+' + $t('stock.stock_manage.warn.text_8')}}</li>
				</ul>
		</div>
<div class="shiporder-base-info_float-setting-btns">
    <div class="setting-btn crm-btn-primary j-confirm" data-action="{{obj.name}}"> {{$t("确定")}}</div>
    <div class="setting-btn btn-cancel j-cancel" data-action="{{obj.name}}">{{$t("取消")}}</div>
</div>
