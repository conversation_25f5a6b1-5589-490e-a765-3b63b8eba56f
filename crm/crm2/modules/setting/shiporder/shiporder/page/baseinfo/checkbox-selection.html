## _.each(obj.options, function (item) { ##
    <div class="shiporder-base-info_float-setting-item shiporder-set--flex fx-checkbox">
		<label>
			<input class="shiporder-base-info_radio j-setting-option" type="checkbox"
				name="{{item.name}}" data-value="{{item.value}}"
				{{obj[item.name] ? 'checked' : ''}} />
			<span class="fx-checkbox-icon"></span>
			<div class="shiporder-base-info_float-setting-content">
				{{ item.title }}
				<i class="icon-tips">{{ item.tips }}</i>
			</div>
		</label>
    </div>
## }) ##
<div class="shiporder-base-info_float-setting-btns">
    <div class="setting-btn crm-btn-primary j-confirm" data-action="{{obj.name}}"> {{$t("确定")}}</div>
    <div class="setting-btn btn-cancel j-cancel" data-action="{{obj.name}}">{{$t("取消")}}</div>
</div>
