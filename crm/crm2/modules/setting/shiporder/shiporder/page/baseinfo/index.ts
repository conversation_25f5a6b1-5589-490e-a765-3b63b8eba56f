/**
 * @description 未开启库存，选择想开启库存类型
 * @TODO 国际语言适配
 */
import {
	isAllowSetStockConf,
	commonGetConfigValueByKey,
	setCoupling,
	commonGetConfigValueByKeys,
	setFillProduct,
	setReturnGoodsInvoiceOfSalesOrderType,
	setPurchaseOrderCalculation,
	setStockCheckNoteProductSourceType,
	setDeliveryStockRelatedCheck,
	setEnableEditPurchaseOrderWhenInStock,
	getRecordRemindByRoleSwitch,
	saveRecordRemindByRoleSwitch,
	asyncSetConfigValue
} from 'crm-modules/common/stock/api';

var crmUtil = FS.crmUtil;
    var CONSTANT = {
		orderWarehouseType: 'WAREHOUSE', // 订货模式-统一设置,
		orderMode: 'ordermode', // 订货模式,
		validate: 'VALIDATE', // 订单校验,
		edit: 'EDIT', // 订单编辑
        stock: 'STOCK', // 库存显示
		coupling: 'COUPLING', // 耦合度显示
		fillProduct: 'FILLPRODUCT', // 订单产品填充
        isNotShowZeroStockType: 'isNotShowZeroStockType', // 显示库存为0的记录
        isOnlyShowOnSaleStockType: 'isOnlyShowOnSaleStockType', // 显示已下架商品库存记录
        warning: 'STOCK_WARNING_TYPE', // 库存预警
        safety: 'STOCK_SAFETY_TYPE', // 库存安全
		scan: 'SCAN', // 扫码设置
		lowStockWarning: 'lowStockWarning', // 开启低库存预警
		highStockWarning: 'highStockWarning', // 开启高库存预警
		recordRemindIsOpen: 'recordRemindIsOpen', // 开启临到期预警
		stockCheckFreezeWarehouse: 'stockCheckFreezeWarehouse', // 盘点冻结仓库
		stockCheckNoteUpdateStockType: 'stockCheckNoteUpdateStockType', // 盘点后库存调整
		requisitionNoteAutomaticInbound: 'requisitionNoteAutomaticInbound', // 调拨确认入库支持配置
		requisitionNoteAutomaticOutbound: 'requisitionNoteAutomaticOutbound', // 调拨出库支持配置
		returnGoodsInvoiceOfSalesOrderType: 'returnGoodsInvoiceOfSalesOrderType', // 订单退货
		setValidateOrderTypeByRule: 'setValidateOrderTypeByRule', // 根据订单业务类型设置不同的校验规则
		PURCHASE_ORDER_CALCULATION: 'purchase_order_calculation', // 采购订单计算
		STOCK_CHECK_NOTE_PRODUCT_SOURCE_TYPE: 'stock_check_note_product_source_type', // 盘点产品来源
		DELIVERY_STOCK_RELATED_CHECK: 'delivery_stock_related_check', // 发货单校验/发货通知单
		ENABLE_EDIT_PURCHASE_ORDER_WHEN_IN_STOCK: 'enable_edit_purchase_order_when_in_stock', // 入库后采购订单能否编辑
	};

  let selectorBox = null

	var parseTipHtml = function (list) {
		var tipHtml = '';
		_.each(list, function (item) {
			if (item.type === 'b') {
				tipHtml += ('<b>' + _.escape(item.label) + '</b>');
			} else {
				tipHtml += ('<p>' + _.escape(item.label) + '</p>');
			}
		});
		return tipHtml;
	};

    const BaseInfo = Backbone.View.extend({
		template: require("./tpl-html"),
		templateForC: require("./tpl4C-html"),
		templateForDelivery: require('./tpl-delivery-html'),
		radioSelection: require("./radio-selection-html"),
		checkboxSelection: require("./checkbox-selection-html"),
		checkboxSelection4Warning: require("./checkbox-selection-4-warning-html"),
		radioSelection4Warehouse: require('./radio-selection-4-warehouse-html'),

		tempData: {
			orderMode: null,  // 对应接口的orderWarehouseTypeConfigRangeCode, 订货模式，企业级或订单业务类型级
			orderWarehouseType: null,
			validateOrderType: null,
			isNotShowZeroStockType: null,
			isOnlyShowOnSaleStockType: null,
			autoReceiveStatus: null,
			allowOrderWarehouseBlank: null,
			lowStockWarning: null,
			highStockWarning: null,
			stockWarningType: null,
			stockCheckNoteUpdateStockType: null,
			requisitionNoteAutomaticInbound: null,
			requisitionNoteAutomaticOutbound: null,
			returnGoodsInvoiceOfSalesOrderType: null,
		},

		events: {
			"click .j-decimal": "_handleSettingDecimal",
			"click .j-decimal-confirm": "_handleConfirmDecimal",
			"click .j-validate": "_handleSettingValidate",
			"click .j-stock": "_handleSettingStock",
			"click .j-delivery": "_handleSettingDelivery",
			"click .j-cancel": "_handleCancel",
			"click .j-confirm": "_handleConfirm",
			"click .j-warehouse": "_handleSettingWarehouse",
			"click .j-warehouse-setting": "_handleWarehouseConfirm",
			"click .j-setting-option": "_handleOptionClick",
			"click .j-warning": "_handleSettingWarning",
			"click .j-waring-pick-role": "handlePickRecordRemindRole",
			"click .j-safety": "_handleSettingSafety",
			"click .j-scan": "_handleSettingScan",
			"click .j-stock-switch": "_handleStockSwitch",
			"click .j-auto-receive": "_handleAutoReceive",
			"click .j-auto-receive-radio": "_handleAutoReciveRadio",
			"click .j-edit": "_handleEditOrder",
			"click .j-allow-blank": "_handleBlankOrder",
			"click .j-stock-grey-check-switch": "_handleStockGreyCheckSwitch",
			"click .j-freeze": "_handleStockCheckFreezeWarehouse",
			"click .j-order-delivery-mode_edit": "_handleOrderDeliveryModeEdit",
			"click .j-setting-stock-check-note-update-stock-type": "_handleStockCheckNoteUpdateStockType",
			"click .j-setting-requisition-note-automatic-inbound": "_handleRequisitionNoteAutomaticInbound",
			"click .j-setting-requisition-note-automatic-outbound": "_handleRequisitionNoteAutomaticOutbound",
			"click .j-setting-return-goods-invoice-of-sales-order-type": "_handleReturnGoodsInvoiceOfSalesOrderType",
			"click .j-coupling-edit": "_handleEditCoupling",
			"click .j-fillProduct-edit": "_handleFillProduct",
			"click .j-stock-scan-switch": "_handleStocScanSwitch",
			"click .j-purchase-order-calculation": "_handlePurchaseOrderCalculation",
			"click .j-stock-check-note-product-source-type": "_handleStockCheckNoteProductSourceType",
			"click .j-setting-delivery-stock-related-check": "_handleDeliveryStockRelatedCheck",
			"click .j-setting-enable_edit_purchase_order_when_in_stock": "_handlePurchaseOrderWhenInStock",
			"click .j-warning-setting-list-del": "handleWarningSettingListDel",
			"click .j-setting-location_management": "_handleLocationManagementSwitch",
		},

		initialize: function() {
			this.listenTo(this.model, "change:orderWarehouseType", this._toggleBlankOrderOption);
		},

		render: function() {
			return Promise.all([
				// 选项值
				commonGetConfigValueByKeys({
					keys: [
						"delivery_note_interaction_model",
						"stock_order_product_fill",
						"return_goods_invoice_of_sales_order_type",
						"purchase_order_calculation",
						"stock_check_note_product_source_type",
						"delivery_stock_related_check",
						"enable_edit_purchase_order_when_in_stock",
						"distribution_stock_switch", // 渠道库存：1 关闭；2开启
						"spare_part_inventory_switch", // 备件库存：1 关闭；2开启
						"location_management", // 货位：0 关闭，1 开启；
					],
					isAllConfig: false,
				}),
				getRecordRemindByRoleSwitch()
			])
				.then((res) => {
					console.log('promisssss', res);
					const [configVal,recordRemindConfig] = res
					this.model.set({
						delivery_note_interaction_model: configVal.delivery_note_interaction_model, // 耦合度
						stock_order_product_fill: configVal.stock_order_product_fill, // 订单产品填充
						return_goods_invoice_of_sales_order_type: configVal.return_goods_invoice_of_sales_order_type, // 订单退货
						purchase_order_calculation: configVal.purchase_order_calculation, // 采购订单计算
						stock_check_note_product_source_type: configVal.stock_check_note_product_source_type, // 盘点产品来源
						delivery_stock_related_check: configVal.delivery_stock_related_check, // 发货单校验/发货通知单
						enable_edit_purchase_order_when_in_stock: configVal.enable_edit_purchase_order_when_in_stock, // 入库后采购订单能否编辑
						distribution_stock_switch: configVal.distribution_stock_switch, // 渠道库存,
						spare_part_inventory_switch: configVal.spare_part_inventory_switch, // 备件库存
						location_management: configVal.location_management, // 货位
						recordRemindIsOpen: recordRemindConfig.recordRemindIsOpen, // 库存预警
						recordRemindRolesMap: recordRemindConfig.recordRemindRolesMap,
						stockWarningType: recordRemindConfig.stockWarningType
					});
				})
				.catch(() => {
					console.error('获取退货单灰度失败');
				})
				.then(() => {
					var stockType = +this.model.get('stockType');
					if (stockType === 3) {
						this._renderCTemplate();
					} else if (stockType === 4) {
						this.renderPageByDelivery();
						return;
					} else {
						this.model.getBaseConfig().then((data) => {
							this._getScanInfo();
							// B类
							if (data.isForErp) {
								this.model.set({
									isDistributionStockSupportMultiOrderingEnable: data.isDistributionStockSupportMultiOrderingEnable
								});
								this.model.getAllConfigData().then(res => {
									this.model.setOrderRecordTypeRelateDeliveryMode(res);
									this._renderCommonTemplate();
								});
								return;
							}
							// A类
							this.model
								.getAllConfigData()
								.then((res) => {
									return this.model.setOrderRecordTypeRelateDeliveryMode(res);
								})
								.then(() => {
									return isAllowSetStockConf({
										configTypes: ['stock_grey_check_switch']
									});
								})
								.then((res) => {
									return this.model.set({
										isShowStockGreyCheckSwitch: res.stock_grey_check_switch,
									});
								})
								.then(() => {
									return this.model.getStockData(() => {
									}, [
										'stock_grey_check_switch',
										'stock_check_freeze_warehouse'
									]);
								})
								.then((params) => {
									this.model.set(
										'stockGreyCheckSwitch',
										params['stock_grey_check_switch']
									);
									// me.model.set('stockCheckFreezeWarehouse', params['stock_check_freeze_warehouse']);
									// data.stockGreyCheckSwitch = params['stock_grey_check_switch'];
									// data.stockCheckFreezeWarehouse = params['stock_check_freeze_warehouse'];
									this._renderCommonTemplate();

								});
						});
						this.model.isShowStockLoading();
					}
				});
		},

		_renderCommonTemplate: function(param) {
			var data = param || this.model.toJSON();
			if (!data.scanCodeType) {
				data.scanCodeType = Number(this.model.get('scanCodeType'));
			}
			data.warningTypeDesc = this.model.getWarningTypeDesc()
			var template = this.template(data);
			this.$el && this.$el.html(template);
      return this.rendered()
		},

		_renderCTemplate: function() {
			var me = this;
			this.model.fetchErpInfo(function (data) {
				var template = me.templateForC(data)
				me.$el && me.$el.html(template);
        return this.rendered()
      })
		},

		renderPageByDelivery() {
			this.model.getBaseConfig().then(data => {
				this.$el && this.$el.html(this.templateForDelivery(this.model.toJSON()));
        return this.rendered()
      });
		},

		_handleLocationManagementSwitch(e) {
			let $target = $(e.currentTarget);
			let me = this;
			const value = me.model.get('location_management');
			if ($target.hasClass("switch-disabled") && value === '1') {
				return;
			}

			let tips = '<p>' + $t('开启后，此功能不可关闭，是否确认开启此功能？') + '</p>';
			let confirm = crmUtil.confirm(tips, $t("温馨提示"), function () {
				confirm.destroy();

				me.model.set('maskTips', $t('正在加载'));
				me.model.set('showMask', true);

				asyncSetConfigValue()
					.circle({ key: 'location_management', value: '1', token: '' }, (data, stop) => {
						if (data.resultCode == 1) stop(data);
					})
					.res(res => {
						me.model.set('showMask', false);

						const msg = (res.Value && res.Value.Result.FailureMessage) || null;
						if (msg) {
							FS.crmUtil.remind(3, msg);
							return;
						}

						FS.crmUtil.remind(1, $t('操作成功'));
						me.model.set('location_management', '1');
						$target.addClass("switch-on switch-disabled");
					})
					.catch(err => {
						me.model.set('showMask', false);
						FS.crmUtil.remind(3, $t(err || '启用失败请稍后刷新重试或联系纷享客服'));
					});
			});
		},

      _handleShowSettingDetail: function(e) {
			var $target = $(e.currentTarget);
			$target.addClass("active");
			$target.find(".space-between").hide();
			$target.find(".shiporder-base-info_setting-detail").show();
		},

      // 渲染后触发
      rendered() {
        const params = FS.util.getTplQueryParams();
        // TODO
        if (params?.param) {
          return this.handleFocusOnDecimalSetting(params);
        }
      },

      handleFocusOnDecimalSetting(params) {
        const $target = $('.j-decimal')
        $target.addClass("active");
        $target.find(".space-between").hide();
        $target.find(".shiporder-base-info_setting-detail").show();
        $('.shiporder-base-info_item-content-stock').addClass('shiporder-shake')
      },

		_handleHideSettingDetail: function(e) {
			var $target = $(e.currentTarget);
			var $parent = $target.parents(".shiporder-base-info_item");
			$parent.removeClass("active");
			$parent.find(".space-between").show();
			$parent.find(".shiporder-base-info_setting-detail").hide();
		},

		_handleSettingDecimal: function(e) {
			this._handleShowSettingDetail(e);
		},

		_checkDecimals: function(
			salesOrderProductDecimal,
			stockDecimal,
			deliveryNoteDecimal,
			purchaseOrderDecimal
		) {
			if (stockDecimal < salesOrderProductDecimal) {
				return crmUtil.remind(3, $t("stock.stock_manage.warn.text12")); // 库存精度不可小于订单产品精度
			}
			if (
				deliveryNoteDecimal < salesOrderProductDecimal ||
				deliveryNoteDecimal > stockDecimal
			) {
				return crmUtil.remind(3, $t("发货单精度应不可小于订单产品精度，不可大于库存精度"));
			}
			if (purchaseOrderDecimal > stockDecimal) {
				return crmUtil.remind(3, $t("采购订单的精度不可大于库存精度"));
			}
			return true;
		},

		_handleConfirmDecimal: function(e) {
			e.stopPropagation();
			var stockType = +this.model.get('stockType');
			var salesOrderProductDecimal = Number(
				this.model.get("salesOrderProductDecimal")
			); // 订货单精度
			var deliveryNoteDecimal = Number(
				$(".j-delivery-decimal").attr("value")
			); // 发货单精度

			if (stockType === 4) {
				if (deliveryNoteDecimal < salesOrderProductDecimal) {
					return crmUtil.remind(3, $t("发货单精度应不可小于订单产品精度"));
				}
				this.model.setDecimal({
					deliveryNoteDecimal,
				}, $(e.target));
				return;
			}

			var stockDecimal = Number($(".j-stock-decimal").attr("value")); // 库存精度
			var purchaseOrderDecimal = Number($(".j-po-decimal").attr("value")); // 采购单精度

			var me = this;
			if (stockType === 3) { // C类库存
				this.model.set('stockDecimal', stockDecimal);
				this.model.setErpStockConfig(function () {
					crmUtil.remind(1, $t("stock.stock_manage.info.text3")) // 操作成功！
					me._handleHideSettingDetail(e);
				}, $(e.target));
			} else {
				var param: { [key: string]: any } = {};
				if (stockDecimal != null) {
					param.stockDecimal = stockDecimal
				}
				if (deliveryNoteDecimal != null) {
					param.deliveryNoteDecimal = deliveryNoteDecimal
				}
				if (purchaseOrderDecimal != null) {
					param.purchaseOrderDecimal = purchaseOrderDecimal
				}
				if (
					this._checkDecimals(
						salesOrderProductDecimal,
						stockDecimal,
						deliveryNoteDecimal,
						purchaseOrderDecimal
					)
				) {
					this.model.setDecimal(param, $(e.target), (data) => {
						// 其他页面的打开的
						if (window.opener) {
							window.opener.postMessage(JSON.stringify({
								isSpareMessage: true,
								shouldUpdateSpareSetting: true
							}), location.origin);
						}
						me.render();
					});
				}
			}
		},

		_handleCancel: function(e) {
			e.stopPropagation();
			this._handleHideSettingDetail(e);
			var action = $(e.currentTarget).data("action");
			console.log('_handleCancel action:', action);
			switch (action) {
				case CONSTANT.orderMode:
					this.tempData.orderMode = null;
					this.tempData.orderWarehouseType = null;
					this.tempData.allowOrderWarehouseBlank = null;
					break;
				case CONSTANT.validate:
					this.tempData.validateOrderType = null;
					break;
				case CONSTANT.stock:
					this.tempData.isNotShowZeroStockType = null;
					break;
				case CONSTANT.edit:
					this.tempData.isAllowModifySalesOrderProduct = null;
					break;
				case CONSTANT.stockCheckFreezeWarehouse:
					this.tempData.stockCheckFreezeWarehouse = null;
					break;
				case CONSTANT.stockCheckNoteUpdateStockType:
					this.tempData.stockCheckNoteUpdateStockType = null;
					break;
				case CONSTANT.requisitionNoteAutomaticInbound:
					this.tempData.requisitionNoteAutomaticInbound = null;
					break;
				case CONSTANT.requisitionNoteAutomaticOutbound:
					this.tempData.requisitionNoteAutomaticOutbound = null;
					break;
				case CONSTANT.coupling:
					this.tempData.delivery_note_interaction_model = null;
					break;
				case CONSTANT.fillProduct:
					this.tempData.stock_order_product_fill = null;
					break;
				case CONSTANT.PURCHASE_ORDER_CALCULATION:
					this.tempData.purchase_order_calculation = null;
					break;
				case CONSTANT.STOCK_CHECK_NOTE_PRODUCT_SOURCE_TYPE:
					this.tempData.stock_check_note_product_source_type = null;
					break;
				case CONSTANT.DELIVERY_STOCK_RELATED_CHECK:
					this.tempData.delivery_stock_related_check = null;
					break;
				case CONSTANT.ENABLE_EDIT_PURCHASE_ORDER_WHEN_IN_STOCK:
					this.tempData.enable_edit_purchase_order_when_in_stock = null;
					break;
			}
		},

		_handleConfirm: function(e) {
			e.stopPropagation();
			var action = $(e.currentTarget).data("action");
			switch (action) {
				case CONSTANT.orderMode:
					this._handleWarehouseConfirm(e);
					break;
				case CONSTANT.validate:
					this._handleValidateConfirm(e);
					break;
				case CONSTANT.stock:
					this._handleStockConfirm(e);
					break;
				case CONSTANT.warning:
					this._handleWarningConfirm(e);
					break;
				case CONSTANT.safety:
					this._handleSafetyConfirm(e);
					break;
				case CONSTANT.scan:
					this._handleScanConfirm(e);
					break;
				case CONSTANT.edit:
					this._handleEditConfirm(e);
					break;
				case CONSTANT.stockCheckFreezeWarehouse:
					this._handleFreezeConfirm(e);
					break;
				case CONSTANT.stockCheckNoteUpdateStockType:
					this._handleStockCheckNoteUpdateStockTypeConfirm(e);
					break;
				case CONSTANT.requisitionNoteAutomaticInbound:
					this._handleRequisitionNoteAutomaticInboundConfirm(e);
					break;
				case CONSTANT.requisitionNoteAutomaticOutbound:
					this._handleRequisitionNoteAutomaticOutboundConfirm(e);
					break;
				case CONSTANT.coupling:
					this._handleSetCoupling(e);
					break;
				case CONSTANT.fillProduct:
					this._handleFillProductConfirm(e);
					break;
				case CONSTANT.returnGoodsInvoiceOfSalesOrderType:
					this._handleReturnGoodsInvoiceOfSalesOrderTypeConfirm(e);
					break;
				case CONSTANT.PURCHASE_ORDER_CALCULATION:
					this._handlePurchaseOrderCalculationConfirm(e);
					break;
				case CONSTANT.STOCK_CHECK_NOTE_PRODUCT_SOURCE_TYPE:
					this._handleStockCheckNoteProductSourceTypeConfirm(e);
					break;
				case CONSTANT.DELIVERY_STOCK_RELATED_CHECK:
					this._handleDeliveryStockRelatedCheckConfirm(e);
					break;
				case CONSTANT.ENABLE_EDIT_PURCHASE_ORDER_WHEN_IN_STOCK:
					this._handleEnableEditPurchaseOrderWhenInStockConfirm(e);
					break;
			}
		},
		_handleReturnGoodsInvoiceOfSalesOrderTypeConfirm(e) {
			var _value = this.tempData.returnGoodsInvoiceOfSalesOrderType || this.model.get('return_goods_invoice_of_sales_order_type');
			if (_value === 1) {
				_value = 'one';
			} else if (_value === 2) {
				_value = 'multiple';
			}
			console.log('设置订单退货', _value);
			let me = this;
			setReturnGoodsInvoiceOfSalesOrderType(_value).then(res => {
				me.render();
			}).catch(e => {
				console.log(e);
				crmUtil.remind(3, e);
			})
		},

		_handleSetCoupling(e){
			var _value = this.tempData.delivery_note_interaction_model || this.model.get('delivery_note_interaction_model');
			console.log('设置耦合度', _value)
			let me = this;
			setCoupling(_value).then(res=>{
				me.render();
			}).catch(e=>{
				console.log(e)
				crmUtil.remind(3, e)
			})
		},

		// 订单产品填充，点击确定
		_handleFillProductConfirm(e){
			var _value = this.tempData.stock_order_product_fill || this.model.get('stock_order_product_fill');
			console.log('订单产品填充，点击确定', _value)
			let me = this;
			setFillProduct(_value).then(res=>{
				me.render();
			}).catch(e=>{
				console.log(e)
				crmUtil.remind(3, e)
			})
		},

		// 采购订单计算，点击确定
		_handlePurchaseOrderCalculationConfirm(e){
			var _value = this.tempData.purchase_order_calculation || this.model.get('purchase_order_calculation');
			console.log('采购订单计算，点击确定', _value)
			let me = this;
			setPurchaseOrderCalculation(_value).then(res=>{
				me.render();
			}).catch(e=>{
				console.log(e)
				crmUtil.remind(3, e)
			})
		},

		// 盘点产品来源，点击确定
		_handleStockCheckNoteProductSourceTypeConfirm(e){
			var _value = this.tempData.stock_check_note_product_source_type || this.model.get('stock_check_note_product_source_type');
			console.log('盘点产品来源，点击确定', _value)
			let me = this;
			setStockCheckNoteProductSourceType(_value).then(res=>{
				me.render();
			}).catch(e=>{
				console.log(e)
				crmUtil.remind(3, e)
			})
		},

		// 发货通知单，点击确定
		_handleDeliveryStockRelatedCheckConfirm(e){
			var _value = this.tempData.delivery_stock_related_check || this.model.get('delivery_stock_related_check');
			console.log('发货通知单，点击确定', _value)
			let me = this;
			setDeliveryStockRelatedCheck(_value).then(res=>{
				me.render();
			}).catch(e=>{
				console.log(e)
				crmUtil.remind(3, e)
			})
		},

		// 入库后，采购订单能否编辑
		_handleEnableEditPurchaseOrderWhenInStockConfirm(e){
			var _value = this.tempData.enable_edit_purchase_order_when_in_stock || this.model.get('enable_edit_purchase_order_when_in_stock');
			let me = this;
			setEnableEditPurchaseOrderWhenInStock(_value).then(res=>{
				me.render();
			}).catch(e=>{
				console.log(e)
				crmUtil.remind(3, e)
			})
		},



			_handleOptionClick: function(e) {
			e.stopPropagation();
			var name = $(e.currentTarget).attr("name");
			var value = $(e.currentTarget).data("value");
			switch (name) {
				case CONSTANT.orderMode:
					this.tempData.orderMode = value;
					if (value == 1) {
						$('.crm-s-shiporder .shiporder-base-info_float-setting-item--child').show();
						$('.crm-s-shiporder .shiporder-base-info_order-delivery-mode').hide();
					} else {
						$('.crm-s-shiporder .shiporder-base-info_float-setting-item--child').hide();
						$('.crm-s-shiporder .shiporder-base-info_order-delivery-mode').show();
					}
					break;
				case CONSTANT.orderWarehouseType:
					this.tempData.orderWarehouseType = value;
					if (+value === 2) {
						$('.j-allow-blank').hide();
					} else {
						$('.j-allow-blank').show();
					}
					break;
				case CONSTANT.validate:
					this.tempData.validateOrderType = value;
					break;
				case CONSTANT.isNotShowZeroStockType:
					this.tempData.isNotShowZeroStockType = $(e.currentTarget).is(":checked") ? 1 : 2;
					break;
				case CONSTANT.isOnlyShowOnSaleStockType:
					this.tempData.isOnlyShowOnSaleStockType = $(e.currentTarget).is(":checked") ? 1 : 2;
					break;
				case CONSTANT.lowStockWarning:
					var lowStockValue = $(e.currentTarget).is(":checked")
					this.tempData.lowStockWarning = lowStockValue;
					break;
				case CONSTANT.highStockWarning:
					var highStockValue = $(e.currentTarget).is(":checked")
					this.tempData.highStockWarning = highStockValue;
					break;
				case CONSTANT.recordRemindIsOpen:
					var recordRemindIsOpen = $(e.currentTarget).is(":checked")
					this.tempData.recordRemindIsOpen = recordRemindIsOpen;
					break;
				case CONSTANT.warning:
					this.tempData.stockWarningType = value;
					break;
				case CONSTANT.safety:
					this.tempData.safetyStockType = value;
					break;
				case CONSTANT.scan:
					this.tempData.scanCodeType = value;
					break;
				case CONSTANT.edit:
					this.tempData.isAllowModifySalesOrderProduct = value;
					break;
				case CONSTANT.stockCheckFreezeWarehouse:
					this.tempData.stockCheckFreezeWarehouse = value;
					break;
				case CONSTANT.stockCheckNoteUpdateStockType:
					this.tempData.stockCheckNoteUpdateStockType = value;
					break;
				case CONSTANT.requisitionNoteAutomaticInbound:
					this.tempData.requisitionNoteAutomaticInbound = value;
					break;
				case CONSTANT.requisitionNoteAutomaticOutbound:
					this.tempData.requisitionNoteAutomaticOutbound = value;
					break;
				case CONSTANT.coupling:
					this.tempData.delivery_note_interaction_model = value;
					break;
				case CONSTANT.fillProduct:
					this.tempData.stock_order_product_fill = value;
					break;
				case CONSTANT.returnGoodsInvoiceOfSalesOrderType:
					this.tempData.returnGoodsInvoiceOfSalesOrderType = value;
					break;
				case CONSTANT.PURCHASE_ORDER_CALCULATION:
					this.tempData.purchase_order_calculation = value;
					break;
				case CONSTANT.STOCK_CHECK_NOTE_PRODUCT_SOURCE_TYPE:
					this.tempData.stock_check_note_product_source_type = value;
					break;
				case CONSTANT.DELIVERY_STOCK_RELATED_CHECK:
					this.tempData.delivery_stock_related_check = value;
					break;
				case CONSTANT.ENABLE_EDIT_PURCHASE_ORDER_WHEN_IN_STOCK:
					this.tempData.enable_edit_purchase_order_when_in_stock = value;
					break;
			}
		},

		/**
		 * 订单校验
		 */
		_handleSettingValidate: function(e) {
			if ($(e.currentTarget).hasClass("item-disabled")) {
				return false;
			}
			var validateOrderType = this.model.get("validateOrderType");
			var stockType = this.model.get('stockType');
			var isForErp = this.model.get('isForErp');
			var isStockTypeA = +stockType === 2 && !isForErp;
			$(".j-validate-detail").html(
				this.radioSelection({
					options: [
						{
							title: $t("可用库存数量不足时，不可提交订单"),
							value: 1,
							tipHtml: isStockTypeA ? [
								{
									type: 'b',
									label: $t("可用库存数量不足时，不可提交订单"),
								},
								{
									label: $t("此设置项下，创建出库单/调拨单时，校验可用库存，可用库存不足不可创建出库单/调拨单。"),
								},
							] : ''
						},
						{
							title: $t("可用库存数量不足时，仍可提交订单"),
							value: 2,
							tipHtml: isStockTypeA ? [
								{
									type: 'b',
									label: $t("可用库存数量不足时，仍可提交订单"),
								},
								{
									label: $t("此设置项下，创建出库单/调拨单时，校验实际库存，实际库存不足不可创建出库单/调拨单。"),
								},
							] : ''
						},
						{
							title: $t("根据订单的业务类型，设置不同的校验规则"),
							value: 3,
							link: {
								dataType: CONSTANT.setValidateOrderTypeByRule,
								text: $t('设置'),
							}
						}
					],
					value: validateOrderType,
					name: CONSTANT.validate,
					parseTipHtml: parseTipHtml
				})
			);
			$('.shiporder-base-info_float-setting-item__href').click(this._handleSettingItemHrefClick.bind(this));
			this._handleShowSettingDetail(e);
		},

		// 销售订单根据业务类型设置库存校验规则
		_handleSettingItemHrefClick(e) {
			e.stopPropagation();
			this.tempData.validateOrderType = 3;
			const el = e.currentTarget;
			const type = $(el).data('type');
			const hash = $(el).data('hash');
			switch (type) {
				case CONSTANT.setValidateOrderTypeByRule:
					this._handleValidateConfirm(e).then(() => {
						FS.tpl.navRouter.navigate(hash, {
							trigger: true,
							mod: 'crmmanage'
						});
					});
					break;
				default:
					break;
			}
		},

		// 设置订单校验类型
		_handleValidateConfirm: function(e) {
			return new Promise((resolve => {
				var me = this;
				var stockType = this.model.get('stockType');
				var data = this.tempData.validateOrderType || this.model.get("validateOrderType");
				this.model.set("validateOrderType", data);
				if (+stockType === 3) {
					this.model.setErpStockConfig(function () {
						crmUtil.remind(1, $t("操作成功!"))
						// @ts-ignore
						resolve()
						me._handleHideSettingDetail(e);
						me.render();
					}, $(e.target));
				} else {
					this.model.setValidateOrderType(data, $(e.target), function() {
						// @ts-ignore
						resolve()
						me.render();
					});
				}
			}))
		},

		/**
		 * 订单编辑
		 */
		_handleEditOrder: function (e) {
			var isAllowModifySalesOrderProduct = this.model.get("isAllowModifySalesOrderProduct");
			$(".j-edit-detail").html(
				this.radioSelection({
					options: [
						{
							title: $t("订单部分发货/全部发货时，允许编辑订单产品的数量和种类"),
							value: 2,
						},
						{
							title: $t("订单部分发货/全部发货时，不允许编辑订单产品的数量和种类"),
							value: 1,
						}
					],
					value: isAllowModifySalesOrderProduct,
					name: CONSTANT.edit,
				})
			);
			this._handleShowSettingDetail(e);
		},

		// 耦合度编辑
		_handleEditCoupling: function (e) {
			var delivery_note_interaction_model = this.model.get("delivery_note_interaction_model");
			console.log('`````````````````````````',delivery_note_interaction_model)
			$(".j-edit-coupling-detail").html(
				this.radioSelection({
					options: [
						{
							title: $t("强耦合，发货单产品的产品范围必须基于订单产品选择，发货单产品金额必须与订单产品保持一致，发货单确认后系统自动更新订单的发货数量、金额等信息"),
							value: 1,
						},
						{
							title: $t("弱耦合，发货单产品可以从订单产品之外的范围内选择，发货单产品金额可以与订单产品不一致，发货单确认后系统不会更新订单的任何信息"),
							value: 2,
							// 弱耦合模式不适用于渠道业务类型的发货单
							desc: this.model.get('isDistributionStockSupportMultiOrderingEnable') ? $t('stock.stock_manage.DeliveryNoteObj.distribution.not_applicable') : ''
						}
					],
					value: delivery_note_interaction_model,
					name: CONSTANT.coupling,
				})
			);
			this._handleShowSettingDetail(e);
		},

		// 订单产品填充
		_handleFillProduct: function (e) {
			var stock_order_product_fill = this.model.get("stock_order_product_fill");
			$(".j-edit-fillProduct-detail").html(
				this.radioSelection({
					options: [
						{
							title: $t("新建发货单时，系统自动将可发货的订单产品，全部填充到发货单产品明细行"),
							value: 1,
						},
						{
							title: $t("新建发货单时，系统不自动填充任何产品到发货单产品明细行"),
							value: 2,
						}
					],
					value: stock_order_product_fill,
					name: CONSTANT.fillProduct,
				})
			);
			this._handleShowSettingDetail(e);
		},

		// 采购订单计算
		_handlePurchaseOrderCalculation: function (e) {
			var purchase_order_calculation = this.model.get("purchase_order_calculation");
			$(".j-edit-purchase-order-calculation").html(
				this.radioSelection({
					options: [
						{
							title: $t("用户手动计算：新建采购订单时，金额相关的字段，均由用户自行计算后输入"),
							value: 1,
						},
						{
							title: $t("系统自动计算：新建采购订单时，金额相关的字段，系统根据相关条件进行自动计算"),
							value: 2,
						}
					],
					// data-action 和 data-name 是在这里传递的
					value: purchase_order_calculation,
					name: CONSTANT.PURCHASE_ORDER_CALCULATION,
				})
			);
			this._handleShowSettingDetail(e);
		},


			_handlePurchaseOrderWhenInStock: function(e) {
				var enable_edit_purchase_order_when_in_stock = this.model.get('enable_edit_purchase_order_when_in_stock');
				$('.j-setting-enable_edit_purchase_order_when_in_stock_detail').html(
					this.radioSelection({
						options: [
							{
								title: $t('采购订单部分入库/全部入库后，不允许编辑采购订单产品的数量和种类'),
								value: 1
							},
							{
								title: $t('采购订单部分入库/全部入库后，允许编辑采购订单产品的数量和种类'),
								value: 2
							}
						],
						// data-action 和 data-name 是在这里传递的
						value: enable_edit_purchase_order_when_in_stock,
						name: CONSTANT.ENABLE_EDIT_PURCHASE_ORDER_WHEN_IN_STOCK
					})
				);
				this._handleShowSettingDetail(e);
			},

		// 盘点产品来源
		_handleStockCheckNoteProductSourceType: function (e) {
			var stock_check_note_product_source_type = this.model.get("stock_check_note_product_source_type");
			$(".j-edit-stock-check-note-product-source-type").html(
				this.radioSelection({
					options: [
						// 库存：只能够盘点仓库中有对应库存记录的产品，且对于开启了批次或序列号的产品需分开盘点
						{
							title: $t("StockCheckNoteObj.check_stock"),
							value: 1,
						},
						// 产品：任何产品均可以盘点，且可以同时盘点开启了批次或序列号的产品
						{
							title: $t("StockCheckNoteObj.check_product"),
							value: 2,
						}
					],
					// data-action 和 data-name 是在这里传递的
					value: stock_check_note_product_source_type,
					name: CONSTANT.STOCK_CHECK_NOTE_PRODUCT_SOURCE_TYPE,
				})
			);
			this._handleShowSettingDetail(e);
		},

		// 发货单校验/发货通知单
		_handleDeliveryStockRelatedCheck: function (e) {
			var delivery_stock_related_check = this.model.get("delivery_stock_related_check");
			$(".j-edit-delivery-stock-related-check").html(
				this.radioSelection({
					options: [
						// 新建/编辑发货单时，不校验仓库、库存、批次、序列号等库存相关逻辑
						{
							title: $t("stock.stock_manage.weak_stock"),
							value: 1,
						},
						// 新建/编辑发货单时，需校验仓库、库存、批次、序列号等库存相关逻辑
						{
							title: $t("stock.stock_manage.strong_stock"),
							value: 2,
						}
					],
					// data-action 和 data-name 是在这里传递的
					value: delivery_stock_related_check,
					name: CONSTANT.DELIVERY_STOCK_RELATED_CHECK,
				})
			);
			this._handleShowSettingDetail(e);
		},

		// 盘点后库存调整
		_handleStockCheckNoteUpdateStockType(e){
			var stockCheckNoteUpdateStockType = this.model.get("stockCheckNoteUpdateStockType");
			$(".j-setting-stock-check-note-update-stock-type-detail").html(
				this.radioSelection({
					options: [
						{
							title: $t("盘点单确认后，需要手动操作盘盈入库、盘亏出库来调整库存"),
							value: 1,
						},
						{
							title: $t("盘点单确认后，系统自动生成盘盈入库单、盘亏出库单来进行库存调整"),
							value: 2,
						}
					],
					value: stockCheckNoteUpdateStockType,
					name: CONSTANT.stockCheckNoteUpdateStockType,
				})
			);
			this._handleShowSettingDetail(e);
		},

		// 调拨确认入库支持配置
		_handleRequisitionNoteAutomaticInbound(e){
			var requisitionNoteAutomaticInbound = this.model.get("requisitionNoteAutomaticInbound");
			$(".j-setting-requisition-note-automatic-inbound-detail").html(
				this.radioSelection({
					options: [
						{
							title: $t("调拨单确认后，系统自动处理调拨确认入库"),
							value: 1,
						},
						{
							title: $t("调拨单确认后，需手动操作调拨确认入库"),
							value: 2,
						}
					],
					value: requisitionNoteAutomaticInbound,
					name: CONSTANT.requisitionNoteAutomaticInbound,
				})
			);
			this._handleShowSettingDetail(e);
		},
		// 调拨出库支持配置
		_handleRequisitionNoteAutomaticOutbound(e){
			var requisitionNoteAutomaticOutbound = this.model.get("requisitionNoteAutomaticOutbound");
			$(".j-setting-requisition-note-automatic-outbound-detail").html(
				this.radioSelection({
					options: [
						{
							title: $t("stock.RequisitionNoteObj.automatic-outbound-setting.radio-automatic"),
							value: 1,
						},
						{
							title: $t("stock.RequisitionNoteObj.automatic-outbound-setting.radio-mannul"),
							value: 2,
						}
					],
					value: requisitionNoteAutomaticOutbound,
					name: CONSTANT.requisitionNoteAutomaticOutbound,
				})
			);
			this._handleShowSettingDetail(e);
		},
		_handleReturnGoodsInvoiceOfSalesOrderType: function(e) {
			var return_goods_invoice_of_sales_order_type = this.model.get("return_goods_invoice_of_sales_order_type");
			console.log('return_goods_invoice_of_sales_order_type', return_goods_invoice_of_sales_order_type);
			$(".j-setting-return-goods-invoice-of-sales-order-type-detail").html(
				this.radioSelection({
					options: [
						{
							title: $t("允许多张销售订单一起退货"),
							value: 2,
						},
						{
							title: $t("每次仅可基于一张销售订单退货"),
							value: 1,
						}
					],
					value: return_goods_invoice_of_sales_order_type === "one" ? 1 : 2,
					name: CONSTANT.returnGoodsInvoiceOfSalesOrderType,
				})
			);
			this._handleShowSettingDetail(e);
		},

		// 订单编辑
		_handleEditConfirm: function(e) {
			var me = this;
			var data = this.tempData.isAllowModifySalesOrderProduct || this.model.get('isAllowModifySalesOrderProduct');
			this.model.set('isAllowModifySalesOrderProduct', data);
			this.model.setEditOrder({
				isAllowModifySalesOrderProduct: data
			}, function() {
				me.render();
			})
		},

		// 盘点单自动出入库
		_handleStockCheckNoteUpdateStockTypeConfirm(e) {
			var data = this.tempData.stockCheckNoteUpdateStockType || this.model.get('stockCheckNoteUpdateStockType');
			this.model.set('stockCheckNoteUpdateStockType', data);
			this.model.setStockCheckNoteUpdateStockType({
				stockCheckNoteUpdateStockType: data
			}, () => {
				this.render();
			});
		},

		// 调拨确认入库支持配置
		_handleRequisitionNoteAutomaticInboundConfirm(e) {
			var data = this.tempData.requisitionNoteAutomaticInbound || this.model.get('requisitionNoteAutomaticInbound');
			this.model.set('requisitionNoteAutomaticInbound', data);

			this.model.setStockConfigs('requisition_note_automatic_inbound', data, () => {
				this.render();
			});
		},

		// 调拨确认入库支持配置
		_handleRequisitionNoteAutomaticOutboundConfirm(e) {
			var data = this.tempData.requisitionNoteAutomaticOutbound || this.model.get('requisitionNoteAutomaticOutbound');
			this.model.set('requisitionNoteAutomaticOutbound', data);

			this.model.setStockConfigs('requisition_note_automatic_outbound', data, () => {
				this.render();
			});
		},

		/**
		 * 订货模式
		 */
		_handleSettingWarehouse: function(e) {
			if ($(e.currentTarget).hasClass("item-disabled")) {
				return false;
			}
			const orderMode = this.model.get("orderWarehouseTypeConfigRangeCode");
			const orderWarehouseType = this.model.get("orderWarehouseType");
			const orderRecordTypeRelateDeliveryMode = this.model.get('orderRecordTypeRelateDeliveryMode')
			var allowOrderWarehouseBlank = this.tempData.allowOrderWarehouseBlank || this.model.get("allowOrderWarehouseBlank");
			var checked = +allowOrderWarehouseBlank === 1 ? 'checked' : '';
			$(".j-warehouse-detail").html(
				this.radioSelection4Warehouse({
					unifiedSetting: {
						title: $t("统一设置"),
						childName: CONSTANT.orderWarehouseType,
						childValue: orderWarehouseType,
						child: [
							{
								title: $t('crm.无仓库订货'),
								value: 3,
								tipHtml: [
									{
										type: 'b',
										label: $t('crm.无仓库订货') + ':'
									},
									{
										label: $t('提交销售订单时，无需指定订货仓库，且不校验库存')
									}
								]
							},
							{
								title: $t('crm.单一仓库订货'),
								value: 1,
								tipHtml: [
									{
										type: 'b',
										label: $t('crm.单一仓库订货:')
									},
									{
										label: $t('提交销售订单时需指定订货仓库根据指定的订货仓库来校验库存')
									}
								]
							},
							{
								title: $t('crm.合并仓库订货'),
								value: 2,
								tipHtml: [
									{
										type: 'b',
										label: $t('crm.合并仓库订货:')
									},
									{
										label: $t('提交订单时无需手动选择仓库根据所有适用的仓库来校验库存')
									}
								]
							}
						]
					},
					orderRecordTypeRelateDeliveryMode,
					value: orderMode,
					name: CONSTANT.orderMode,
					parseTipHtml: parseTipHtml
				})
			);
			if (orderMode == 1) {
				$('.crm-s-shiporder .shiporder-base-info_float-setting-item--child').show();
				$('.crm-s-shiporder .shiporder-base-info_order-delivery-mode').hide();
			} else {
				$('.crm-s-shiporder .shiporder-base-info_float-setting-item--child').hide();
				$('.crm-s-shiporder .shiporder-base-info_order-delivery-mode').show();
			}
			this._handleShowSettingDetail(e);
			this._toggleBlankOrderOption();
		},

		// 订货模式
		_handleWarehouseConfirm: function (e, stopDefault) {
			const orderWarehouseType = this.tempData.orderWarehouseType || this.model.get('orderWarehouseType');
			const allowOrderWarehouseBlank = this.tempData.allowOrderWarehouseBlank || this.model.get('allowOrderWarehouseBlank');
			// 统一设置或业务类型设置
			const orderMode = this.tempData.orderMode || this.model.get('orderWarehouseTypeConfigRangeCode');
			this.model.set('orderWarehouseType', orderWarehouseType);
			this.model.set('allowOrderWarehouseBlank', allowOrderWarehouseBlank);
			this.model.set('orderWarehouseTypeConfigRangeCode', orderMode);
			var param = {
				orderWarehouseType: orderWarehouseType,
				allowOrderWarehouseBlank: orderWarehouseType === 2 ? 2 : allowOrderWarehouseBlank, // 如果选择合并仓库，不允许订货仓库为空
				orderWarehouseTypeConfigRangeCode: orderMode // 设置订货模式，企业级或订单级
			};
			return this.model.setOrderWarehouseType(param, e.target ? $(e.target) : $(e)).then(() => {
				if (stopDefault) {
					return;
				}
				this.render();
			});
		},

		/**
		 * 库存显示
		 */
		_handleSettingStock: function(e) {
			if ($(e.currentTarget).hasClass("item-disabled")) {
				return false;
			}
			var isNotShowZeroStockType = this.model.get(
				"isNotShowZeroStockType"
			);
			var isOnlyShowOnSaleStockType = this.model.get(
				"isOnlyShowOnSaleStockType"
			);
			var stockType = this.model.get('stockType');
			var options = +stockType === 3 ? [
				{
					title: $t("stock.stock_manage.inventory_display.dot_not_display_empty_stock"), // 不显示可用库存和实际库存为0的库存记录
					name: CONSTANT.isNotShowZeroStockType
				},
			] : [
				{
					title: $t("stock.stock_manage.inventory_display.dot_not_display_empty_stock"), // 不显示可用库存和实际库存为0的库存记录
					name: CONSTANT.isNotShowZeroStockType
				},
				{
					title: $t("stock.stock_manage.inventory_display.dot_not_display_discontinued_product"), // 不显示已下架产品的库存记录
					name: CONSTANT.isOnlyShowOnSaleStockType
				}
			];
			$(".j-stock-detail").html(
				this.checkboxSelection({
					options: options,
					isNotShowZeroStockType:
						Number(isNotShowZeroStockType) === 1,
					isOnlyShowOnSaleStockType:
						Number(isOnlyShowOnSaleStockType) === 1,
					name: CONSTANT.stock
				})
			);
			this._handleShowSettingDetail(e);
		},

		_handleStockConfirm: function(e) {
			// 如果tmepData里没有相应数据，则取model里的原始值
			var isNotShowZeroStockType =
				this.tempData.isNotShowZeroStockType ||
				this.model.get("isNotShowZeroStockType");
			var isOnlyShowOnSaleStockType =
				this.tempData.isOnlyShowOnSaleStockType ||
				this.model.get("isOnlyShowOnSaleStockType");
			this.model.set("isNotShowZeroStockType", isNotShowZeroStockType);
			this.model.set("isOnlyShowOnSaleStockType", isOnlyShowOnSaleStockType);
			var stockType = this.model.get('stockType');
			var me = this;
			if (+stockType === 3) {
				this.model.setErpStockConfig(function () {
					crmUtil.remind(1, $t("操作成功!"))
					me._handleHideSettingDetail(e);
					me.render();
				}, $(e.target));
			} else {
				this.model.setShowStockType({
						isNotShowZeroStockType: isNotShowZeroStockType,
						isOnlyShowOnSaleStockType: isOnlyShowOnSaleStockType
					}, $(e.target), function() {
						me.render();
					}
				);
			}

		},

		initWarningSetting	(recordRemindRolesMap, recordRemindTypes: any = {}){
			var isBatchSNOpen = this.model.get('batchSNSwitch') == 2;
			var stockWarningType = +this.model.get("stockWarningType");
			var _recordRemindRolesMap = recordRemindRolesMap || _.clone(this.model.get('recordRemindRolesMap'));
			if (Object.keys(recordRemindTypes).length) {
				var _lowStock = recordRemindTypes.lowStock;
				var _highStock = recordRemindTypes.highStock;
				var _recordRemindIsOpen = recordRemindTypes.recordRemindIsOpen;
			} else {
				var _lowStock: any = stockWarningType === 2 || stockWarningType === 4;
				var _highStock: any = stockWarningType === 3 || stockWarningType === 4;
				var _recordRemindIsOpen = this.model.get("recordRemindIsOpen");
			}
			this.tempData.lowStockWarning = _lowStock;
			this.tempData.highStockWarning = _highStock;
			this.tempData.recordRemindIsOpen = _recordRemindIsOpen;
			this.tempData.recordRemindRolesMap = _recordRemindRolesMap;
			let options  = [
				{
					title: $t('stock.stock_manage.warn.text_1'), // 开启低库存预警，当可用库存小于安全库存时，收到预警
					name: CONSTANT.lowStockWarning
				},
				{
					title: $t("stock.stock_manage.warn.text_2"), // 开启高库存预警，当可用库存大于最高库存时，收到预警
					name: CONSTANT.highStockWarning
				},
			]

			if(isBatchSNOpen){
				options.push({
					title: $t('stock.stock_manage.warn.title'), // 开启临到期库存预警
					name: CONSTANT.recordRemindIsOpen
				});
			}

			$(".j-warning-detail").html(
				this.checkboxSelection4Warning({
					isBatchSNOpen,
					options,
					recordRemindRolesMap: _recordRemindRolesMap,
					// roleName: Object.values(_recordRemindRolesMap),
					lowStockWarning: _lowStock,
					highStockWarning: _highStock,
					recordRemindIsOpen: _recordRemindIsOpen,
					name: CONSTANT.warning
				})
			);
		},

		handleWarningSettingListDel(e){
			e.stopPropagation();
			const clickedElement = $(event.target);
			const id = clickedElement.data('id');
			delete this.tempData.recordRemindRolesMap[id]

			const recordRemindTypes = {
				lowStock: this.tempData.lowStockWarning,
				highStock: this.tempData.highStockWarning,
				recordRemindIsOpen: this.tempData.recordRemindIsOpen,
			};

			this.initWarningSetting(this.tempData.recordRemindRolesMap, recordRemindTypes)
		},

		/**
		 * 库存预警
		 */
		_handleSettingWarning: function(e) {
			if ($(e.currentTarget).hasClass("item-disabled")) {
				return false;
			}
			this.initWarningSetting()
			this._handleShowSettingDetail(e);
		},

		handlePickRecordRemindRole(e){
			e.stopPropagation();
			const recordRemindRolesMap = this.tempData.recordRemindRolesMap;
			const role = Object.keys(recordRemindRolesMap)
			const backBone = this
			FxUI.create({
				wrapper: 'body',
				template:`
            <fx-selector-box-v2
              ref='sb'
              :show.sync="showSelectorBox"
              v-bind="opts"
              @confirm="confirm"
              @cancel="cancel"
              @selector-mounted="onSelectorMounted"
            >
            </fx-selector-box-v2>
          `,
				data: function () {
					return {
						showSelectorBox: false,
						opts: FS.selectorParseContactV2.parseContacts({
							role: true,
							defaultSelectedItems: {
								role
							},
						}),
					}
				},
				methods: {
					confirm:  function (val) {
						const {role} = val
						const sb = this.$refs.sb
						sb.getRoleList().then((allRole)=>{
							// 全roleMap
							const allRoleMap = allRole.reduce((acc,idNameObj)=>{
								const id = idNameObj.id
								const name = idNameObj.name
								acc[id] = name
								return acc
							}, {})
							// 挑选,选取的roleMap
							const recordRemindRolesMap = role.reduce(
								(acc, id) => {
									acc[id] = allRoleMap[id];
									return acc;
								}, {});
							console.log(recordRemindRolesMap)
							backBone.tempData.recordRemindRolesMap = recordRemindRolesMap

							const recordRemindTypes = {
								lowStock: backBone.tempData.lowStockWarning,
								highStock: backBone.tempData.highStockWarning,
								recordRemindIsOpen: backBone.tempData.recordRemindIsOpen,
							};

							backBone.initWarningSetting(recordRemindRolesMap, recordRemindTypes)
						})
					},
					cancel: function () {
						this._destroy();
					},
					_destroy: function () {
						if (selectorBox && selectorBox.destroy) {
							selectorBox.destroy();
							selectorBox = null;
						}
					},
					onSelectorMounted: function () {
						this.$nextTick( () =>{
							this.showSelectorBox = true;
						});
					},
				},
				mounted () {},
			});
		},


		_handleWarningConfirm: function(e) {
			var isBatchSNOpen = this.model.get('batchSNSwitch') == 2;
			var lowStockValue = this.tempData.lowStockWarning;
			var highStockValue = this.tempData.highStockWarning;
			var recordRemindIsOpen = this.tempData.recordRemindIsOpen;
			var recordRemindRolesMap = this.tempData.recordRemindRolesMap
			var stockWarningType;
			if (lowStockValue && highStockValue) {
				stockWarningType = 4
			} else if (highStockValue) {
				stockWarningType = 3;
			} else if (lowStockValue) {
				stockWarningType = 2;
			} else {
				stockWarningType = 1;
			}
			return saveRecordRemindByRoleSwitch({
				stockWarningType: stockWarningType + '',
				willExpireStockWarningType: recordRemindIsOpen ? '2' : '1',
				roles: Object.keys(recordRemindRolesMap)
			}, $(e.target)).then(() => {
				this.model.set('stockWarningType', stockWarningType);
				this.model.set('recordRemindIsOpen', recordRemindIsOpen);
				this.model.set('recordRemindRolesMap', recordRemindRolesMap);
				this.render();
				crmUtil.remind(1, $t("操作成功!"))
			}).catch(e => {
				console.error(e);
			});
		},

		/**
		 * 安全库存
		 */
		_handleSettingSafety: function(e) {
			if ($(e.currentTarget).hasClass("item-disabled")) {
				return false;
			}
			var safetyStockType = this.model.get("safetyStockType");
			$(".j-safety-detail").html(
				this.radioSelection({
					options: [
						{
							title: $t("统一设置安全库存与最高库存"),
							value: 1,
							tipHtml: [
								{
									label: $t("在产品对象中设置相应的字段值，对所有仓库均适用"),
								},
							],
						},
						{
							title: $t("分仓库设置安全库存与最高库存"),
							value: 2,
							tipHtml: [
								{
									label: $t("在库存对象中设置相应的字段值，仅对该产品库存适用"),
								},
							],
						}
					],
					value: safetyStockType,
					name: CONSTANT.safety,
					parseTipHtml: parseTipHtml
				})
			);
			this._handleShowSettingDetail(e);
		},

		// 盘点冻结仓库
		_handleStockCheckFreezeWarehouse: function(e) {
			if ($(e.currentTarget).hasClass("item-disabled")) {
				return false;
			}
			var stockCheckFreezeWarehouse = this.model.get("stockCheckFreezeWarehouse");
			$(".j-freeze-detail").html(
				this.radioSelection({
					options: [
						{
							title: $t("stock.stock_manage.stock_check_freeze_warehouse.not_create_inbound_outbound"), // 盘点时，冻结盘点仓库，不可再新建任何与出入库有关的业务单据
							value: 1,
						},
						{
							title: $t("stock.stock_manage.stock_check_not_freeze_warehouse.create_inbound_outbound"), // 盘点时，不冻结盘点仓库，仍可新建与出入库有关的业务单据
							value: 2,
						}
					],
					value: stockCheckFreezeWarehouse,
					name: CONSTANT.stockCheckFreezeWarehouse,
				})
			);
			this._handleShowSettingDetail(e);
		},

		_handleSafetyConfirm: function(e) {
			var safetyStockType = this.tempData.safetyStockType || this.model.get("safetyStockType");
			var me = this;
			this.model.set("safetyStockType", safetyStockType);
			this.model.setSafetyStockType(safetyStockType, $(e.target), function() {
				me.render();
			});
		},

		_handleFreezeConfirm: function(e) {
			var stockCheckFreezeWarehouse = this.tempData.stockCheckFreezeWarehouse || this.model.get("stockCheckFreezeWarehouse");
			var me = this;
			this.model.set("stockCheckFreezeWarehouse", stockCheckFreezeWarehouse);
			me.model.setStockConfigs('stock_check_freeze_warehouse', stockCheckFreezeWarehouse, () => {
				me.render();
			});
		},

		/**
		 * 扫码设置
		 */
		_handleSettingScan: function(e) {
			if ($(e.currentTarget).hasClass('item-disabled')) {
				return false;
			}
			var me = this;
			var $target = $(e.currentTarget.parentNode.parentNode.parentNode);
			$target.addClass("active");
			$target.find(".space-between").hide();
			$target.find(".shiporder-base-info_setting-detail").show();
			var batchSNSwitch = this.model.get('batchSNSwitch');
			var options = [];
			if (+batchSNSwitch === 2) {
				options = [
					{
						title: $t("扫描产品条码录入产品信息"),
						value: 1
					},
					{
						title: $t("扫描批次条码录入产品信息"),
						value: 2
					},
					{
						title: $t("扫描序列号条码录入产品信息"),
						value: 3
					}
				]
			} else {
				options = [
					{
						title: $t("扫描产品条码录入产品信息"),
						value: 1
					}
				]
			}
            $(".j-scan-detail").html(
				me.radioSelection({
					options: options,
					value: me.model.get("scanCodeType"),
					name: CONSTANT.scan
				})
			);
		},
		_handleScanConfirm: function(e) {
			var value = this.tempData.scanCodeType || this.model.get("scanCodeType");
			var me = this;
			this.model.set("scanCodeType", value);
			this.model.setScanInfo(value, function() {
				me.render();
			});
		},
		/**
		 * 可用库存显示
		 */
		_handleStockSwitch: function(e) {
			var $target = $(e.currentTarget);
			var confirm;
			var me = this;
			if ($target.hasClass("switch-disabled")) {
				return;
			}

			var tips = '';
			tips += '<p>' + $t('此功能开启后，将在销售订单的提交页面显示所选仓库（单一仓库订货模式）或所有适用仓库（合并仓库订货模式）的可用库存数量。') + '</p>'
			tips += '<p style="margin-top: 20px">' + $t('开启后，此功能不可关闭，是否确认开启此功能？') + '</p>';
			confirm = crmUtil.confirm(tips, $t("温馨提示"), function () {
				confirm.destroy();
				me.model.set('maskTips', $t('正在加载'));
				me.model.set('showMask', true);
				me.model.enableSalesOrder(function () {
					$target.addClass("switch-on switch-disabled");
					me.model.set('showMask', false);
				});
			});
		},

		// 盘点暗盘模式
		_handleStockGreyCheckSwitch: function(e) {
			var $target = $(e.currentTarget);
			var confirm;
			var me = this;
			if ($target.hasClass("switch-disabled")) {
				return;
			}

			var tips = '';
			tips += '<p>' + $t('开启后，此功能不可关闭，是否确认开启此功能？') + '</p>';
			confirm = crmUtil.confirm(tips, $t("温馨提示"), function () {
				confirm.destroy();
				me.model.set('maskTips', $t('正在加载'));
				const value = me.model.get('stockGreyCheckSwitch');

				me.model.set('showMask', true);
				me.model.setStockConfigs('stock_grey_check_switch', value === 1 ? 2 : 1, () => {
					$target.addClass("switch-on switch-disabled");
					me.model.set('showMask', false);
				});
			});
		},

		_handleStocScanSwitch(e){
			var $target = $(e.currentTarget);
			var me = this;
			if ($target.hasClass("switch-disabled")) {
				return;
			}
			const  pvalue =  this.model.get("scanCodeType");
			// 10 为关。 如果关了，就打开设置为1。如果开的，就设置为关10。
			const value = pvalue == 10 ? 1 : 10;
			this.model.set("scanCodeType", value);
			this.model.setScanInfo(value, function() {
				me.render();
			});
		},

		/**
		 * 自动收货
		 */
		_handleSettingDelivery: function(e) {
			this._handleShowSettingDetail(e);
		},
		_handleAutoReceive: function(e) {
			var me = this;
			var days = $(".j-auto-receive-days").attr("value");
			var status =
				this.tempData.autoReceiveStatus ||
				this.model.get("autoReceiveStatus");
			this.model.set("daysAfterShipment", parseInt(days));
			this.model.set("autoReceiveStatus", parseInt(status));
			this.model.setReceiving(
				{
					status: parseInt(status),
					daysAfterShipment: parseInt(days)
				},
				function() {
					var text =
						status === 1
							? "物流签收起" +
							  days +
							  "天后，自动对发货单进行收货确认"
							: "不启动自动收货";
					$(".j-auto-receive-text").text(text);
					me._handleHideSettingDetail(e);
				}
			);
		},
		_handleAutoReciveRadio: function(e) {
			var $target = $(e.currentTarget);
			var value = parseInt($target[0].value);
			this.tempData.autoReceiveStatus = value;
        },

        /**
         * 获取扫码设置信息
         */
        _getScanInfo: function () {
            var me = this;
            var title = {
                1: $t('扫描产品条码录入产品信息'),
				2: $t('扫描批次条码录入产品信息'),
				3: $t('扫描序列号条码录入产品信息')
            }
            this.model.getScanInfo(function (data) {
                me.model.set("scanCodeType", data);
                $(".j-scan-type").html(title[data]);
            })
		},

		/**
		 * 是否允许空订货仓库
		 */
		_handleBlankOrder: function (e) {
			e.stopPropagation();
			var allowOrderWarehouseBlank = this.model.get('allowOrderWarehouseBlank');
			if (+allowOrderWarehouseBlank === 1) {
				this.tempData.allowOrderWarehouseBlank = 2
			} else {
				this.tempData.allowOrderWarehouseBlank = 1
			}
		},
		_toggleBlankOrderOption: function() {
			var orderWarehouseType = +this.model.get('orderWarehouseType');
			if (orderWarehouseType === 2) {
				$('.j-allow-blank').hide();
			} else {
				$('.j-allow-blank').show();
			}
		},
		_handleOrderDeliveryModeEdit() {
			function isNewWeb() {
				return /XV\/UI/.test(window.location.href);
			}

			/*
      			兼容新旧主站，封装跳转, 通过 window.open 新开tab打开的，请直接调用此函数，如
        		var toUrl = FS.util.getFxHash('#crmmanage', {
            	mod: manage
        		});
        		window.open(toUrl, "_blank");
      		*/
			function getFxHash(hash, opts) {
				// 新主站标识
				if (isNewWeb()) {
					return `/XV/UI/${opts.mod}${hash}`;
				}
				return hash;
			}

			const hash = '#crmmanage/=/module-businessconfig';
			this._handleWarehouseConfirm($('.j-confirm[data-action="ordermode"]'), true).then(() => {
				if (isNewWeb()) {
					const url = getFxHash(hash, {mod: 'manage'});
					return window.open(url, '_blank');
				}
				FS.tpl.navRouter.navigate(hash, {
					trigger: true,
					mod: 'crmmanage'
				});
			});
		},
		destroy: function() {
			this.$el.off();
			this.$el.empty();
			this.$el = this.el = this.options = null;
			window.addEventListener('message', this.receiveMessage, false);
		}
	});

		export { BaseInfo };
