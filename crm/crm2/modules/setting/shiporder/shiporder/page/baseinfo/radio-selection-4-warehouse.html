<div class="shiporder-base-info_float-setting-item shiporder-base-info_float-setting-item--no-margin" style="display: block">
	<div class="shiporder-base-info_float-setting-item fx-radio">
		<label>
			<input class="shiporder-base-info_radio j-setting-option"
					type="radio"
					name="ordermode"
					data-value="1"
					{{Number(obj.value)=== 1 ? 'checked' : ''}}
			/>
			<div class="fx-radio-icon"></div>
			<div class="shiporder-base-info_float-setting-content">{{obj.unifiedSetting.title}}</div>
		</label>
	</div>
	<div class="shiporder-base-info_float-setting-item--child">
		## _.each(obj.unifiedSetting.child, function (child) { ##
		<div class="shiporder-base-info_float-setting-item fx-radio">
			<label>
				<input class="shiporder-base-info_radio j-setting-option"
						type="radio" name="{{obj.unifiedSetting.childName}}" data-value="{{child.value}}"
						{{Number(obj.unifiedSetting.childValue)=== Number(child.value) ? 'checked' : ''}} />
				<div class="fx-radio-icon"></div>
				<div class="shiporder-base-info_float-setting-content">
					{{child.title}}
				</div>
			</label>
			## if (!_.isEmpty(child.tipHtml)) { ##
			<div class="shiporder-tip">
				<i class="shiporder-tip_icon">?</i>
				<div class="shiporder-tip_intro">
					{{obj.parseTipHtml(child.tipHtml)}}
				</div>
			</div>
			## } ##
		</div>
		## }) ##
	</div>
</div>

<div class="shiporder-base-info_float-setting-item--no-margin">
	<div class="shiporder-base-info_float-setting-item fx-radio">
		<label>
			<input class="shiporder-base-info_radio j-setting-option"
					id="option-title"
					type="radio"
					name="ordermode"
					data-value="2"
					{{Number(obj.value)=== 2 ? 'checked' : ''}}
			/>
			<div class="fx-radio-icon"></div>
			<div class="shiporder-base-info_float-setting-content">
				{{$t("根据订单业务类型设置")}}
			</div>
		</label>
		<span class="j-order-delivery-mode_edit"> {{ $t("编辑") }}</span>
	</div>
	<div class="shiporder-base-info_order-delivery-mode">
		## _.each(obj.orderRecordTypeRelateDeliveryMode, function (val) { ##
		<div class="shiporder-base-info_order-delivery-mode_item">
			<span>{{ $t(val.label) }}: </span>
			<span>{{ $t(val.configLabel) }}</span>
		</div>
		## }) ##
	</div>
</div>

<!--提交-->
<div class="shiporder-base-info_float-setting-btns">
	<div class="setting-btn crm-btn-primary j-confirm" data-action="{{obj.name}}"> {{ $t("确定") }}</div>
	<div class="setting-btn btn-cancel j-cancel" data-action="{{obj.name}}">{{ $t("取消") }}</div>
</div>
