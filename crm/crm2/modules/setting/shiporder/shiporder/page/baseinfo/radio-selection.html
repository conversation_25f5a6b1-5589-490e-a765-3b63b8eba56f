## _.each(obj.options, function (item) { ##
    <div class="shiporder-base-info_float-setting-item fx-radio">
        <label for="radion">
			<input class="shiporder-base-info_radio j-setting-option" type="radio" name="{{obj.name}}"
				data-value="{{item.value}}" {{Number(obj.value) === Number(item.value) ? 'checked' : ''}} />
			<div class="fx-radio-icon"></div>
            <div class="shiporder-base-info_float-setting-content">
                {{item.title}}
            </div>
      </label>
      ## if (!_.isEmpty(item.tipHtml)) { ##
      <div class="shiporder-tip">
        <i class="shiporder-tip_icon">?</i>
        <div class="shiporder-tip_intro">
          {{obj.parseTipHtml(item.tipHtml)}}
        </div>
      </div>
      ## } ##
      ## if (!_.isEmpty(item.tipText)) { ##
      <span class='shiporder-tiptext'>{{item.tipText}}</span>
      <!--            <div class="shiporder-tip">-->
      <!--                <i class="shiporder-tip_icon">?</i>-->
      <!--                <div class="shiporder-tip_intro">-->
      <!--                    {{item.tipText}}-->
      <!--                </div>-->
      <!--            </div>-->
      ## } ##
      
		<!-- 选项末尾的链接 -->
		## if(item.link) { ##
		<span class="shiporder-base-info_float-setting-item__href" data-hash="{{'#crmmanage/=/module-businessconfig'}}" data-type="{{item.link.dataType}}">{{item.link.text}}</span>
		## } ##
		## if (item.option) { ##
            {{item.option}}
        ## } ##
    </div>

    <div class='shiporder-base-info_float-setting-item__desc'>{{item.desc}}</div>
## }) ##
<div class="shiporder-base-info_float-setting-btns">
    <div class="setting-btn crm-btn-primary j-confirm" data-action="{{obj.name}}">{{ $t("确定") }}</div>
    <div class="setting-btn btn-cancel j-cancel" data-action="{{obj.name}}">{{ $t("取消") }}</div>
</div>
