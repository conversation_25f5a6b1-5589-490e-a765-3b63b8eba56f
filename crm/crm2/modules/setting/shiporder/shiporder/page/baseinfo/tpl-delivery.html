<div class="shiporder-base-info_wrapper">
	<div class="shiporder-base-info_item">
		<div class="shiporder-base-info_item-title">{{ $t("版本状态") }}</div>
		<div class="shiporder-base-info_item-content">
			<p>{{ $t("仅开启发货单") }}</p>
		</div>
	</div>
	<div class="shiporder-base-info_item j-decimal">
		<div class="shiporder-base-info_item-title">{{ $t("精度设置") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			<p>{{ $t("设置发货单的精度") }}</p>
			<div class="shiporder-base-info_item-btn j-btn-setting btn-float">{{ $t("设置") }}</div>
		</div>
		<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail">
			<div class="shiporder-base-info_float-setting-item">
				<div class="shiporder-base-info_float-setting-title">{{ $t("发货单") }}：</div>
				<div class="shiporder-base-info_float-setting-content">
					<input class="j-delivery-decimal fx-form-control" type="number" value="{{obj.deliveryNoteDecimal}}"
						onkeyup="value=value.replace(/[^\d]+/g,'')">
					<span>{{ $t("位小数") }}</span>
					<span class="decimal-tips"> {{ $t("发货单精度应不可小于订单产品精度") }}</span>
				</div>
			</div>
			<div class="shiporder-base-info_float-setting-btns">
				<div class="setting-btn crm-btn-primary j-decimal-confirm">{{ $t("确定") }}</div>
				<div class="setting-btn btn-cancel j-cancel">{{ $t("取消") }}</div>
			</div>
		</div>
	</div>
	## if (obj.isForErp || obj.stockStatus === 1) { ##
		<div class="shiporder-base-info_item j-coupling-edit">
			<div class="shiporder-base-info_item-title">{{ $t("订单耦合度") }}</div>
			<div class="shiporder-base-info_item-content space-between">
				<p>{{Number(obj.delivery_note_interaction_model) === 1 ? $t("强耦合") : $t("弱耦合") }}</p>
				<div class="shiporder-base-info_item-btn btn-setting btn-check">{{ $t("设置") }}</div>
			</div>
			<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-edit-coupling-detail">

			</div>
		</div>
	## } ##
	## if (Number(obj.delivery_note_interaction_model) === 1) { ##
	<div class="shiporder-base-info_item j-fillProduct-edit">
		<div class="shiporder-base-info_item-title">{{ $t("订单产品填充") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			<p>{{Number(obj.stock_order_product_fill) === 1 ? $t("新建发货单时，系统自动将可发货的订单产品，全部填充到发货单产品明细行") : $t("新建发货单时，系统不自动填充任何产品到发货单产品明细行") }}</p>
			<div class="shiporder-base-info_item-btn btn-setting btn-check">{{ $t("设置") }}</div>
		</div>
		<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-edit-fillProduct-detail">

		</div>
	</div>
	## } ##
	## if ((!obj.isForErp && !(obj.stockStatus === 1))|| Number(obj.delivery_note_interaction_model) === 1) { ##
	<div class="shiporder-base-info_item j-edit">
		<div class="shiporder-base-info_item-title">{{ $t("订单编辑") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			<p>{{Number(obj.isAllowModifySalesOrderProduct) === 2 ? $t("订单部分发货/全部发货时，允许编辑订单产品的数量和种类") : $t("订单部分发货/全部发货时，不允许编辑订单产品的数量和种类") }}</p>
			<div class="shiporder-base-info_item-btn btn-setting btn-check">{{ $t("设置") }}</div>
		</div>
		<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-edit-detail">

		</div>
	</div>
	## } ##

	<div class="shiporder-base-info_item j-setting-return-goods-invoice-of-sales-order-type">
		<div class="shiporder-base-info_item-title">{{ $t("订单退货") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			## if (obj.return_goods_invoice_of_sales_order_type === "one") { ##
			<p>{{ $t("每次仅可基于一张销售订单退货") }}</p>
			## } else { ##
			<p>{{ $t("允许多张销售订单一起退货") }}</p>
			## } ##
			<div class="shiporder-base-info_item-btn btn-setting btn-check"> {{ $t("设置") }} </div>
		</div>
		<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-setting-return-goods-invoice-of-sales-order-type-detail"></div>
	</div>
</div>
