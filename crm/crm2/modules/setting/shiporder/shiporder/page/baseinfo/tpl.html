<!-- 库存管理-基础设置 -->
<div class="shiporder-base-info_wrapper">
	<div class="shiporder-base-info_item">
		<div class="shiporder-base-info_item-title">{{ $t("版本状态") }}</div>
		## if (obj.isForErp) { ##
		<div class="shiporder-base-info_item-content">
			<p>{{ $t("对接版") }}</p>
			<p class="shiporder-base-info_item-tips">
				{{ $t("纷享库存对接版，主要实现与ERP对接，出入库、盘点、调拨等业务均在ERP中处理，并将同步部分库存业务数据到纷享CRM。") }}</p>
		</div>
		## } else { ##
		## if (obj.stockStatus === 1) { ##
		<div class="shiporder-base-info_item-content">
			<p>{{ $t("仅开启发货单") }}</p>
		</div>
		## } else { ##
		<div class="shiporder-base-info_item-content">
			<p>{{ $t("完整版") }}</p>
			<p class="shiporder-base-info_item-tips">{{ $t("纷享库存完整版，包括出入库、盘点、调拨等业务，均在纷享CRM管理并形成业务闭环。") }}</p>
		</div>
		## } ##
		## } ##
	</div>
	<div class="shiporder-base-info_item j-decimal">
		<div class="shiporder-base-info_item-title">{{ $t("精度设置") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			<p>{{ $t("设置发货单及库存各个模块的精度") }}</p>
			<div class="shiporder-base-info_item-btn j-btn-setting btn-float">{{ $t("设置") }}</div>
		</div>
		<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail">
			## if (Number(obj.stockSwitch) !== 1) { ##
			<div class="shiporder-base-info_float-setting-item  shiporder-base-info_item-content-stock">
				<div class="shiporder-base-info_float-setting-title">{{ $t("库存") }}：</div>
				<div class="shiporder-base-info_float-setting-content">
					<input class="j-stock-decimal fx-form-control" type="number" value="{{obj.stockDecimal}}"
						{{Number(obj.stockSwitch) === 3 ? 'disabled' : ''}} onkeyup="value=value.replace(/[^\d]+/g,'')">
					<span>{{ $t("位小数") }}</span>
					<span class="decimal-tips"> {{ $t("库存精度不可小于订单产品精度，当前订单产品精度为") }} {{obj.salesOrderProductDecimal}}</span>
				</div>
			</div>
			## } ##
			<div class="shiporder-base-info_float-setting-item">
				<div class="shiporder-base-info_float-setting-title">{{ $t("发货单") }}：</div>
				<div class="shiporder-base-info_float-setting-content">
					<input class="j-delivery-decimal fx-form-control" type="number" value="{{obj.deliveryNoteDecimal}}"
						onkeyup="value=value.replace(/[^\d]+/g,'')">
					<span>{{ $t("位小数") }}</span>
					<span class="decimal-tips"> {{ $t("发货单精度应不可小于订单产品精度，不可大于库存精度") }}</span>
				</div>
			</div>
			## if (obj.isPurchaseOrderEnable) { ##
			<div class="shiporder-base-info_float-setting-item">
				<div class="shiporder-base-info_float-setting-title">{{ $t("采购") }}：</div>
				<div class="shiporder-base-info_float-setting-content">
					<input class="j-po-decimal fx-form-control" type="number" value="{{obj.purchaseOrderDecimal}}"
						{{Number(obj.stockSwitch) !== 2 ? 'disabled' : ''}} onkeyup="value=value.replace(/[^\d]+/g,'')">
					<span>{{ $t("位小数") }}</span>
					<span class="decimal-tips"> {{ $t("采购订单的精度不可大于库存精度") }}</span>
				</div>
			</div>
			## } ##
			<div class="shiporder-base-info_float-setting-btns">
				<div class="setting-btn crm-btn-primary j-decimal-confirm">{{ $t("确定") }}</div>
				<div class="setting-btn btn-cancel j-cancel">{{ $t("取消") }}</div>
			</div>
		</div>
	</div>

	## if (obj.isVersionB||obj.isVersionDeliveryOnly) { ##
		<div class="shiporder-base-info_item j-coupling-edit">
			<div class="shiporder-base-info_item-title">{{ $t("订单耦合度") }}</div>
			<div class="shiporder-base-info_item-content space-between">
				<p>{{Number(obj.delivery_note_interaction_model) === 1 ? $t("强耦合") : $t("弱耦合") }}</p>
				<div class="shiporder-base-info_item-btn btn-setting btn-check">{{ $t("设置") }}</div>
			</div>
			<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-edit-coupling-detail">
			</div>
		</div>
	## } ##
	## if ((!obj.isForErp && !(obj.stockStatus === 1))|| Number(obj.delivery_note_interaction_model) === 1) { ##
		<div class="shiporder-base-info_item j-edit">
			<div class="shiporder-base-info_item-title">{{ $t("订单编辑") }}</div>
			<div class="shiporder-base-info_item-content space-between">
				<p>{{Number(obj.isAllowModifySalesOrderProduct) === 2 ? $t("订单部分发货/全部发货时，允许编辑订单产品的数量和种类") : $t("订单部分发货/全部发货时，不允许编辑订单产品的数量和种类") }}</p>
				<div class="shiporder-base-info_item-btn btn-setting btn-check">{{ $t("设置") }}</div>
			</div>
			<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-edit-detail">

			</div>
		</div>

		## if (obj.isForErp) { ##
		<div class="shiporder-base-info_item">
			<div class="shiporder-base-info_item-title">{{ $t("订单超发") }}</div>
			<div class="shiporder-base-info_item-content space-between">
				<p>
					{{ $t("根据订单的业务类型，来设置是否允许超出订单产品的数量发货") }}
					<a href="#crmmanage/=/module-businessconfig" target="_blank">{{ $t("设置") }}</a>
				</p>
			</div>
		</div>
		## } ##
	## } ##


	## if (Number(obj.stockSwitch) !== 1) { ##
	<div class="shiporder-base-info_item {{Number(obj.stockSwitch) === 3 ? 'disabled-item' : 'j-validate'}}">
		<div class="shiporder-base-info_item-title">{{ $t("订单校验") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			## if(Number(obj.validateOrderType) === 1) { ##
			<p>{{ $t("可用库存数量不足时，不可提交订单") }}</p>
			## } else if(Number(obj.validateOrderType) === 2) { ##
			<p>{{ $t("可用库存数量不足时，仍可提交订单") }}</p>
			## } else { ##
			<p>{{ $t("根据订单的业务类型，设置不同的校验规则") }}</p>
			## } ##
			<div class="shiporder-base-info_item-btn btn-setting btn-check {{ Number(obj.stockSwitch) === 3 ? 'btn-disabled' : '' }}"> {{ $t("设置") }} </div>
		</div>
		<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-validate-detail"></div>
	</div>
	## } ##

	## if (obj.isVersionA || Number(obj.delivery_note_interaction_model) === 1) { ##
	<div class="shiporder-base-info_item j-fillProduct-edit">
		<div class="shiporder-base-info_item-title">{{ $t("订单产品填充") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			<p>{{Number(obj.stock_order_product_fill) === 1 ? $t("新建发货单时，系统自动将可发货的订单产品，全部填充到发货单产品明细行") : $t("新建发货单时，系统不自动填充任何产品到发货单产品明细行") }}</p>
			<div class="shiporder-base-info_item-btn btn-setting btn-check">{{ $t("设置") }}</div>
		</div>
		<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-edit-fillProduct-detail">
		</div>
	</div>
	## } ##

	## if (Number(obj.stockSwitch) !== 1) { ##
	<div class="shiporder-base-info_item {{Number(obj.stockSwitch) === 3 ? 'disabled-item' : 'j-warehouse'}}">
		<div class="shiporder-base-info_item-title">{{ $t("订货模式") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			## if (Number(obj.orderWarehouseTypeConfigRangeCode) === 1) { ##
			<p>
				<span>{{ $t("统一设置") }}: </span>
				## if (Number(obj.orderWarehouseType) === 1) { ##
				<span>{{ $t("单一仓库订货") }}</span>
				## } else if (Number(obj.orderWarehouseType) === 2) { ##
				<span>{{ $t("合并仓库订货") }}</span>
				## } else { ##
				<span>{{ $t("无仓库订货") }}</span>
				## } ##
			</p>
			## } else { ##
			<p>{{ $t("根据订单业务类型设置") }}</p>
			## } ##
			<div class="shiporder-base-info_item-btn btn-setting btn-warehouse {{Number(obj.stockSwitch) === 3 ? 'btn-disabled' : ''}}"> {{ $t("设置") }} </div>
		</div>
		<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-warehouse-detail"></div>
	</div>
	## } ##

	<div class="shiporder-base-info_item j-setting-return-goods-invoice-of-sales-order-type">
		<div class="shiporder-base-info_item-title">{{ $t("订单退货") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			## if (obj.return_goods_invoice_of_sales_order_type === "one") { ##
			<p>{{ $t("每次仅可基于一张销售订单退货") }}</p>
			## } else { ##
			<p>{{ $t("允许多张销售订单一起退货") }}</p>
			## } ##
			<div class="shiporder-base-info_item-btn btn-setting btn-check"> {{ $t("设置") }} </div>
		</div>
		<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-setting-return-goods-invoice-of-sales-order-type-detail"></div>
	</div>

	## if (Number(obj.stockSwitch) !== 1) { ##
	<div class="shiporder-base-info_item">
		<!-- 货位管理 -->
		<div class="shiporder-base-info_item-title">{{ $t("stock.stock_manage.location_manage.title") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			<!-- 开启后，将新增货位等对象，新建仓库时可选择是否启用货位管理，启用后出入库均需填写货位信息 -->
			<p>{{ $t("stock.stock_manage.location_manage.describe_text") }}</p>
			<div class="shiporder-base-info_item-switch j-setting-location_management {{obj.location_management == 1 ? 'switch-on switch-disabled' : ''}} ">
			</div>
		</div>
	</div>
	## } ##

	<!-- 【发货单校验】发货通知单 开关展示条件：B类库存 且 未开启弱耦合的开关 -->
	## if (obj.isVersionB && obj.delivery_note_interaction_model == 1) { ##
	<div class="shiporder-base-info_item j-setting-delivery-stock-related-check">
		<div class="shiporder-base-info_item-title">{{ $t("stock.stock_manage.deliverynote_verification") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			## if (obj.delivery_stock_related_check == 1) { ##
			<p>{{ $t("stock.stock_manage.weak_stock") }}</p>
			## } else { ##
			<p>{{ $t("stock.stock_manage.strong_stock") }}</p>
			## } ##
			<div class="shiporder-base-info_item-btn btn-setting btn-check"> {{ $t("设置") }} </div>
		</div>
		<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-edit-delivery-stock-related-check"></div>
	</div>
	## } ##

	## if (obj.isVersionA && obj.isPurchaseOrderEnable) { ##
	<div class="shiporder-base-info_item j-setting-enable_edit_purchase_order_when_in_stock">
		<div class="shiporder-base-info_item-title">{{ $t("采购订单编辑") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			## if (obj.enable_edit_purchase_order_when_in_stock === "1") { ##
			<p>{{$t('采购订单部分入库/全部入库后，不允许编辑采购订单产品的数量和种类')}}</p>
			## } else { ##
			<p>{{$t('采购订单部分入库/全部入库后，允许编辑采购订单产品的数量和种类')}}</p>
			## } ##
			<div class="shiporder-base-info_item-btn btn-setting btn-check"> {{ $t("设置") }} </div>
		</div>
		<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-setting-enable_edit_purchase_order_when_in_stock_detail"></div>
	</div>
	## } ##

	## if (obj.isVersionA) { ##
		<div class="shiporder-base-info_item j-purchase-order-calculation">
			<div class="shiporder-base-info_item-title">{{ $t("采购订单计算") }}</div>
			<div class="shiporder-base-info_item-content space-between">
				<p>{{Number(obj.purchase_order_calculation) === 1 ? $t("用户手动计算") : $t("系统自动计算") }}</p>
				<div class="shiporder-base-info_item-btn btn-setting btn-check">{{ $t("设置") }}</div>
			</div>
			<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-edit-purchase-order-calculation">
			</div>
		</div>
		## } ##


	## if (Number(obj.stockSwitch) !== 1 && obj.isForErp == false && obj.isShowStockGreyCheckSwitch) { ##
	<div class="shiporder-base-info_item">
		<div class="shiporder-base-info_item-title">{{ $t("盘点暗盘模式") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			<p>{{ $t("开启后，将新增暗盘业务类型的盘点单，盘点时不显示系统数量和盘盈盘亏数量") }}</p>
			<div class="shiporder-base-info_item-switch j-stock-grey-check-switch {{obj.stockGreyCheckSwitch == 1 ? 'switch-on switch-disabled' : ''}} "></div>
		</div>
	</div>
	## } ##

	## if (!obj.isForErp && Number(obj.stockSwitch) !== 1 || obj.isForErp && (obj.distribution_stock_switch == 2 || obj.spare_part_inventory_switch == 2)) { ##
	<div class="shiporder-base-info_item j-setting-stock-check-note-update-stock-type">
		<div class="shiporder-base-info_item-title">{{ $t("盘点后库存调整") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			<p>{{Number(obj.stockCheckNoteUpdateStockType) === 1 ? $t("盘点单确认后，需要手动操作盘盈入库、盘亏出库来调整库存") : $t("盘点单确认后，系统自动生成盘盈入库单、盘亏出库单来进行库存调整") }}</p>
			<div class="shiporder-base-info_item-btn btn-setting btn-check">{{ $t("设置") }}</div>
		</div>
		<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-setting-stock-check-note-update-stock-type-detail"></div>
	</div>
	## } ##

	<!-- 开关展示条件：A类库存，或者B类库存且开了渠道库存/备件库存的租户 -->
	## if (!obj.isForErp || obj.isForErp && (obj.distribution_stock_switch == 2 || obj.spare_part_inventory_switch == 2)) { ##
	<div class="shiporder-base-info_item j-stock-check-note-product-source-type">
		<!--  开关文案：盘点产品来源 -->
		<div class="shiporder-base-info_item-title">{{ $t("stock.inventory_of_product_source") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			<p>{{Number(obj.stock_check_note_product_source_type) === 1 ? $t("库存") : $t("产品") }}</p>
			<div class="shiporder-base-info_item-btn btn-setting btn-check">{{ $t("设置") }}</div>
		</div>
		<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-edit-stock-check-note-product-source-type">
		</div>
	</div>
	## } ##

	<!-- 开关展示条件：A类库存，或者B类库存且开了渠道库存/备件库存的租户 -->
	## if (!obj.isForErp || obj.isForErp && (obj.distribution_stock_switch == 2 || obj.spare_part_inventory_switch == 2)) { ##
	<div class="shiporder-base-info_item j-setting-requisition-note-automatic-outbound">
		<!-- 调拨自动出库 -->
		<div class="shiporder-base-info_item-title">{{ $t("stock.RequisitionNoteObj.automatic-outbound-setting.title") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			<!-- 调拨单确认后，系统自动处理调拨出库/调拨单确认后，需手动操作调拨出库 -->
			<p>{{Number(obj.requisitionNoteAutomaticOutbound) === 1 ? $t("stock.RequisitionNoteObj.automatic-outbound-setting.radio-automatic") : $t("stock.RequisitionNoteObj.automatic-outbound-setting.radio-mannul") }}</p>
			<div class="shiporder-base-info_item-btn btn-setting btn-check">{{ $t("设置") }}</div>
		</div>
		<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-setting-requisition-note-automatic-outbound-detail">
		</div>
	</div>
	## } ##

	<!-- 开关展示条件：A类库存，或者B类库存且开了渠道库存/备件库存的租户 -->
	## if (!obj.isForErp || obj.isForErp && (obj.distribution_stock_switch == 2 || obj.spare_part_inventory_switch == 2)) { ##
	<div class="shiporder-base-info_item j-setting-requisition-note-automatic-inbound">
		<div class="shiporder-base-info_item-title">{{ $t("调拨确认入库") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			<p>{{Number(obj.requisitionNoteAutomaticInbound) === 1 ? $t("调拨单确认后，系统自动处理调拨确认入库") : $t("调拨单确认后，需手动操作调拨确认入库") }}</p>
			<div class="shiporder-base-info_item-btn btn-setting btn-check">{{ $t("设置") }}</div>
		</div>
		<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-setting-requisition-note-automatic-inbound-detail">
		</div>
	</div>
	## } ##

	<!-- 盘点冻结仓库 -->
	<!-- ## if (Number(obj.stockSwitch) !== 1 && obj.isForErp == false) { ##
        <div class="shiporder-base-info_item {{Number(obj.stockSwitch) === 3 ? 'disabled-item' : 'j-freeze'}}">
                <div class="shiporder-base-info_item-title">{{ $t("stock.stock_manage.stock_check_freeze_warehouse") }}</div>
                <div class="shiporder-base-info_item-content space-between">
                        <p>{{Number(obj.stockCheckFreezeWarehouse) === 1 ? $t("盘点时，冻结盘点仓库，不可再新建任何与出入库有关的业务单据"):$t("盘点时，不冻结盘点仓库，仍可新建与出入库有关的业务单据") }}</p>
                        <div class="shiporder-base-info_item-btn btn-setting btn-stock {{Number(obj.stockSwitch) === 3 ? 'btn-disabled' : ''}}">设置</div>
                </div>
                <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-freeze-detail">

                </div>
        </div>
        ## } ## -->


	<!--
		【多语显示】
		stock.stock_manage.inventory_display.dot_not_display_empty_and_discontinued 不显示可用库存和实际库存为0的库存记录，不显示已下架产品的库存记录
		stock.stock_manage.inventory_display.dot_not_display_empty_stock 不显示可用库存和实际库存为0的库存记录
		stock.stock_manage.inventory_display.dot_not_display_discontinued_product 不显示已下架产品的库存记录
	-->
	## if (Number(obj.stockSwitch) !== 1) { ##
	<div class="shiporder-base-info_item {{Number(obj.stockSwitch) === 3 ? 'disabled-item' : 'j-stock'}}">
		<div class="shiporder-base-info_item-title">{{ $t("库存显示") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			<p>
				## if (obj.isNotShowZeroStockType == 1 && obj.isOnlyShowOnSaleStockType == 1) { ##
				{{ $t("stock.stock_manage.inventory_display.dot_not_display_empty_and_discontinued") }}
				## } else if (obj.isNotShowZeroStockType == 1) { ##
				{{ $t("stock.stock_manage.inventory_display.dot_not_display_empty_stock") }}
				## } else if (obj.isOnlyShowOnSaleStockType == 1) { ##
				{{ $t("stock.stock_manage.inventory_display.dot_not_display_discontinued_product") }}
				## } else { ##
				{{ $t("未设置") }}
				## } ##
			</p>
			<div class="shiporder-base-info_item-btn btn-setting btn-stock {{Number(obj.stockSwitch) === 3 ? 'btn-disabled' : ''}}">
				{{ $t("设置") }}</div>
		</div>
		<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-stock-detail">

		</div>
	</div>
	## } ##

	## if (Number(obj.stockSwitch) !== 1) { ##
	<div class="shiporder-base-info_item {{Number(obj.stockSwitch) === 3 ? 'disabled-item' : 'j-safety'}}">
		<div class="shiporder-base-info_item-title">{{ $t("库存结余设置") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			<p>{{Number(obj.safetyStockType) === 1 ? $t("统一设置安全库存与最高库存") : $t("分仓库设置安全库存与最高库存") }}</p>
			<div class="shiporder-base-info_item-btn btn-setting {{Number(obj.stockSwitch) === 3 ? 'btn-disabled' : ''}}">
				{{ $t("设置") }}</div>
		</div>
		<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-safety-detail">
		</div>
	</div>
	## } ##

	## if (Number(obj.stockSwitch) !== 1) { ##
	<div class="shiporder-base-info_item {{Number(obj.stockSwitch) === 3 ? 'disabled-item' : 'j-warning'}}">
		<div class="shiporder-base-info_item-title">{{ $t("库存预警") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			<p>{{obj.warningTypeDesc}}</p>
			<div class="shiporder-base-info_item-btn btn-setting {{Number(obj.stockSwitch) === 3 ? 'btn-disabled' : ''}}">
				{{ $t("设置") }}</div>
		</div>
		<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-warning-detail">
		</div>
	</div>
	## } ##

	## if (Number(obj.stockSwitch) !== 1) { ##
	<div class="shiporder-base-info_item">
		<div class="shiporder-base-info_item-title">{{ $t("可用库存显示") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			<p>{{ $t("启用后，在订单新建/编辑页面，显示订货仓库的可用库存数量") }}</p>
			<div class="shiporder-base-info_item-switch j-stock-switch {{obj.isSalesOrderShowStock == 1 ? 'switch-on switch-disabled' : ''}} ">
			</div>
		</div>
	</div>
	## } ##

	## if (Number(obj.stockSwitch) !== 1) { ##
	<div class="shiporder-base-info_item">
		<div class="shiporder-base-info_item-title">{{ $t("扫码设置") }}</div>
		<div class="shiporder-base-info_item-content space-between">
			<div>
				<p>{{ $t("开启扫码录入，快速提升单据处理效率") }}</p>
			</div>
			<div class="shiporder-base-info_item-switch j-stock-scan-switch {{obj.scanCodeType != 10 ? 'switch-on' : ''}} "></div>
		</div>
		<div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-scan-detail">
		</div>
	</div>
	## } ##
</div>
