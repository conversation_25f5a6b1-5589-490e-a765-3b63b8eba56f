<div class="shiporder-base-info_wrapper">
    <div class="shiporder-base-info_item">
        <div class="shiporder-base-info_item-title">{{ $t("版本状态") }}</div>
        <div class="shiporder-base-info_item-content">
            <p>{{ $t("对接版") }}</p>
            <p class="shiporder-base-info_item-tips">{{ $t("基础对接版，仅包含ERP仓库和ERP库存两个对象，可升级至高级对接版，获得更丰富的功能及更好的体验。") }}</p>
        </div>
    </div>
    <div class="shiporder-base-info_item j-decimal">
        <div class="shiporder-base-info_item-title">{{ $t("精度设置") }}</div>
        <div class="shiporder-base-info_item-content space-between">
<!--            <p>{{ $t("设置库存精度") }}</p>-->
            <p>{{ $t("stock.stock_manage.info.text4") }}</p>
            <div class="shiporder-base-info_item-btn j-btn-setting btn-float">{{ $t("设置") }}</div>
        </div>
        <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail">
            <div class="shiporder-base-info_float-setting-item">
                <div class="shiporder-base-info_float-setting-title">{{ $t("库存") }}：</div>
                <div class="shiporder-base-info_float-setting-content">
                    <input class="j-stock-decimal fx-form-control" type="number" value="{{obj.stockDecimal}}"
                        onkeyup="value=value.replace(/[^\d]+/g,'')">
                    <span>{{ $t("位小数") }}</span>
                    <span class="decimal-tips">{{ $t("库存精度不可小于订单产品精度") }}</span>
                </div>
            </div>
            <div class="shiporder-base-info_float-setting-btns">
                <div class="setting-btn crm-btn-primary j-decimal-confirm">{{ $t("确定") }}</div>
                <div class="setting-btn btn-cancel j-cancel">{{ $t("取消") }}</div>
            </div>
        </div>
    </div>
    <div class="shiporder-base-info_item j-validate">
        <div class="shiporder-base-info_item-title">{{ $t("订单校验") }}</div>
        <div class="shiporder-base-info_item-content space-between">
            <p>{{Number(obj.validateOrderType) === 1 ? $t("可用库存数量不足时，不可提交订单") : $t("可用库存数量不足时，仍可提交订单") }}</p>
            <div class="shiporder-base-info_item-btn btn-setting btn-check">{{ $t("设置") }}</div>
        </div>
        <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-validate-detail">

        </div>
    </div>
    <div class="shiporder-base-info_item j-stock">
        <div class="shiporder-base-info_item-title">{{ $t("库存显示") }}</div>
        <div class="shiporder-base-info_item-content space-between">
            <p>
                ## if (obj.isNotShowZeroStockType == 1) { ##
                <span>{{ $t("不显示可用库存和实际库存均为0的库存记录") }}</span>
                ## } else { ##
<!--                <span>{{ $t("显示可用库存和实际库存均为0的库存记录") }}</span>-->
                <span>{{ $t("stock.stock_manage.info.text5") }}</span>
                ## } ##
            </p>
            <div class="shiporder-base-info_item-btn btn-setting btn-stock">{{ $t("设置") }}</div>
        </div>
        <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-stock-detail">

        </div>
    </div>
</div>
