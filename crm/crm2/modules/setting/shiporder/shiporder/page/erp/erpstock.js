/**
 * @description ERP库存
 */
define(function (require, exports, module) {
	var Layout = require('../../layout/layout');
    var crmUtil = FS.crmUtil;

    return Layout.extend({
        options: {
            topInfo: {
	            messages: [
	                $t("已成功对接第三方 ERP将新增ERP仓库和ERP库存两个对象。"),
	                $t("请在第三方中维护库存相关数据纷享侧不可维护库存相关数据。"),
	                $t("可设置订单校验规则以及库存数据的展示规则。"),
	                $t("必须将客户端版本升级至6.3.4或以上，才能在移动端使用。"),
	            ],
	        },
        	layout: {
        		buttons: [{
	                label: $t("保存"),
	                action: 'save_erpstock_config',
	                show: true,
	            }],
	            switchInfo: {
	                name: 'erpStock',
	                label: $t("ERP库存状态"),
                    type: 'off',
                    desc: '',
	                // canSwitch: false,
	            },
	            config: {
	            	setting: true,
	            },
	            fields: [
                    {
    		            name: 'erpValidateOrderType',
    		            type: 'radio',
                        label: $t("订单校验"),
    		            options: [
    		                {
    		                    name: $t("可用库存数量不足时不可提交订单"),
    		                    value: 1,
    		                },
    		                {
    		                    name: $t("可用库存数量不足时仍可提交订单"),
    		                    value: 2,
    		                },
    		            ]
    		        },
                    {
                        name: 'erpStockShow',
                        type: 'checkboxs',
                        label: $t("库存显示"),
                        options: [
                            {
                                name: 'erpIsNotShowZeroStockType',
                                label: $t("不显示可用库存和实际库存均为0的库存记录"),
                            },
                        ]
                    },
                    {
                        name: 'erpStockDecimal',
                        type: 'singleSelect',
                        label: $t("精度设置"),
                        range: [0, 10],
                        tip: '',
                    }
                ],
        	},
        },

        render: function () {
        	this.super.render.call(this);
            this.$setting = this.$('.j-settings');
        	this._loadInfos();
        },
        getDescribe: function () {
        	var describe = this.super.getDescribe.call(this);
        	describe.loading = true;
        	return describe;
        },
        _loadInfos: function () {
        	var me = this;
        	this.model.fetchErpInfo(function (data) {
        		if (data.errCode !== 0) {
        			me.showErrMsg();
        			return;
        		}
                if (!data.erpStock) {
                    me.changeSwitchType('off');
                    me.$setting.hide();
                } else {
                    me.changeSwitchType('on');
                    me.$setting.show();
					// me.forms['erpStockDecimal'].setTip($t("当前订单产品的小数位数为") + data.salesOrderProductDecimal);
                }
                me.showContent();
        	});
        },
        changeSwitchType: function (type) {
            var switchInfo = this.widgets.switchInfo;
            if (!switchInfo) return;

            switchInfo.setType(type);
        },
        // 开启关闭ERP库存
        switchHandle: function (action) {
            if (['off', 'on'].indexOf(action) === -1) return;

            var me = this;
            var confirm, confirmText;
            var erpStock = me.model.get('erpStock');
            if (erpStock) {
                confirmText = $t("关闭后ERP仓库和ERP库存两个对象将不可用提交撤销订单时将不再根据设置的规则校验库存。")
                    + '<br><br>' + $t("您确认要关闭ERP库存吗");
                confirm = crmUtil.confirm(confirmText, $t("关闭ERP库存"), function () {
                    me.model.closeErpStock(function (status) {
                        confirm.hide();
                        if (status == 0) {
                            me.showTip($t("ERP库存已关闭"), 1);
                            me.render();
                            return;
                        }
                        me.showTip($t("操作失败请稍后重试或联系纷享客服"));
                    }, confirm.$('.b-g-btn'));
                });
            } else {
                confirmText = $t("您确认要开启ERP库存吗")
                confirm = crmUtil.confirm(confirmText, $t("开启ERP库存"), function () {
                    me.model.enableErpStock(function (status) {
                        confirm.hide();
                        if (status == 2) {
                            me.showTip($t("ERP库存已开启"), 1);
                            me.render();
                            return;
                        }
                        me.showTip($t("开启失败请稍后重试或联系纷享客服"));
                    }, confirm.$('.b-g-btn'));
                });
            }
        },
        doActionHandle: function (e) {
            var $target = $(e.target);
            var action = $target.attr('data-action');
            if (action !== 'save_erpstock_config') return;

            // 保存ERP库存设置
            var me = this;
			// if (this.validateErpStockConfig()) return;
            this.model.setErpStockConfig(function () {
                me.showTip($t("保存成功"), 1);
            }, $(e.target));
        },
		// validateErpStockConfig: function () {
		//     var flag = false;
		//     var data = this.model.toJSON();
		//     // ERP库存校验
		//     if (data.erpStockDecimal < data.salesOrderProductDecimal) {
		//         flag = true;
		//         this.showTip($t("库存精度不可小于订单产品精度请重新设置"));
		//     }
		//     return flag;
		// },
    });
});
