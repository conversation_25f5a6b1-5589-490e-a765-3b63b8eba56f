define(function (require, exports, module) {
	var Layout = require('../../layout/layout');

	return Layout.extend({
		options: {
			topInfo: {
				messages: [
					$t("如果您的企业已购买了ERP，且-db2602ac"),
					$t("默认包括发货单、仓库、库存等对象"),
					$t("支持商品开启批次和序列号管理，-886743f7"),
					$t("仓库和库存数据的维护在ERP处-3766d70b"),
				],
			},
			layout: {
				config: {
					setting: true,
				},
				fields: [{
					name: 'tip',
					type: 'tip',
					tip: $t('请联系纷享客服或者对应的纷享实施工程师开通'),
				}],
			},
		},

		mycomponents: {
			tip: require('./tip/tip'),
		},
	});
});
