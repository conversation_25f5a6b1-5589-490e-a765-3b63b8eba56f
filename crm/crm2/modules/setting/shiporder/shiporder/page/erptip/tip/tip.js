/**
 * @description ERP开启提示
 */
define(function (require, exports, module) {
	module.exports = Backbone.View.extend({
		template: require('./tpl-html'),

		initialize: function () {
			this.fieldAttr = this.getAttr(this.options.name);
		},

		render: function () {
			this.$el.html(this.template({
				tip: this.fieldAttr.tip,
			}));
		},

		getAttr: function(name) {
            return this.model.get('fields')[name] || {};
        },

		/**
		 *@desc 销毁方法 销毁一些公用的东西 子组件可覆盖进行更细致的销毁
		 */
		destroy: function () {
			this.$el.off();
			this.model = null;
			this.$el.empty();
			this.$el = this.el = this.options = null;
		}
	});
});
