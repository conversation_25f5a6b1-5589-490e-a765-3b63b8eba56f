/**
 * @description ERP库存
 */
define(function (require, exports, module) {
	var Layout = require('../../layout/layout');
    var crmUtil = FS.crmUtil;

    return Layout.extend({
        options: {
            topInfo: {
	            messages: [
	                $t("启用批次和序列号管理功能之前，必须先开启库存。"),
	                $t("启用批次和序列号管理功能后，不可停用，请谨慎开启。"),
	            ],
	        },
        	layout: {
        		buttons: [{
	                label: $t("保存"),
	                action: 'save_batch_config',
	                show: true,
	            }],
	            switchInfo: {
	                name: 'batch',
	                label: $t("批次和序列号管理状态："),
                    type: 'unopen',
                    desc: $t("已启用"),
	            },
	            fields: [
                    {
    		            name: 'willExpireStockWarningType',
    		            type: 'radio',
                        label: $t("临到期库存预警"),
    		            options: [
    		                {
    		                    name: $t("不开启预警"),
    		                    value: 1,
    		                },
    		                {
    		                    name: $t("开启预警"),
    		                    value: 2,
    		                },
    		            ]
    		        },
                ],
        	},
        },

        render: function () {
        	this.super.render.call(this);
            this.$setting = this.$('.j-settings');
        	this._loadInfos();
        },
        getDescribe: function () {
        	var describe = this.super.getDescribe.call(this);
        	describe.loading = true;
        	return describe;
        },
        _loadInfos: function () {
        	var me = this;
        	this.model.fetchBatch(function (data) {
        		if (data.errCode !== 0) {
        			me.showErrMsg();
        			return;
        		}

                me.changeSwitchType(data);
                if (data.batchSNSwitch === 2) { // 开启批次和序列号管理
                    me.$setting.show();
                } else {
                    me.$setting.hide();
                }
                me.showContent();
        	});
        },
        changeSwitchType: function (data) {
            var switchInfo = this.widgets.switchInfo;
            if (!switchInfo) return;

            var type = 'unopen';
            switch(data.batchSNSwitch) {
                case 2:
                    type = 'info';
                    break;
                default:
                    break;
            }

            switchInfo.setType(type);
        },
        // 开启关闭ERP库存
        switchHandle: function (action) {
            if (action !== 'unopen') return;

            var me = this;
            this.model.fetchStock(function (data) {
                if (data.errCode !== 0) {
                    me.showTip($t("查询库存是否开启失败，请稍后重试或联系纷享客服"));
                    return;
                }
                if (data.stockStatus !== 2) {
                    me.showTip($t("启用批次和序列号管理之前必须先启用库存，请确认库存成功开启后，再来尝试。"));
                    return;
                }
                var confirmText = $t("启用批次和序列号管理功能后，不可停用此功能，是否确定开启？");
                var confirm = crmUtil.confirm(confirmText, $t("温馨提示"), function () {
                    me.model.enableBatch({
                        submitSelector: confirm.$('.b-g-btn'),
                    }).done(function (res) {
                        confirm.hide();
                        if (res.Result.StatusCode == 0 && res.Value.isSuccess) {
                            crmUtil.remind(1, $t("批次和序列号管理开启成功"));
                            me.render();
                            CRM.control.refreshAside();
                            return;
                        }
                        me.showTip(res.Result.FailureMessage || $t("启用失败请稍后重试或联系纷享客服"));
                    });
                });
            });
        },
        doActionHandle: function (e) {
            var $target = $(e.target);
            var action = $target.attr('data-action');
            if (action !== 'save_batch_config') return;

            // 保存批次设置
            var me = this;
            this.model.setBatchConfig(function () {
                me.showTip($t("保存成功"), 1);
            }, $(e.target));
        },
    });
});
