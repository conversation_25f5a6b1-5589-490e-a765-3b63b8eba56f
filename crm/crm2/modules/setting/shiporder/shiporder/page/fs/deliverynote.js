/**
 * @description ERP库存
 */
define(function (require, exports, module) {
	var Layout = require('../../layout/layout');
    var crmUtil = FS.crmUtil;

    return Layout.extend({
        options: {
            topInfo: {
	            messages: [
	                $t("订单审核后可新建发货单可维护发货产品和数量支持分批发货。"),
                    $t("单独开启发货单未开启库存的情况下创建发货单不会校验及扣减库存。"),
                    $t("同时开启了发货单和库存的情况下创建发货单时会校验发货仓库的实际库存发货单确认后会自动创建出库单并扣减发货仓库的实际库存。"),
                    $t("发货单一旦开启不可关闭。"),
                    $t("为保证发货单功能的正常使用，请将移动客户端的版本升级至6.3及以上。"),
	            ],
                hasService: true,
	        },
        	layout: {
        		buttons: [{
	                label: $t("保存"),
	                action: 'save_deliverynote_config',
	                show: true,
	            }],
	            switchInfo: {
	                name: 'erpStock',
	                label: $t("发货单状态"),
                    type: 'unopen',
                    btnLabel: $t("开启"),
                    desc: $t("已启用"),
                    tipHtml: '',
	            },
	            fields: [
                    {
    		            name: 'expressQueryFuncId',
    		            type: 'radio',
						label: $t("stock.stock_manage.logistics_inquiry"), // 物流查询
						isHidden: true,
    		            options: [
    		                {
    		                    name: $t("stock.stock_manage.use_standard_query_features"), // 使用标准查询功能
    		                    value: 1,
                                tipHtml: [
                                    {
                                        type: 'b',
                                        label: $t("stock.stock_manage.standard_query_features"), // 标准查询功能：
                                    },
                                    {
                                        label: $t('stock.stock_manage.logistics_only_supports'), // 暂只支持申通、中通、圆通，如遇到超出查询次数限制的情况，请第二天再重试。
                                    },
                                ],
    		                },
    		                {
    		                    name: $t("stock.stock_manage.use_advanced_query_features"), // 使用高级查询功能
    		                    value: 2,
                                tipHtml: [
                                    {
                                        type: 'b',
                                        label: $t("stock.stock_manage.advanced_query_features"), // 高级查询功能：
                                    },
                                    {
										label: $t("stock.stock_manage.advanced_query_function_is_charge"), // 高级查询功能为收费功能，可免费体验至 2021-03-31
                                    },
                                ],
    		                },
    		            ]
    		        },
                    {
                        name: 'isAllowModifySalesOrderProduct',
                        type: 'radio',
                        label: $t("订单部分发货/全部发货后"),
                        options: [
                            {
                                name: $t("不允许编辑订单产品的种类和数量"),
                                value: 1,
                            },
                            {
                                name: $t("允许编辑订单产品的种类和数量"),
                                value: 2,
                            },
                        ]
                    },
                ],
        	},
        },

        render: function () {
        	this.super.render.call(this);
			this.$setting = this.$('.j-settings');
        	this._loadInfos();
        },
        getDescribe: function () {
			var describe = this.super.getDescribe.call(this);
        	describe.loading = true;
        	return describe;
		},
		// 设置表单设置相关字段
        setFields: function (list) {
			// 业务调整 B 类用户 又允许修改订单设置了
			/*
			var item = _.findWhere(list, {name: 'isAllowModifySalesOrderProduct'}) || {};
			if (this.model.get('isForErp')) {
				// B类用户不允许修改订单
				item.isHidden = true;
			}
			*/
            this.super.setFields.call(this, list);
        },
        _loadInfos: function () {
			var me = this;
        	this.model.fetchDeliveryNote(function (data) {
        		if (data.errCode !== 0) {
        			me.showErrMsg();
        			return;
        		}
                // 付费用户才能使用物流查询
                me.setField('expressQueryFuncId', {
					isHidden: !data.isPaid,
				});
				// todo: 付费用户才能设置高级物流
				me.forms['expressQueryFuncId'].setStatus();

                me.changeSwitchType(data);
				if (data.deliveryStatus === 2) { // 已开启发货单
					// 所有字段隐藏时，隐藏
					// if (!data.isPaid && me.model.get('isForErp')) {
					// 	me.$setting.hide();
					// } else {
						me.$setting.show();
					// }
                } else {
                    me.$setting.hide();
                }
                me.showContent();
        	});
        },
        changeSwitchType: function (data) {
            var switchInfo = this.widgets.switchInfo;
            if (!switchInfo) return;

            data = data || this.model.toJSON();
            var type = 'unopen';
            var tipHtml;
            var desc;
            switch(data.deliveryStatus) {
                case 1:
                    tipHtml = '<p class="shiporder-action_intro shiporder-action_intro__fail">' + _.escape(data.deliveryErrMsg) + '</p>';
                    break;
                case 2:
                    type = 'info';
                    tipHtml = '<p class="shiporder-action_intro">' + $t("发货单已开启建议先去后台设置") +
                        $t("crm.发货单") +
                        $t("对象的字段布局等信息") + '</p>';
                    desc = $t("已启用");
                    break;
                case 3:
                    type = 'info';
                    desc = $t("系统正在处理请耐心等待");
                    break;
                default:
                    break;
            }

            if (tipHtml) {
                switchInfo.setTip(tipHtml);
            }
            switchInfo.setType(type, desc);
        },
        // 开启发货单
        switchHandle: function (action) {
            if (action !== 'unopen') return;

            var me = this;
            var confirm, confirmText;
            me._doLog('delivery');

            confirmText = $t("启用发货单后将无法停用确认启用吗");
            confirm = crmUtil.confirm(confirmText, $t("发货单启用提示"), function () {
                // $target.parent().html('<span>'+ $t("系统正在处理请耐心等待") +'</span>');
                me.widgets.switchInfo.setTip('');
                me.widgets.switchInfo.setType('info', $t("系统正在处理请耐心等待"));

                me.model.enableDeliveryNote({
                    submitSelector: confirm.$('.b-g-btn'),
                }).done(function (res) {
                    confirm.hide();
                    if (res.Result.StatusCode == 0 && res.Value.enableStatus == 3) {
                        return; // 开启中
                    }
                    if (res.Result.StatusCode == 0 && res.Value.enableStatus == 2) {
                        me.showTip($t("发货单开启成功"), 1);
                        me.render();
                        CRM.control.refreshAside();
                        return;
                    }
                    if (res.Result.FailureCode == 1029) {
                        var deliveryErrMsg = res.Result.FailureMessage || $t("启用失败请稍后重试或联系纷享客服");
                        me.model.set('deliveryErrMsg', deliveryErrMsg);
                        me.widgets.switchInfo.setType('info', deliveryErrMsg);
                        me.showTip(res.Result.FailureMessage || $t("启用失败请稍后重试或联系纷享客服"));
                        return;
                    }
                    me.showTip($t("启用失败请稍后重试或联系纷享客服"));
                });
            });
        },
        doActionHandle: function (e) {
            var $target = $(e.target);
            var action = $target.attr('data-action');
            if (action !== 'save_deliverynote_config') return;

            // 保存ERP库存设置
            var me = this;
            this.model.setDeliveryConfig(function () {
                me.showTip($t("保存成功"), 1);
            }, $(e.target));
        },
    });
});
