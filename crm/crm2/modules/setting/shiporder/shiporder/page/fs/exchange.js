/**
 * @description ERP库存
 */
define(function (require, exports, module) {
	var Dialog = require('crm-widget/dialog/dialog');
	var Layout = require('../../layout/layout');
	var stopStock = require('./stopstock-html');
	var crmUtil = FS.crmUtil;

	return Layout.extend({
		mycomponents: {
			stockViewType: require('./stockviewtype/stockviewtype'),
		},

		options: {
			topInfo: {
				messages: [
					$t("开启退换货管理后会新增退换货单、退款单两个对象"),
					$t("退换货单默认包括2种业务类型均不强关联销售订单分别对应退货、换货业务场景"),
					$t("退款单不强关联销售订单，主要用来处理退货的退款及换货、补发场景下的差价退还"),
				],
				hasService: true,
			},
			layout: {
				switchInfo: {
					name: 'erpStock',
					label: $t("退换货管理状态："),
					type: 'unopen',
					btnLabel: $t("开启"),
					desc: $t("已启用"),
					tipHtml: '',
				},
			},
		},

		render: function () {
			this.super.render.call(this);
			this._loadInfos();
		},
		getDescribe: function () {
			var describe = this.super.getDescribe.call(this);
			describe.loading = true;
			return describe;
		},
		_loadInfos: function () {
			var me = this;
			this.model.fetchExchangeManageConfig(function (data) {
				if (data.errCode !== 0) {
					me.showErrMsg();
					return;
				}

				me.changeSwitchType(data);
				me.showContent();
			});
		},
		changeSwitchType: function (data) {
			var switchInfo = this.widgets.switchInfo;
			if (!switchInfo) return;

			data = data || this.model.toJSON();
			var type = 'unopen';
			var tipHtml;
			var desc;
			switch (data.exchangeReturnNoteStatus) {
				case 2:
					type = 'info';
					tipHtml = '<p class="shiporder-action_intro">' + $t('退换货单、退款单两个对象已开启') +
						'，' + $t('建议先去设置对象的字段、布局等信息') + '</p>';
					desc = $t("已启用");
					break;
				case 3:
					type = 'info';
					desc = $t("系统正在处理请耐心等待");
					break;
				default:
					break;
			}

			if (tipHtml) {
				switchInfo.setTip(tipHtml);
			}
			switchInfo.setType(type, desc);
		},
		// 开启退换货管理
        switchHandle: function (action) {
            if (action !== 'unopen') return;

			var me = this;
			var status = this.model.toJSON();

			if (status.stockStatus !== 2) {
				me.showTip($t("stock.stock_manage.info.text14")); // 启用退换货管理之前必须先启用库存，请确认库存成功开启后，再来尝试。
				return;
			}

			var confirmText = $t('stock.stock_manage.info.text15'); // 启用退换货管理后，不可停用此功能，是否确定开启？
			var confirm = crmUtil.confirm(confirmText, $t("温馨提示"), function () {
				me.model.enableExchangeReturn({
					submitSelector: confirm.$('.b-g-btn'),
				}).done(function (res) {
					confirm.hide();
					if (res.Result.StatusCode == 0) {
						crmUtil.remind(1, $t("退换货管理开启成功"));
						me.render();
						CRM.control.refreshAside();
						return;
					}
					me.showTip(res.Result.FailureMessage || $t("启用失败请稍后重试或联系纷享客服"));
				});
			});
        },
	});
});
