/**
 * @description 未开启库存
 */
define(function (require, exports, module) {
	var View = require("../../view/view");
	var Page = {
		baseinfo: require("../baseinfo/index").BaseInfo,
		manage: require("../manage/index").Manage,
		advance: require("../advance/index")
	};

	var getCurTab = function () {
		var curTab = sessionStorage.getItem("curTab") || 0;
		sessionStorage.removeItem("curTab");
		return Number(curTab);
	};

	return View.extend({
		options: {
			title: $t("stock.stock_management.stock_management"),
			titleLabel: "",
			tabMap: ["baseinfo", "manage", "advance"],
			tabs: [$t("基础设置"), $t("插件管理"), $t("高级功能")], // TODO 国际语言适配
			getCurTab: getCurTab
		},

		initialize(...args) {
			this.setBeginTab();
			View.prototype.initialize.apply(this, args);
		},

		setBeginTab() {
			const queryParams = CRM.util.getTplQueryParams(window.location.href);
			const tabsValue = queryParams.tabs;
			const allTabs = this.options.tabMap.map((item, index) => index.toString());
			// 只有'0' '1'  '2' 才处理
			if (!allTabs.includes(tabsValue)) {
				return;
			}

			sessionStorage.setItem("curTab", Number(tabsValue));
		},

		formatTabs: function (tabs) {
			let label = "";
			switch (Number(this.model.get("stockType"))) {
				case 3:
					label = $t("对接版·C类");
					tabs = tabs.splice(0, 1);
					break;
				case 4:
					label = $t("crm.发货单");
					break;
				default:
					if (this.model.get("isForErp")) {
						label = $t("对接版·B类");
					} else {
						label = $t("完整版·A类");
					}
					break;
			}

			this.options.titleLabel = label;
			if (_.isEmpty(tabs) || !_.isArray(tabs) || tabs.length < 2) {
				return [];
			}
			return tabs;
		},

		getCurPage: function () {
			var detail = this.options.tabMap[this.options.curTab] || "baseinfo";
			return Page[detail];
		},

		renderTpl: function () {
			var me = this;
			var Page = this.getCurPage();
			this.widgets.page && this.widgets.page.destroy();
			this.widgets.page = new Page({
				el: me.$container.get(0),
				model: me.model
			});
			this.widgets.page.$el.html('<div class="crm-loading tab-loading"></div>');
			this.widgets.page.render();
		}

		// destroy: function () {
		//     this.page && this.page.destroy();
		//     this.page = null;
		//     this.remove();
		// },
	});
});
