/**
 * @description 订货提交
 */
define(function (require, exports, module) {
	var Base = require('../../../components/components').base;
	var crmUtil = FS.crmUtil;

	module.exports = Base.extend({
		template: require('./tpl-html'),

		events: {
			'click .j-comp-enable': 'enable',
		},

		render: function () {
			var attr = this.fieldAttr;
			// var value = this.get(this.options.name);
			// var enableSetStockView = this.get('enableSetStockView');
			this.$el.html(this.template({
				label: attr.label,
				text: attr.text,
			}));

			this.changeValue();
		},

		setValue: function (isOpen) {
			var $wrap = $('.text-btns', this.$el);

			if (isOpen) {
				$wrap.html(`<span>${$t("已开启")}</span>`);
			} else {
				$wrap.html(`<a href="javascript:;" class="crm-btn crm-btn-sm crm-btn-primary j-comp-enable">${$t("开启")}</a>`);
			}
		},

		enable: function () {
			var me = this;
			var confirmText = $t('此功能开启后，将在销售订单的提交页面显示所选仓库（单一仓库订货模式）或所有适用仓库（合并仓库订货模式）的可用库存数量。')
				+ '<br><br>'
				+ $t('开启后，此功能不可关闭，是否确认开启此功能？');
			var confirm = crmUtil.confirm(confirmText, $t("温馨提示"), function () {
				me.model.enableOrderShowStock({
					submitSelector: confirm.$('.b-g-btn'),
				}).done(function (res) {
					confirm.hide();
					if (res.Result.StatusCode == 0 && res.Value.isSuccess) {
						crmUtil.remind(1, $t("开启成功"));
						me.set('showStockForOrder', true);
						me.renderBtns(true);
						return;
					}
					crmUtil.alert(res.Vlaue.message || $t("操作失败请稍后尝试或联系纷享客服"));
				});
			});
		},

		changeValue: function () {
			var value = this.get(this.options.name);
			this.setValue(value);
		},
	});
});
