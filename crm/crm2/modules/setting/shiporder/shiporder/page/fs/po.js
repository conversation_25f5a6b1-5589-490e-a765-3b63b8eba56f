/**
 * @description ERP库存
 */
define(function (require, exports, module) {
	var Layout = require('../../layout/layout');
	var crmUtil = FS.crmUtil;

	return Layout.extend({
		options: {
			topInfo: {
				messages: [
					$t("stock.crm.setting.shiporder.before_purchasing_module_is_opened"),
					$t("stock.crm.setting.shiporder.after_purchasing_module_is_opened"),
					$t("stock.crm.setting.shiporder.after_the_purchase_order_is_confirmed"),
					$t("stock.crm.setting.shiporder.after_the_purchasing_module_is_opened"),
				],
			},
			layout: {
				buttons: [{
					label: $t("保存"),
					action: 'save_po_config',
					show: true,
				}],
				switchInfo: {
					name: 'po',
					label: $t("stock.crm.setting.shiporder.purchasing_module_status"),
					type: 'unopen',
					desc: $t("已启用"),
				},
				fields: [{
					name: 'poDecimal',
					type: 'singleSelect',
					label: $t("stock.crm.setting.shiporder.purchase_order_accuracy"),
					range: [0, 9],
					tip: '',
				}],
			},
		},

		render: function () {
			this.super.render.call(this);
			this.$setting = this.$('.j-settings');
			this._loadInfos();
		},
		getDescribe: function () {
			var describe = this.super.getDescribe.call(this);
			describe.loading = true;
			return describe;
		},
		_loadInfos: function () {
			var me = this;
			this.model.fetchPO(function (data) {
				if (data.errCode !== 0) {
					me.showErrMsg();
					return;
				}
				me.changeSwitchType(data);
				if (data.poSwitch !== 2) {
					me.$setting.hide();
				} else {
					me.$setting.show();
					me.forms['poDecimal'].setTip($t('stock.crm.setting.shiporder.current_inventory_accuracy_is') + data.stockDecimal + $t('stock.crm.setting.shiporder.accuracy_of_the_purchase_order_cannot_be_greater'));
				}
				me.showContent();
			});
		},
		changeSwitchType: function (data) {
			var switchInfo = this.widgets.switchInfo;
			if (!switchInfo) return;

			var type = 'unopen';
			var tipHtml;
			switch (data.poSwitch) {
				case 2:
					type = 'info';
					tipHtml = '<p class="shiporder-action_intro">' + $t("stock.crm.setting.shiporder.the_purchasing_module_has_been_started") + '</p>';
					break;
				default:
					break;
			}

			if (tipHtml) {
				switchInfo.setTip(tipHtml);
			}
			switchInfo.setType(type);
		},
		// 开启关闭ERP库存
		switchHandle: function (action) {
			if (action !== 'unopen') return;

			var me = this;
			this.model.fetchStock(function (data) {
				if (data.errCode !== 0) {
					me.showTip($t("查询库存是否开启失败，请稍后重试或联系纷享客服"));
					return;
				}
				if (data.stockStatus !== 2) {
					me.showTip($t('stock.crm.setting.shiporder.after_the_inventory_is_successfully_opened'));
					return;
				}
				var confirmText = $t('stock.crm.setting.shiporder.after_enabling_the_purchasing_module');
				var confirm = crmUtil.confirm(confirmText, $t("温馨提示"), function () {
					me.model.enablePO({
						submitSelector: confirm.$('.b-g-btn'),
					}).done(function (res) {
						confirm.hide();
						if (res.Result.StatusCode == 0 && res.Value.enableStatus == 2) {
							crmUtil.remind(1, $t("stock.crm.setting.shiporder.the_purchasing_module_is_successfully_enabled"));
							me.render();
							CRM.control.refreshAside();
							return;
						}
						me.showTip( res.Result.FailureMessage || $t("启用失败请稍后重试或联系纷享客服"));
					});
				});
			});
		},
		doActionHandle: function (e) {
			var $target = $(e.target);
			var action = $target.attr('data-action');
			if (action !== 'save_po_config') return;

			// 保存ERP库存设置
			var me = this;
			if (this.validatePOConfig()) return;
			this.model.setPOConfig(function () {
				me.showTip($t("保存成功"), 1);
			}, $(e.target));
		},
		validatePOConfig: function () {
			var flag = false;
			var data = this.model.toJSON();
			// 校验采购单精度精度
			if (data.stockDecimal < data.poDecimal) {
				flag = true;
				this.showTip($t('stock.crm.setting.shiporder.accuracy of the purchase order cannot be greater_tip'));
			}
			return flag;
		},
	});
});
