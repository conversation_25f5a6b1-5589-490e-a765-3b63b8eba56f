/**
 * @description ERP库存
 */
define(function (require, exports, module) {
    var Dialog = require('crm-widget/dialog/dialog');
	var Layout = require('../../layout/layout');
    var stopStock = require('./stopstock-html');
    var crmUtil = FS.crmUtil;

    return Layout.extend({
        mycomponents: {
			stockViewType: require('./stockviewtype/stockviewtype'),
			showStockForOrder: require('./ordersubmit/ordersubmit'),
        },

        options: {
            topInfo: {
	            messages: [
	                $t("启用库存之前必须开启发货单。"),
	                $t("启用库存后将增加库存仓库入库单出库单和调拨单等对象。"),
	                $t("库存的数据不可直接编辑必须通过入库单出库单等单据来更新。"),
	                $t("库存开启后可手动停用但停用后不可再手动启用。"),
	            ],
                hasService: true,
	        },
        	layout: {
        		buttons: [{
	                label: $t("保存"),
	                action: 'save_stock_config',
	                show: true,
	            }],
	            switchInfo: {
	                name: 'erpStock',
	                label: $t("库存状态"),
                    type: 'unopen',
                    desc: $t("已停用"),
	            },
	            fields: [
					{
						name: 'showStockForOrder',
						type: 'text',
						label: $t("订单提交"),
						isHidden: true,
						text: $t('stock.stock_manage.info.text10'), // 订单提交页面，显示所选仓库的可用库存
					},
                    {
    		            name: 'validateOrderType',
    		            type: 'radio',
                        label: $t("订单校验"),
    		            options: [
    		                {
    		                    name: $t("可用库存数量不足时不可提交订单"),
    		                    value: 1,
                                tipHtml: [
                                    {
                                        type: 'b',
                                        label: $t("可用库存数量不足时不可提交订单"),
                                    },
                                    {
                                        label: $t("此设置项下创建出库单调拨单时校验可用库存可用库存不足不可创建出库单调拨单"),
                                    },
                                ],
    		                },
    		                {
    		                    name: $t("可用库存数量不足时仍可提交订单"),
    		                    value: 2,
                                tipHtml: [
                                    {
                                        type: 'b',
                                        label: $t("可用库存数量不足时仍可提交订单"),
                                    },
                                    {
                                        label: $t("此设置项下创建出库单调拨单时校验实际库存实际库存不足不可创建出库单调拨单"),
                                    },
                                ],
    		                },
    		            ]
    		        },
                    {
                        name: 'orderWarehouseType',
                        type: 'radio',
                        label: $t("订货仓库设置"),
                        options: [
                            {
                                name: $t("crm.单一仓库订货"),
                                value: 1,
                                tipHtml: [
                                    {
                                        type: 'b',
                                        label: $t("crm.单一仓库订货:"),
                                    },
                                    {
                                        label: $t("提交销售订单时需指定订货仓库根据指定的订货仓库来校验库存"),
                                    },
                                ],
                            },
                            {
                                name: $t("crm.合并仓库订货"),
                                value: 2,
                                tipHtml: [
                                    {
                                        type: 'b',
                                        label: $t("crm.合并仓库订货:"),
                                    },
                                    {
                                        label: $t("提交订单时无需手动选择仓库根据所有适用的仓库来校验库存"),
                                    },
                                ],
                            },
                        ]
                    },
                    {
                        name: 'stockViewType',
                        type: 'stockViewType',
                        label: $t("订货通设置"),
                        noEnable: $t("请先购买") + $t("企业互联") + $t("、") + $t("需开通订货通后可设置库存的显示方式"),
                        options: [
                            {
                                name: $t("不显示库存"),
                                value: 1,
                            },
                            {
                                name: $t("模糊显示库存"),
                                value: 3,
                                tipHtml: [
                                    {
                                        type: 'b',
                                        label: $t("模糊库存"),
                                    },
                                    {
                                        label: $t("当安全库存不为空时模糊库存有缺货少量充足三种"),
                                    },
                                    {
                                        label: $t("缺货：可用库存") + '<=0',
                                    },
                                    {
                                        label: $t("少量") + '：0 <' + $t("可用库存") + '<' + $t("安全库存"),
                                    },
                                    {
                                        label: $t('充足：安全库存<=可用库存，且可用库存大于0。'),
                                    },
                                    {
                                        label: $t("当安全库存为空时模糊库存有缺货充足两种"),
                                    },
                                    {
                                        label: $t("缺货：可用库存") + '<=0',
                                    },
                                    {
                                        label: $t("充足：可用库存") + '> 0',
                                    },
                                ],
                            },
                            {
                                name: $t("精确显示库存"),
                                value: 2,
                            },
                        ]
                    },
                    {
                        name: 'stockDecimal',
                        type: 'singleSelect',
                        label: $t("精度设置"),
                        range: [0, 9],
                        tip: '',
                    },
                    {
                        name: 'safetyStockType',
                        type: 'radio',
                        label: $t("安全库存设置"),
                        options: [
                            {
                                name: $t("统一设置"),
                                value: 1,
                                tipHtml: [
                                    {
                                        label: $t("在产品的安全库存字段中设置对所有仓库均适用"),
                                    },
                                ],
                            },
                            {
                                name: $t("分仓库设置"),
                                value: 2,
                                tipHtml: [
                                    {
                                        label: $t("在库存产品的安全库存字段中设置仅对该产品库存有效"),
                                    },
                                ],
                            },
                        ]
                    },
                    {
                        name: 'stockWarningType',
                        type: 'radio',
                        label: $t("库存预警"),
                        tip: $t("建议先去后台设置产品对象的") + $t("安全库存") + $t("字段和布局信息"),
                        options: [
                            {
                                name: $t("不开启预警"),
                                value: 1,
                            },
                            {
                                name: $t("开启预警当可用库存小于安全库存时收到预警"),
                                value: 2,
                            },
                        ]
                    },
                    {
                        name: 'stockShow',
                        type: 'checkboxs',
                        label: $t("库存显示"),
                        options: [
                            {
                                name: 'isNotShowZeroStockType',
                                label: $t("stock.stock_manage.inventory_display.dot_not_display_empty_stock"), // 不显示可用库存和实际库存为0的库存记录
							},
							{
								name: 'isOnlyShowOnSaleStockType',
								label: $t("stock.stock_manage.inventory_display.dot_not_display_discontinued_product"), // 不显示已下架产品的库存记录
							}
                        ]
                    },
                ],
        	},
        },

        render: function () {
        	this.super.render.call(this);
            this.$setting = this.$('.j-settings');
            this.$btns = this.$('.shiporder-btns');
        	this._loadInfos();
        },
        getDescribe: function () {
        	var describe = this.super.getDescribe.call(this);
        	describe.loading = true;
        	return describe;
        },
        _loadInfos: function () {
        	var me = this;
        	this.model.fetchStock(function (data) {
        		if (data.errCode !== 0) {
        			me.showErrMsg();
        			return;
        		}

				// 拥有UI事件模块才能
				if (data.hasUiEventModule) {
					me.setField('showStockForOrder', {
						isHidden: false,
					});
					me.forms['showStockForOrder'].setStatus();
				}

                me.changeSwitchType(data);
                if (data.stockStatus === 2) { // 已开启库存
                    me.$setting.show();
                    me.$btns.show();
                    me.forms['stockDecimal'].setTip($t("当前订单产品的小数位数为") + data.salesOrderProductDecimal +
                        (data.poSwitch === 2 ? ($t('stock.stock_manage.info.text11') + data.poDecimal) : '')); // , 采购单产品的小数位数为
                } else if (data.stockStatus === 3) {
                    me.disableSettings();
                } else {
                    me.$setting.hide();
                }
                me.showContent();
        	});
        },
        changeSwitchType: function (data) {
            var switchInfo = this.widgets.switchInfo;
            if (!switchInfo) return;

            var type = 'off';
            var tipHtml;
            switch(data.stockStatus) {
                case 2:
                    type = 'on';
                    tipHtml = '<p class="shiporder-action_intro">' + $t("库存已开启建议先去后台设置") +
                        $t("crm.库存") +
                        $t("、") + $t("crm.仓库") +
                        $t("、") + $t("crm.入库单") +
                        $t("对象的字段布局等信息") + '</p>';
                    break;
                case 3:
                    type = 'info';
                    break;
                default:
                    break;
            }

            if (tipHtml) {
                switchInfo.setTip(tipHtml);
            }
            switchInfo.setType(type);
        },
        disableSettings: function () {
            this.$setting.show();
            this.$btns.hide();
            _.each(this.forms, function (item, name) {
                item.disabeld && item.disabeld();
                item.setStatus && item.setStatus();
            });
        },
        // 开启关闭库存
        switchHandle: function (action) {
            if (['off', 'on'].indexOf(action) === -1) return;

            var me = this;
            var confirm, confirmText;
            var status = this.model.toJSON();
            me._doLog('stock');

             // 未开启库存
            if (status.stockStatus === 1) {
                if (status.deliveryStatus !== 2) {
                    me.showTip($t("启用库存之前必须先启用发货单请确认发货单成功开启后再来尝试。"));
                    return;
                }
                confirmText = $t("库存启用后会对销售订单和发货单的使用产生一定的影响开启前请务必确认您已知晓这些影响。")
                    + '<br><br>'
                    + $t("库存启用成功后将无法关闭您确认要启用库存吗");
                confirm = crmUtil.confirm(confirmText, $t("库存启用提示"), function () {
                    me.model.changeStockStatus({
                        data: {
                            stockSwitch: '2',
                        },
                        submitSelector: confirm.$('.b-g-btn'),
                    }).done(function (res) {
                        confirm.hide();
                        if (res.Result.StatusCode == 0 && res.Result.FailureCode == 0) {
                            crmUtil.remind(1, $t("库存开启成功"));
                            me.render();
                            CRM.control.refreshAside();
                            return;
                        }
                        if (res.Result.FailureCode == 1001) {
                            me.showTip(res.Result.FailureMessage || $t("启用失败请稍后重试或联系纷享客服"));
                            return;
                        }
                        me.showTip($t("启用失败请稍后重试或联系纷享客服"));
                    });
                });
            }

            // 已开启库存
            if (status.stockStatus === 2) {
                this.showStopStockDialog(function (res) {
                    if (res.Result.StatusCode == 0 && res.Result.FailureCode == 0 && res.Value.isSuccess) {
                        crmUtil.remind(1, $t("库存停用成功"));
                        me.render();
                        return;
                    }
                    if (res.Result.FailureCode == 1001) {
                        me.showTip(res.Result.FailureMessage || $t("stock.stock_manage.info.text12")); // 停用失败请稍后重试或联系纷享客服
                        return;
                    }
                    me.showTip($t("stock.stock_manage.info.text12")); // 停用失败请稍后重试或联系纷享客服
                });
            }
        },
        // 显示停用提醒
        showStopStockDialog: function (cb) {
            var me = this;
            var dialog = new Dialog({
                title: $t("库存停用"),
                classPrefix: 'crm-c-dialog crm-c-dialog-stopstock',
                width: 650,
                showBtns: true,
                showScroll: false,
                content: stopStock(),
            });
            var timer;
            var destroy = function () {
                timer && clearInterval(timer);
                dialog && dialog.destroy && dialog.destroy();
                dialog = timer = null;
            };
            dialog.on('hide', destroy);
            dialog.on('dialogCancel', destroy);
            dialog.on('dialogEnter', function (e) {
                var t = this;
                me.model.changeStockStatus({
                    data: {
                        stockSwitch: '3',
                    },
                    submitSelector: $('[action-type="dialogEnter"]', t.element),
                }).done(function (res) {
                    destroy();
                    cb && cb(res);
                });
            });
            dialog.on('agree', function (e) {
                var t = this;
                var $target = $(e.target);
                if (t._canAgree) {
                    $target.toggleClass('on');
                    var $enter = $('[action-type="dialogEnter"]', t.element);
                    if ($target.hasClass('on')) {
                        $enter.removeClass('btn-disabled');
                    } else {
                        $enter.addClass('btn-disabled');
                    }
                }
            });
            dialog.on('show', function (e) {
                var t = this;
                // 初始设置
                t._canAgree = false;
                $('[action-type="dialogEnter"]', t.element).addClass('btn-disabled');

                // 倒计时设置
                var countdown = 30;
                var $countdown = $('.stopstock-countdown', t.element)
                timer && clearInterval(timer);
                timer = setInterval(function () {
                    countdown--;
                    if (countdown === 0) {
                        t._canAgree = true;
                        timer && clearInterval(timer);
                        timer = null;
                        $countdown.text('');
                    } else {
                        $countdown.text(countdown + 's');
                    }
                }, 1000);
            });
            dialog.show();
        },
        doActionHandle: function (e) {
            var $target = $(e.target);
            var action = $target.attr('data-action');
            if (action !== 'save_stock_config') return;

            // 保存库存设置
            var me = this;
            if (this.validateStockConfig()) return;
            this.model.setStockConfig(function () {
                me.showTip($t("保存成功"), 1);
            }, $(e.target));
        },
        validateStockConfig: function () {
            var flag = false;
            var data = this.model.toJSON();
            // 校验库存精度
            if (data.stockDecimal < data.salesOrderProductDecimal) {
                flag = true;
				this.showTip($t("库存精度不可小于订单产品精度请重新设置"));
				return flag;
            }
            if (data.poSwitch === 2 && data.stockDecimal < data.poDecimal) {
                flag = true;
                this.showTip($t('stock.stock_manage.info.text13')); // 库存精度不允许小于采购单产品的精度！
            }
            return flag;
        },
    });
});
