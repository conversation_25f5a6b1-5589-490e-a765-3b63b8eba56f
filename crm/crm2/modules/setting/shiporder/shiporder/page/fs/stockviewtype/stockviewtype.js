/**
 * @description 订货设置
 */
define(function (require, exports, module) {
	var Radio = require('../../../components/components').Radio;

	module.exports = Radio.extend({
		template: require('./tpl-html'),
		
		render: function () {
			var attr = this.fieldAttr;
			var value = this.get(this.options.name);
			var enableSetStockView = this.get('enableSetStockView');
			this.$el.html(this.template({
				label: attr.label,
				options: attr.options,
				value: value,
				enableSetStockView: enableSetStockView,
				noEnable: attr.noEnable,
				parseTipHtml: function (list) {
					var tipHtml = '';
					_.each(list, function (item) {
						if (item.type === 'b') {
							tipHtml += ('<b>' + _.escape(item.label) + '</b>');
						} else {
							tipHtml += ('<p>' + _.escape(item.label) + '</p>');
						}
					});
					return tipHtml;
				},
			}));
		},
	});
});
