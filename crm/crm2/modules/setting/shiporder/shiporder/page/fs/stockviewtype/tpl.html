<div class="shiporder-set {{obj.enableSetStockView ? '' : 'shiporder-set--flex'}}">
	<label class="shiporder-set_label">{{{{-obj.label}}}}</label>
	## if (!obj.enableSetStockView) { ##
		<p>{{obj.noEnable}}</p>
	## } else { ##
		<div class="shiporder-radios">
			## _.each(obj.options, function (item) { ##
				<div class="shiporder-radio">
					<div class="shiporder-radio_set {{obj.value == item.value ? 'on' : ''}}" data-value="{{item.value}}">
						<i class="shiporder-radio_icon"></i>
						<span>{{{{-item.name}}}}</span>
						## if (!_.isEmpty(item.tipHtml)) { ##
							<span class="shiporder-tip">
								<i class="shiporder-tip_icon">?</i>
								<div class="shiporder-tip_intro">
									{{obj.parseTipHtml(item.tipHtml)}}
								</div>
							</span>
						## } ##
					</div>
				</div>
			## }) ##
		</div>
	## } ##
</div>