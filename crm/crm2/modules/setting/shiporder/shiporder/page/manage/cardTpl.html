<div class="shiporder-manage_category-card-title">{{ $t(obj.title) }}</div>
<div class="shiporder-manage_category-card-content">{{ $t(obj.content)}}</div>
<div class="shiporder-manage_category-card-footer">
    <div class="shiporder-manage_category-card-options">
        <!-- <div class="shiporder-manage_category-card-option j-detail">详情</div> -->
        <div class="shiporder-manage_category-card-option {{obj.settingTriger}}">{{ $t("参数设置") }}</div>
    </div>
    <div class="shiporder-manage_category-card-switcher">
        <i class="oprate-btn_switch
        {{obj.switchTriger}}
        {{obj.isOn ? 'oprate-btn_switch--on' : ''}}
        {{obj.disabled ? 'oprate-btn_switch--disabled' : ''}}
        "
        ></i>
    </div>
</div>