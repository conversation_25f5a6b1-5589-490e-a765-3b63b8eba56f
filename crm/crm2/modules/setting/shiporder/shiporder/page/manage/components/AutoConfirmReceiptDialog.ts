/**
 * @description 发货单自动确认收货参数设置
 */

import {
	queryOpenSign,
	getPluginsConfigValueByKey,
	getDnTemplates,
} from "crm-modules/common/stock/api";
import { DeliveryNoteSupportSignatures } from "crm-modules/setting/shiporder/shiporder/model/model";

export interface AutoConfrimReceiptValue extends DeliveryNoteSupportSignatures {
	value: 1 | 2;
	days: number;

	// 是否开启了电子签章功能
	hasOpenSign: boolean;
}

const template = `
			<fx-dialog
			  :visible.sync='visible'
			  ref='dialog1'
			  :append-to-body='true'
			  :title="$t('确认收货-参数设置')"
			  @closed='handleClosed'
			  class='auto-confirm-receipt-dialog'
			>
				<div v-loading="loading">
					<div class="auto-confirm-receipt-dialog__tab">
					 <fx-radio-group v-model="tab">
						 <fx-radio :label="'1'">
							 <span>{{ $t('系统自动确认收货') }}</span>
						 </fx-radio>
						 <br>
						 <fx-radio :label="'2'" v-if="hasOpenSign">
							 <span>{{ $t('手动确认收货，且必须电子签名') }}</span>
						 </fx-radio>
					 </fx-radio-group>
					 </div>
					<div class="auto-confirm-receipt-dialog__tab__content" v-show="tab=='1'">
						<fx-radio-group v-model='value'>
							<fx-radio :label='1'>
								<span>{{ $t('发货单确认后') }}
									<fx-input-number
										class='auto-confirm-receipt-dialog__number'
										v-model='days'
										type='number'
										:min='1'
										:max='365'
										:step='1'
										:step-strictly='true'
										:controls='false'
									></fx-input-number>
								  {{ $t('天，系统自动确认收货') }}
								  <i class='icon-tips'></i>
								</span>
							</fx-radio>
							<br>
							<fx-radio :label='2'>
								<span>{{ $t('发货单物流签收后，系统自动确认收货') }}</span>
							</fx-radio>
							<p class='auto-confirm-receipt-dialog__radio-tips'>{{ $t('需要在发货单填写正确的物流信息，并订阅物流签收提醒，每次订阅均会扣除一次物流查询次数。') }}</p>
						</fx-radio-group>
						</div>
					<div class="auto-confirm-receipt-dialog__tab__content" v-show="tab=='2'">
						<div class="auto-confirm-receipt-dialog__tab__content__inner">
							<div class="auto-confirm-receipt-dialog__tab__content__row">
								<span>{{$t('上游签署人')}}</span>
								<fx-select
								v-model="SSupStreamSigner"
								:options="options"
								size="small"
								placeholder="请选择">
								</fx-select>
							</div>
							<div class="auto-confirm-receipt-dialog__tab__content__row">
								<span>{{$t('下游签署人')}}</span>
								<fx-select
								v-model="SSdownStreamSigner"
								:options="options1"
								size="small"
								placeholder="请选择">
								</fx-select>
							</div>
							<div class="auto-confirm-receipt-dialog__tab__content__row" style='position:relative;'>
								<a style="position: absolute;right: -110px;width: 100px;top: calc(50% - 11px);height: 22px;cursor: pointer;" @click='handleClick'>{{$t('设置打印模板')}}</a>
								<span>{{$t('模板')}}</span>
								<fx-select
								v-model="SSprintTemplateId"
								:options="optionsTemplate"
								size="small"
								placeholder="请选择">
								</fx-select>
							</div>
						</div>
					</div>
				</div>
				<span slot='footer' class='dialog-footer'>
    				<fx-button
    					type='primary'
    					@click='handleConfirm'
    					size='small'
    					:disabled="loading"
    				>{{$t('确 定')}}</fx-button>
    				<fx-button
    					@click='handleClosed'
    					size='small'
    					:disabled="loading"
    				>{{$t('取 消')}}</fx-button>
    			</span>
			</fx-dialog>
			`;

const AutoConfirmReceiptDialog = Vue.extend({
	template,

	props: {
		// 选中
		defaultValue: Number,
		defaultDays: String,
		defaultSSstatus: String,
		defaultSSupStreamSigner: String,
		defaultSSdownStreamSigner: String,
		defaultSSprintTemplateId: String,
		defaultSSsignAppType: String,
	},

	data() {
		return {
			loading: true,
			visible: true,
			value: null,
			days: null,
			//  电子签章功能是否开启了
			hasOpenSign: false,
			SSstatus: "",
			// 上游签收人
			SSupStreamSigner: "0",
			// 下游签收人
			SSdownStreamSigner: "0",
			// 签收模板
			SSprintTemplateId: "",
			// 电子签厂商
			SSsignAppType: "",
			// 1自动，2手动
			tab: "1",
			options: [
				{
					value: "1",
					label: $t("stock.stock_manage.deliverynote_manager"), // 发货单负责人
				},
				{
					value: "2",
					label:$t("stock.stock_manage.account_manager"), // 客户负责人
				},
			],

			options1: [
				{
					value: "1",
					label:$t("stock.stock_manage.account_contact"), // 客户联系人
				},
				{
					value: "2",
					label:$t("stock.stock_manage.consignee"), // 收货人
				},
			],

			optionsTemplate: [{}],
		};
	},

	created() {
		this.value = this.defaultValue;
		this.days = this.defaultDays;
		this.getSignConfig();
	},

	methods: {
		handleClick(){
			window.open(FS.util.getFxHash('#crmmanage/=/module-templatemanage', { mod: 'manage' }))
		},
		getValue() {
			const value: AutoConfrimReceiptValue = {
				value: this.value,
				days: this.days,
				hasOpenSign: this.hasOpenSign,
				SSstatus: this.tab,
				SSupStreamSigner: this.SSupStreamSigner,
				SSdownStreamSigner: this.SSdownStreamSigner,
				SSprintTemplateId: this.SSprintTemplateId,
				SSsignAppType: this.SSsignAppType,
			};
			return value;
		},

		handleConfirm() {
			this.$emit("confirm", this.getValue());
			this.handleClosed();
		},

		handleClosed() {
			this.$emit("close", this.getValue());
			this.$destroy();
		},

		// 电子签章
		getSignConfig() {
			this.loading = true;
			// 电子签章功能是否开启
			return queryOpenSign()
				.then((res:Record<string, any>) => {
					this.hasOpenSign = res.hasOpenSign;
					this.SSsignAppType = res.signAppType;
				})
				.then(() => {
					if (!this.hasOpenSign) {
						return;
					}

					const promises = [
						// 发货单电子签配置
						getPluginsConfigValueByKey({
							key: "delivery_note_support_signatures",
						}),
						getDnTemplates(),
					];

					return Promise.all(promises);
				})
				.then((res) => {
					const [r1, r2] = res;
					this.SSstatus = r1.status;
					// 关闭电子签===1，刚好对应tab===1。因此这样写。无特殊含义
					this.tab = r1.status;
					this.SSupStreamSigner = r1.upStreamSigner;
					this.SSdownStreamSigner = r1.downStreamSigner;
					this.SSprintTemplateId = r1.printTemplateId;
					this.optionsTemplate = r2;
				})
				.catch((e) => {
					console.error(e.message);
				})
				.finally(() => {
					this.loading = false;
				});
		},
	},
});

export { AutoConfirmReceiptDialog };
