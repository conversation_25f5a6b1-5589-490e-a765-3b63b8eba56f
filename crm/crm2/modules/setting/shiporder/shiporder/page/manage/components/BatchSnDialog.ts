/**
 * @description k3c插件，开启对话框
 */
 import {
	asyncSetConfigValue,
	commonGetConfigValueByKey,
	setBatchConfig,
	enableSnParamConfig,
	getBatchSnConfig
} from "crm-modules/common/stock/api";

const template = `
			<fx-dialog
			  :visible.sync="visible"
			  ref="dialog1"
			  :append-to-body="true"
			  :title="$t('批次与序列号-参数设置')"
			  width="550px"
			  @closed="handleClosed"
			>
				<template >
					<fx-tabs
						v-model='activeName'
			 			v-loading="loading"
					>
    				<fx-tab-pane
    					:label="$t('序列号参数')"
    					name='sn'
    				>
						<div v-if='has_open_multi_sn'>
							<span>{{ $t('发货单产品：序列号字段支持查找关联多个序列号') }}</span>
								<fx-switch
									:value='openMultiSn'
									@change='handleOpenMultiSnSwitch'
									:disabled="openMultiSn==='2'"
									inactive-value='1'
									active-value='2'
								size="mini"
							></fx-switch>
						</div>
						<div style="margin-top: 10px;">
							<span>{{ $t('stock.crm.manage.enable_batch_and_sn_at_the_same_time') }}</span>
							<fx-switch
							:value='enableBatchAndSnAtTheSameTime'
							@change='handleEnableBatchAndSnAtTheSameTime'
							:disabled="enableBatchAndSnAtTheSameTime==='1'"
							inactive-value='0'
							active-value='1'
							size="mini"
							></fx-switch>
						</div>
						<div style="margin-top: 10px;">
							<span>{{ $t('开启品级管理，针对序列号管理的产品，库存区分良品、不良品') }}</span>
							<fx-switch
							:value='dht_serial_number_param_switch'
							@change='handleOpenWareHouseSwitch'
							:disabled="dht_serial_number_param_switch"
							size="mini"
							></fx-switch>
						</div>
						</fx-tab-pane>
  				</fx-tabs>
				</template>
				<span slot="footer" class="dialog-footer">
<!--    				<fx-button -->
<!--    					type="primary" -->
<!--    					@click="handleConfirm" -->
<!--    					size="small"-->
<!--    				>确 定</fx-button>-->
    				<fx-button
    					@click="handleClosed"
    					size="small"
    				>{{ $t('取 消') }}</fx-button>
    			</span>
			</fx-dialog>
			`;

const BatchSnDialog = Vue.extend({
	template,

	props: {
		propWillExpireStockWarningType: String,
		has_open_multi_sn: Boolean,
		propOpenMultiSn: String,
		dht_serial_number_param_switch: Boolean,
	},

	data() {
		return {
			loading: false,
			visible: true,
			activeName: "sn",
			willExpireStockWarningType: "1",
			openMultiSn: "1",
			enableBatchAndSnAtTheSameTime: "0", // 开启 1; 关闭 0
		};
	},

	created() {
		console.log(
			"dht_serial_number_param_switch::" +
				this.dht_serial_number_param_switch
		);
		this.willExpireStockWarningType = this.propWillExpireStockWarningType;
		this.init();
	},

	methods: {
		init() {
			this.loading = true;
			const batSnConfigReq = this.getBatchSnConfig(),
				enableBatchAndSnAtTheSameTimeConfigReq =
					this.getEnableBatchAndSnAtTheSameTimeConfig(),
				multiSnStatusReq =
					this.has_open_multi_sn && this.getMultiSnStatus();
			const requestArr = [
				batSnConfigReq,
				enableBatchAndSnAtTheSameTimeConfigReq,
				multiSnStatusReq,
			];

			Promise.all(requestArr)
				.catch((err) => {
					console.log(err);
				})
				.finally(() => {
					this.loading = false;
				});
		},

		getBatchSnConfig() {
			return getBatchSnConfig().then((res) => {
				console.log("````````````````````");
				console.log(res);
				this.dht_serial_number_param_switch =
					res["snParamSwitch"] == "1" ? true : false;
			});
		},

		// 获取 同时开启批次序列号开关 状态
		getEnableBatchAndSnAtTheSameTimeConfig() {
			return commonGetConfigValueByKey({
				key: "enable_batch_and_sn_at_the_same_time",
			}).then((res) => {
				this.enableBatchAndSnAtTheSameTime = res.value;
			});
		},

		// 获取序列号多选状态
		getMultiSnStatus() {
			return commonGetConfigValueByKey({
				key: "open_multi_sn",
			}).then((res) => {
				this.openMultiSn = res.value;
			});
		},

		// 批次库存预警
		handleWillExpireStockWarningTypeInput: _.throttle(function (value) {
			this.loading = true;
			return setBatchConfig({
				willExpireStockWarningType: value,
			})
				.then(() => {
					this.willExpireStockWarningType = value;
					this.$emit("updateWillExpireStockWarningType", value);
				})
				.finally(() => {
					this.loading = false;
				});
		}, 1000),

		// 开启序列号多选
		handleOpenMultiSnSwitch: _.throttle(function (value) {
			this.loading = true;
			asyncSetConfigValue()
				.circle(
					{ key: "open_multi_sn", value, token: "" },
					(data, stop) => {
						if (data.resultCode == 1) stop(data);
					}
				)
				.res((res) => {
					this.loading = false;
					this.openMultiSn = value;
					FS.crmUtil.remind(1, $t("操作成功"));
				})
				.catch(() => {
					this.loading = false;
				});
		}, 1000),

		openConfirmMessageBox() {
			return new Promise((resolve) => {
				this.$confirm(
					$t("该功能开启后不可关闭，是否确认开启？"),
					$t("提示"),
					{
						confirmButtonText: $t("确定"),
						cancelButtonText: $t("取消"),
					}
				)
					.then(() => {
						resolve(1);
					})
					.catch(() => {
						resolve(0);
					});
			});
		},

		// 开启序列号 良品 不良品
		async handleOpenWareHouseSwitch() {
			if (this.dht_serial_number_param_switch) return;

			const confirmResult = await this.openConfirmMessageBox();
			if (!confirmResult) return;

			this.loading = true;
			enableSnParamConfig().then((res) => {
				this.loading = false;
				console.log("resresres::", res);
				if (res["isSuccess"]) {
					this.dht_serial_number_param_switch = true;
					FS.crmUtil.remind(1, $t("操作成功"));
				}else{
					FS.crmUtil.remind(1, $t("stock.crm.setting.shiporder.abnormal_operation"));
				}
			});
		},

		// 开启 同时开启批次序列号开关
		handleEnableBatchAndSnAtTheSameTime: _.throttle(async function (value) {
			const confirmResult = await this.openConfirmMessageBox();
			if (!confirmResult) return;

			this.loading = true;
			asyncSetConfigValue()
				.circle(
					{
						key: "enable_batch_and_sn_at_the_same_time",
						value: "1",
						token: "",
					},
					(data, stop) => {
						if (data.resultCode == 1) stop(data);
					}
				)
				.res((res) => {
					this.loading = false;
					this.enableBatchAndSnAtTheSameTime = value;
					FS.crmUtil.remind(1, $t("操作成功"));
				})
				.catch(() => {
					this.loading = false;
				});
		}, 1000),

		handleClosed() {
			this.$destroy();
		},
	},
});

export { BatchSnDialog };
