/**
 * @description k3c插件，开启对话框
 */
import {
  asyncSetConfigValue,
} from "crm-modules/common/stock/api";


// $t  stock.cost_management.setting.tip  成本管理-参数设置
// $t  stock.cost_management.setting.title  请选择行业模版，不同的模版的成本价取值逻辑会有不同
// $t  stock.cost_management.setting.normal.title 通用模版
// $t  stock.cost_management.setting.normal  用户可自行输入不同单据的成本单价，按照用户输入的价格进行成本计算。 
// $t  stock.cost_management.setting.kx.title  快消行业模版
// $t  stock.cost_management.setting.kx  预置了快消行业常见的单价取值逻辑，并进行对应的成本计算。 


const template = `
			<fx-dialog
			  :visible.sync='visible'
			  ref='dialog1'
			  :append-to-body='true'
			  :title="$t('stock.cost_management.setting.tip')"
			  width='600px'
			  @closed='handleClosed'
			>
				<template >
				<div v-loading ='loading'>
    					<div style='margin-bottom: 10px'>{{ $t('stock.cost_management.setting.title') }}</div>
  					  <fx-radio-group
  					    :value='cost_management_calculate'
  					    @input='handleCostManagementCalculate'
  					  >
							  <fx-radio style='margin-bottom: 10px' label='1'>{{ $t('stock.cost_management.setting.normal.title') }} <span style='color: gray'>{{$t('stock.cost_management.setting.normal')}}</span></fx-radio>
  						  <fx-radio style='margin-bottom: 10px' label='2'>{{ $t('stock.cost_management.setting.kx.title') }} <span style='color: gray'>{{$t('stock.cost_management.setting.kx')}}</span></fx-radio>
  					  </fx-radio-group>
				</div>
				</template>
				<span slot='footer' class='dialog-footer'>
<!--    				<fx-button -->
<!--    					type="primary" -->
<!--    					@click="handleConfirm" -->
<!--    					size="small"-->
<!--    				>确 定</fx-button>-->
    				<fx-button
    					@click='handleClosed'
    					size='small'
    				>{{ $t('取 消') }}</fx-button>
    			</span>
			</fx-dialog>
			`;

const CostManagementDialog = Vue.extend({
  template,

  props: {
    prop_cost_management_calculate: String,
  },

  data() {
    return {
      loading: false,
      visible: true,
      cost_management_calculate: "1",
    };
  },

  created() {
    this.cost_management_calculate = this.prop_cost_management_calculate;
  },

  methods: {
    // 开启序列号多选
    handleCostManagementCalculate: _.throttle(function (value) {
      this.loading = true;
      asyncSetConfigValue()
        .circle({ key: "cost_management_calculate", value, token: "" }, (data, stop) => {
          if (data.resultCode == 1) stop(data);
        })
        .res((res) => {
          this.loading = false;
          this.cost_management_calculate = value;
          this.$emit("updatecost_management_calculate", value);
          // FS.crmUtil.remind(1, $t("操作成功"));
        })
        .catch(() => {
          this.loading = false;
        });
    }, 1000),

    handleClosed() {
      this.$destroy();
    }
  }
});

export { CostManagementDialog };
