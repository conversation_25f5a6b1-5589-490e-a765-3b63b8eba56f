/**
 * @description 查看指引
 */
export const CustomFunctionGuide = Vue.extend({
	template: `
			<fx-dialog
				class="app-custom-function-guide"
  				:visible="true"
  				fullscreen
  				appendToBody
  				:title="$t('发货单函数配置指引')"
  				@close="$emit('close')"
  			>
			 	<template >
				 	<h6>{{ $t('一、进入自定义函数管理页面') }}</h6>
				 	<fx-image
				 		class="app-custom-function-guide__pic"
    					style="width: 100%"
					    :src="srcList[0]"
					    :img-index="0"
					    :preview-src-list="srcList"
					    show-download
					>
					</fx-image>
				 	<h6>{{ $t('二、点击右上角的“新建函数”，在弹出框中输入配置信息：') }}</h6>
				 	<fx-image
				 		class="app-custom-function-guide__pic"
    					style="width: 100%"
					    :src="srcList[1]"
					    :img-index="1"
					    :preview-src-list="srcList"
					    show-download
					>
					</fx-image>
					<p>{{ $t('函数名称、ApiName可自定义，命名空间选择“校验函数”，返回值类型选择“ValidateResult”，绑定对象选择“发货单”。') }}</p>
					<p>{{ $t('进入下一步后，将以下文本复制后粘贴到函数的输入框内，点击保存，即可完成函数的创建。') }}</p>
					<fx-image
						class="app-custom-function-guide__pic"
    					style="width: 100%"
					    :src="srcList[2]"
					    :img-index="2"
					    :preview-src-list="srcList"
					    show-download
					>
					</fx-image>
					<textarea
					ref="copyfunction"
					style="position: absolute;opacity: 0;"
					/></textarea>
				 	<fx-button 
				 		style="margin-left: calc(50% - 80px);margin-top: 40px"
				 		@click="handleCopy" 
				 		type="success" 
				 		round>{{ $t('点我复制函数信息') }}
				 	</fx-button>
				 	<h6>{{ $t('三、在流程的审批节点配置自定义函数作为前置条件') }}</h6>
				 	<fx-image
				 		class="app-custom-function-guide__pic"
    					style="width: 100%"
					    :src="srcList[3]"
					    :img-index="3"
					    :preview-src-list="srcList"
					    show-download
					>
					</fx-image>
					<fx-image
						class="app-custom-function-guide__pic"
    					style="width: 100%"
					    :src="srcList[4]"
					    :img-index="4"
					    :preview-src-list="srcList"
					    show-download
					>
					</fx-image>
					<fx-image
					 	class="app-custom-function-guide__pic"
    					style="width: 100%"
					    :src="srcList[5]"
					    :img-index="5"
					    :preview-src-list="srcList"
					    show-download
					>
					</fx-image>
					<p>{{ $t('前置条件选择“基于自定义函数”，然后选择刚刚配置的函数即可。') }}</p>
  				</template>
			</fx-dialog>
		`,

	data() {
		return {
			language: 'zh-CN'
		};
	},
	
	computed: {
		srcList() {
			return [
				`https://a9.fspage.com/FSR/uipaas/stock/guide1_${this.language}.png`,
				`https://a9.fspage.com/FSR/uipaas/stock/guide2_${this.language}.png`,
				`https://a9.fspage.com/FSR/uipaas/stock/guide3_${this.language}.png`,
				`https://a9.fspage.com/FSR/uipaas/stock/guide4_${this.language}.png`,
				`https://a9.fspage.com/FSR/uipaas/stock/guide5_${this.language}.png`,
				`https://a9.fspage.com/FSR/uipaas/stock/guide6_${this.language}.png`,
			]
		}
			
	},

	methods: {
		handleCopy() {
			const str =
				"Map requestParam = [ \n" +
				'\t//"deliveryNoteDataId":"600a934cf08093000131f31c"\n' +
				'\t// "deliveryNoteDataId":"600a962ff080930001321b74"\n' +
				'\t"deliveryNoteDataId":context.data._id\n' +
				"]; \n" +
				"\n" +
				'Fx.log.info("requsetParam=" + requestParam) \n' +
				"\n" +
				'def ret = Fx.proxy.callAPI("stock.checkDeliveryRealStock", null, requestParam) \n' +
				'Fx.log.info("ret=" + ret); \n' +
				"\n" +
				"HttpResult data = ret.data as HttpResult \n" +
				"\n" +
				"if(null == data) { \n" +
				"  return ValidateResult.build{\n" +
				"    success = false\n" +
				'    errorMessage = "网络超时"\n' +
				"  }\n" +
				"} \n" +
				"\n" +
				"String json = data.content\n" +
				'Fx.log.info("data.content:" + json)\n' +
				"\n" +
				"Map resultData = Fx.json.parse(json)\n" +
				"String realRetStr = resultData.result\n" +
				'Fx.log.info("realRetStr:" + realRetStr)\n' +
				"\n" +
				"Map realRetMap = Fx.json.parse(realRetStr)\n" +
				"boolean passCheck = realRetMap.passCheck\n" +
				"\n" +
				'String retMessage = "库存校验通过";\n' +
				"if (passCheck == false) {\n" +
				'\tretMessage = "库存校验不通过:"\n' +
				"\tString shortageProductStr = realRetMap.shortageProducts\n" +
				'\tFx.log.info("shortageProducts:" + shortageProductStr)\n' +
				"\tMap shortageProductMap = Fx.json.parse(shortageProductStr)\n" +
				"\tshortageProductMap.each { key, value -> \n" +
				"\t  String name = (String)key\n" +
				"\t  BigDecimal substact = (BigDecimal)value\n" +
				'\t  retMessage = retMessage + name + "还差" + substact + ";"\n' +
				"\t}\n" +
				"}\n" +
				"\n" +
				"def result = ValidateResult.build{\n" +
				"  success = passCheck\n" +
				"  errorMessage = retMessage\n" +
				"}\n" +
				"\n" +
				"return result";
			const input = this.$refs.copyfunction;
			input.value = str;
			input.select();
			const success = document.execCommand("copy");
			if (success) {
				this.$message({
					showClose: true,
					message: $t("拷贝成功。"),
					type: "success"
				});
			} else {
				this.$message({
					showClose: true,
					message: $t("拷贝失败，请重试。"),
					type: "error"
				});
			}
		}
	},
	
	created() {
		const language = FS.contacts.getCurrentEmployee().language
		if (language === 'zh-CN' || language === 'en') {
			this.language = language
		}
	}
});
