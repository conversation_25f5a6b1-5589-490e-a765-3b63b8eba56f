/**
 * @description 查看指引
 */
export const CustomFunctionGuideStock = Vue.extend({
	template: `
			<fx-dialog
				class="app-custom-function-guide"
  				:visible="true"
  				fullscreen
  				appendToBody
  				:title="$t('校验库存函数配置指引')"
  				@close="$emit('close')"
  			>
			 	<template >
				 	<h6>{{ $t('一、进入自定义函数管理页面') }}</h6>
				 	<fx-image
				 		class="app-custom-function-guide__pic"
    					style="width: 100%"
					    :src="srcList[0]"
					    :img-index="0"
					    :preview-src-list="srcList"
					    show-download
					>
					</fx-image>
				 	<h6>{{ $t('二、点击右上角的“新建函数”，在弹出框中输入配置信息：') }}</h6>
				 	<fx-image
				 		class="app-custom-function-guide__pic"
    					style="width: 100%"
					    :src="srcList[1]"
					    :img-index="1"
					    :preview-src-list="srcList"
					    show-download
					>
					</fx-image>
					<p>{{ $t('函数名称、ApiName可自定义，命名空间选择“校验函数”，返回值类型选择“ValidateResult”，绑定对象选择“发货单”。') }}</p>
					<p>{{ $t('进入下一步后，将以下文本复制后粘贴到函数的输入框内，点击保存，即可完成函数的创建。') }}</p>
					<fx-image
						class="app-custom-function-guide__pic"
    					style="width: 100%"
					    :src="srcList[2]"
					    :img-index="2"
					    :preview-src-list="srcList"
					    show-download
					>
					</fx-image>
					<textarea
					ref="copyfunction"
					style="position: absolute;opacity: 0;"
					/></textarea>
				 	<fx-button 
				 		style="margin-left: calc(50% - 80px);margin-top: 40px"
				 		@click="handleCopy" 
				 		type="success" 
				 		round>{{ $t('点我复制函数信息') }}
				 	</fx-button>
				 	<h6>{{ $t('三、在流程的审批节点配置自定义函数作为前置条件') }}</h6>
				 	<fx-image
				 		class="app-custom-function-guide__pic"
    					style="width: 100%"
					    :src="srcList[3]"
					    :img-index="3"
					    :preview-src-list="srcList"
					    show-download
					>
					</fx-image>
					<fx-image
						class="app-custom-function-guide__pic"
    					style="width: 100%"
					    :src="srcList[4]"
					    :img-index="4"
					    :preview-src-list="srcList"
					    show-download
					>
					</fx-image>
					<fx-image
					 	class="app-custom-function-guide__pic"
    					style="width: 100%"
					    :src="srcList[5]"
					    :img-index="5"
					    :preview-src-list="srcList"
					    show-download
					>
					</fx-image>
					<p>{{ $t('前置条件选择“基于自定义函数”，然后选择刚刚配置的函数即可。') }}</p>
  				</template>
			</fx-dialog>
		`,

	data() {
		return {
			language: 'zh-CN'
		};
	},
	
	computed: {
		srcList() {
			return [
				`https://a9.fspage.com/FSR/uipaas/stock/guide1_${this.language}.png`,
				`https://a9.fspage.com/FSR/uipaas/stock/check_stock1_${this.language}.png`,
				`https://a9.fspage.com/FSR/uipaas/stock/check_stock2_${this.language}.png`,
				`https://a9.fspage.com/FSR/uipaas/stock/check_stock3_${this.language}.png`,
				`https://a9.fspage.com/FSR/uipaas/stock/guide5_${this.language}.png`,
				`https://a9.fspage.com/FSR/uipaas/stock/check_stock4_${this.language}.png`,
			]
		}
	},

	methods: {
		handleCopy() {
			const str =`
			Map requestParam = [
                "dataId":context.data._id,
                "apiName":context.data.object_describe_api_name
            ];
            
            Fx.log.info("requsetParam=" + requestParam)
            def ret = Fx.proxy.callAPI("stock.flowCompletedCheckStock", null, requestParam)
            Fx.log.info("ret=" + ret)
            
            HttpResult data = ret.data as HttpResult
            if(null == data) {
                return "网络超时，请稍后重试"
            }
            def Map result = data.content as Map
            Fx.log.info("result=" + result)
            String msg = "校验成功";
            Boolean isSuccess = true;
            
            if(result == null || !result['result']['isSuccess']) {
                msg = result['result']['message']
                isSuccess = false
            }
            def validateResult = ValidateResult.build{
                success = isSuccess
                errorMessage = msg
            }
            
            return validateResult`;
			const input = this.$refs.copyfunction;
			input.value = str;
			input.select();
			const success = document.execCommand("copy");
			if (success) {
				this.$message({
					showClose: true,
					message: $t("拷贝成功。"),
					type: "success"
				});
			} else {
				this.$message({
					showClose: true,
					message: $t("拷贝失败，请重试。"),
					type: "error"
				});
			}
		}
	},

	created() {
		const language = FS.contacts.getCurrentEmployee().language
		if (language === 'zh-CN' || language === 'en') {
			this.language = language
		}
	}
});
