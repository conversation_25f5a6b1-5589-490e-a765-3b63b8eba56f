import {
    setExchangeSettingParam,
    getSparePartConfig
} from 'crm-modules/common/stock/api';

export const ExchangeSettingDialog = Vue.extend({
    template: `
        <fx-dialog class="exchange-setting-dialog" :title="$t('stock.shiporder.exchange_plugin_setting.title')"
            size="small"
            :visible.sync="visible"
            :append-to-body="true"
        >
            <div class="main-content" v-loading="isLoading">
                <div class="text">{{ $t('stock.shiporder.exchange_plugin_setting.text') }}</div>
                <fx-radio-group v-model="radio" is-vertical>
                    <fx-radio :label="1">{{ $t('stock.shiporder.exchange_plugin_setting.radio1') }}</fx-radio>
                    <fx-radio :label="2">{{ $t('stock.shiporder.exchange_plugin_setting.radio2') }}</fx-radio>
                </fx-radio-group>
            </div>
            <div slot="footer" class="dialog-footer">
                <fx-button type="primary" @click="onConfirm" size="small">{{ $t('确定') }}</fx-button>
                <fx-button @click="visible = false" size="small">{{ $t('取消') }}</fx-button>
            </div>
        </fx-dialog>
    `,
    data() {
        return {
            visible: true,
            isLoading: true,
            radio: 1,
        }
    },
    created() {
        getSparePartConfig({ key: 'returned_goods_auto_update_stock' }).then(res => {
            console.log(res);
            this.radio = res && Number(res.value);
        }).catch(err => {
            console.error(err);
        }).finally(() => {
            this.isLoading = false;
        });
    },
    methods: {
        onConfirm() {
            this.isLoading = true;
            setExchangeSettingParam(this.radio).then(res => {
                CRM.util.remind(1, $t('操作成功'));
            }).catch(err => {
                console.error(err);
                CRM.util.remind(3, $t('操作失败'));
            }).finally(() => {
                this.isLoading = false;
                this.visible = false
            });
        }
    }
});