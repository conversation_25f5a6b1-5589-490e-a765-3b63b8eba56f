import {
    openModifyFreezeInventorySwitch,
} from 'crm-modules/common/stock/api';

export const FreezeInventoryAdjustment = Vue.extend({
    template: `
        <fx-dialog class="freeze-inventory-adjustment-dialog" :title="$t('stock.shiporder.freeze_inventory_adjustment.plugin_title')"
            :visible.sync="visible"
            :append-to-body="true"
            @close="handleDialogClose"
        >
            <div class="main-content" v-loading="isLoading">
                <div class="notice-text">{{ $t('stock.shiporder.freeze_inventory_adjustment.notice_text') }}</div>
                <div class="text-container">
                    <div class="text-item">{{ '1.' + $t('stock.shiporder.freeze_inventory_adjustment.plugin_text1') }}</div>
                    <div class="text-item" v-if="isStockBAndDistributionOpen">{{ '2.' + $t('stock.shiporder.freeze_inventory_adjustment.plugin_text2') }}</div>
                    <div class="text-item">{{ (isStockBAndDistributionOpen ? '3.' : '2.') + $t('stock.shiporder.freeze_inventory_adjustment.plugin_text3') }}</div>
                    <div class="text-confirm">{{ $t('stock.shiporder.freeze_inventory_adjustment.confirm_text') }}</div>
                </div>
            </div>
            <div slot="footer" class="dialog-footer">
                <fx-button type="primary" @click="onConfirm" size="small">{{ $t('确定') }}</fx-button>
                <fx-button @click="visible = false" size="small">{{ $t('取消') }}</fx-button>
            </div>
        </fx-dialog>
    `,
    data() {
        return {
            visible: true,
            isLoading: false,
            isConfirmSuccessful: false,
        }
    },
    props: {
        // 是否开启 B 类库存且开启渠道库存
        isStockBAndDistributionOpen: Boolean,
    },
    methods: {
        handleDialogClose() {
            this.$emit('close', this.isConfirmSuccessful);
        },
        onConfirm() {
            this.isLoading = true;
            const param = {
                status: '1',
                update_time: Date.now().toString(), // 后端需要字符串类型的值
            };
            openModifyFreezeInventorySwitch(JSON.stringify(param)).then(res => {
                CRM.util.remind(1, $t('操作成功'));
                this.isConfirmSuccessful = true;
            }).catch(err => {
                console.error('coder log error info:', err);
                CRM.util.remind(3, $t('操作失败'));
            }).finally(() => {
                this.isLoading = false;
                this.visible = false
            });
        }
    }
});