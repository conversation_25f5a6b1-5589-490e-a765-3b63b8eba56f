/**
 * @description 采购业务联动-切换身份
 *
 * 用 TypeScript 写 PropType
 * https://frontendsociety.com/using-a-typescript-interfaces-and-types-as-a-prop-type-in-vuejs-508ab3f83480
 * https://stackoverflow.com/questions/67865654/what-is-a-proper-way-to-create-a-type-for-vue-props
 *
 */
import {
	getPurchaseReceivedWarehouseForDownstream,
	setAutoGenerateUpstreamDeliveryNoteConfig,
	setPurchaseReceivedWarehouse,
	UpstreamEnterpriseInfo,
	UpstreamEnterpriseInfos
} from "crm-modules/common/stock/api";
import { WareHouseOption } from "crm-modules/setting/shiporder/shiporder/model/api";
import { messageMixins } from "crm-modules/common/stock/mixins/messageMixins";

// 身份
export interface IdentityInfos {
	isUp: Boolean;
	isDown: Boolean;
}

// 上游入参
export interface UpParams {
	autoGenerateDeliveryConfig: "0" | "1";
}

// 下游入参
export interface DownParams {
	[key: string]: any;
	correspondingUpstreamList: UpstreamEnterpriseInfos;
	selectedCorrespondingUpstream: string;
}

// 上游企业列表
export interface UpStreamListSelectOption extends UpstreamEnterpriseInfo {
	label: string;
	value: string;
}

const template = `
			<fx-dialog
				class='interconnection-setting-dialog'
			  :visible.sync='visible'
			  ref='dialog1'
			  :append-to-body='true'
			  :title='$t("采购业务联动-参数设置")'
			  width='500px'
			  @closed='handleClosed'
			>
				<template >
					<fx-tabs v-model='activeName'>
    				<fx-tab-pane 
    					v-if='identityInfos.isUp'
    					:label='$t("我是上游")' 
    					name='up'
    				>
    					<div class='interconnection-setting-dialog-up'>
    						<div>{{ $t("当销售订单确认后") }}</div>
    						<fx-radio-group v-model='upData.autoGenerateDeliveryConfig'>
    							<fx-radio label='1'>
    								<span>{{ $t("在满足相关条件的前提下，系统自动创建发货单") }}</span>
									</fx-radio>
    							<fx-radio label='0'>
    								<span>{{ $t("系统不自动创建发货单，需要手动创建") }}</span>
									</fx-radio>
  							</fx-radio-group>
							</div>
						</fx-tab-pane>
    				<fx-tab-pane 
    					v-if='identityInfos.isDown'
    					:label="$t('我是下游')"
    					name='down'
    				>
    					<div class='interconnection-setting-dialog-down'>
    						<div class='interconnection-setting-dialog-down--item'>
    							<span>{{ $t("对应上游") }}</span>
									<fx-select
    								v-model="downData.selectedCorrespondingUpstream"
   									:options="downData.correspondingUpstreamList"
    							></fx-select>
								</div>
    						<div class='interconnection-setting-dialog-down--item'>
    							<span>{{ $t("采购入库默认仓库") }}</span>
    							<fx-select
    								v-model="downData.selectedWarehouse"
   									:options="downData.warehouseList"
    							></fx-select>
								</div>
							</div>
						</fx-tab-pane>
  				</fx-tabs>
				</template> 
				
				<span slot='footer' class='dialog-footer'>
    				<fx-button 
    					type='primary' 
    					@click='handleSave' 
    					size='small'
    				>{{$t('保存')}}</fx-button>
    				<fx-button 
    					@click='handleClosed' 
    					size='small'
    				>{{$t('取 消')}}</fx-button>
    			</span>
			</fx-dialog>
			`;

const InterconnectionSettingDialog = Vue.extend({
	template,

	mixins: [messageMixins],

	props: {
		// 身份
		identityInfos: {
			type: Object as () => IdentityInfos,
			validator: function (value) {
				return (
					value &&
					value.hasOwnProperty("isUp") &&
					typeof value.isUp === "boolean" &&
					value.hasOwnProperty("isDown") &&
					typeof value.isDown === "boolean"
				);
			}
		},

		// 上游
		upStreamParams: {
			type: Object as () => UpParams,
			default: () => {
				return {
					autoGenerateDeliveryConfig: 1
				};
			}
		},

		// 下游
		downStreamParams: {
			type: Object as () => DownParams,
			default: () => {
				return {
					correspondingUpstreamList: [],
					selectedCorrespondingUpstream: ""
				};
			}
		}
	},

	watch: {
		// 下游设置中: 切换上游企业，请求下游仓库
		async "downData.selectedCorrespondingUpstream"(val) {
			if (!val) {
				return;
			}
			try {
				const { value, wareHouse } = await this.getPurchaseReceivedWarehouse();
				this.downData.warehouseList = wareHouse;
				this.downData.selectedWarehouse = value;
			} catch (e) {
				this.downData.warehouseList = [];
				this.downData.selectedWarehouse = "";
				console.error(e);
			}
		}
	},

	data() {
		return {
			visible: true,
			activeName: "up",
			upData: {
				autoGenerateDeliveryConfig: "1"
			},
			downData: {
				correspondingUpstreamList: [],
				selectedCorrespondingUpstream: "",
				warehouseList: [],
				selectedWarehouse: ""
			}
		};
	},

	created() {
		this.propsToLocalData();
		this.activeName = this.identityInfos.isUp ? "up" : "down";
	},

	methods: {
		// props to data
		propsToLocalData() {
			this.downParamsPropToLocalData();
			this.upParamsToLocalData();
		},

		// props to data
		downParamsPropToLocalData() {
			const temp = JSON.parse(JSON.stringify(this.downStreamParams));
			this.downData.correspondingUpstreamList = temp.correspondingUpstreamList;
			this.downData.selectedCorrespondingUpstream =
				temp.selectedCorrespondingUpstream;
		},

		// props to data
		upParamsToLocalData() {
			const temp = JSON.parse(JSON.stringify(this.upStreamParams));
			this.upData.autoGenerateDeliveryConfig = temp.autoGenerateDeliveryConfig;
		},

		// 获取对应下游仓库
		async getPurchaseReceivedWarehouse() {
			try {
				// 选中
				let value: string;
				// 可选列表
				let wareHouse: WareHouseOption[];

				// const upEI = this.model.get('correspondingUpstream');
				const upEI = this.downData.selectedCorrespondingUpstream;
				// // @ts-ignore
				// const downEa =$.cookie('enterprise')
				const downEa = "";

				const res = await getPurchaseReceivedWarehouseForDownstream({
					upstreamTenantId: upEI
				});

				value = res.warehouseInfoList.find(
					(item) => item.predefine
				).purchaseReceivedWarehouseId;
				wareHouse = res.warehouseInfoList.map((item) => {
					return {
						value: item.purchaseReceivedWarehouseId,
						label: item.purchaseReceivedWarehouseName
					};
				});
				return Promise.resolve({
					value,
					wareHouse
				});
			} catch (e) {
				return Promise.reject(e);
			}
		},

		// 设置上游参数
		setPurchaseUpstreamParam(autoGenerateDeliveryConfig: 0 | 1) {
			return setAutoGenerateUpstreamDeliveryNoteConfig({
				autoGenerateConfig: autoGenerateDeliveryConfig
			});
		},

		// 设置下游仓库
		setPurchaseReceivedWarehouse(
			purchaseReceivedWarehouseId: string,
			upEI: string
		) {
			return setPurchaseReceivedWarehouse({
				upstreamTenantId: upEI,
				purchaseReceivedWarehouseId
			});
		},

		/**
		 * 保存
		 */
		handleSave: _.throttle(function () {
			const jobs = [];
			if (this.identityInfos.isUp) {
				jobs.push(
					this.setPurchaseUpstreamParam(
						this.upData.autoGenerateDeliveryConfig
					).then(() => {
						const value: UpParams = {
							autoGenerateDeliveryConfig: this.upData.autoGenerateDeliveryConfig
						};
						this.$emit("updateUpStreamParams", value);
					})
				);
			}
			if (this.identityInfos.isDown) {
				jobs.push(
					this.setPurchaseReceivedWarehouse(
						this.downData.selectedWarehouse,
						this.downData.selectedCorrespondingUpstream
					)
				);
			}

			Promise.all(jobs).then(this.success).catch(this.error);
		}, 1000),

		/**
		 * 关闭
		 */
		handleClosed() {
			this.$emit("cancel");
			this.$destroy();
		}
	}
});

export { InterconnectionSettingDialog };
