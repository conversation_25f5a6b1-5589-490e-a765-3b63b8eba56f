/**
 * @description k3c插件，开启对话框
 */
import {
	asyncSetConfigValue,
	commonGetConfigValueByKey,
	settingK3C,
} from "crm-modules/common/stock/api";

const template = `
			<fx-dialog
			  :visible.sync="visible"
			  ref="dialog1"
			  :append-to-body="true"
			  :title="$t('金蝶K3C对接插件')"
			  width="475px"
			  @closed="handleClosed"
			  class="k3c-plugin-dialog"
			>
				<template >
					<div v-loading="loading">
						<div class="sub-title">{{ $t("同步机制") }}</div>
						<div>
  						<fx-radio-group :value="syncType" is-vertical @input='changeSynchronizationConfirm'>
							<fx-radio label="1">{{ $t("同步库存") }}</fx-radio>
							<div class="radio-desc">{{ $t("直接同步库存数据到库存、批次库存等对象") }}</div>
  							<fx-radio label="2">{{ $t("同步库存明细") }}</fx-radio>
							<div class="radio-desc">{{ $t("同步数据到库存明细对象后，系统将自动进行计算并更新库存、批次库存等对象的数据") }}</div>
  						</fx-radio-group>
						</div>
						<div class="sub-title available-quantit-title">{{ $t("实时可用量") }}</div>
						<div class="available-quantit-wrap">
							<span class="available-quantit-text">{{ $t("通过接口，获取金蝶K3C库存及批次库存的实时可用量") }}</span>
							<fx-switch
								:value='instantAvailableQuantityOpen'
								@change='handleSetK3CInstantAvailableQuantity'
								inactive-value='1'
								active-value='2'
							size="micro"
							></fx-switch>
						</div>

					</div>
				</template>
				<span slot="footer" class="dialog-footer">
    				<fx-button
    					@click="handleClosed"
    					size="small"
    				>{{ $t("完成") }}</fx-button>
    			</span>
			</fx-dialog>
			`;

const K3CDialog = Vue.extend({
	template,

	props: {
		// "1": 同步库存 "2": 同步库存明细
		propSyncType: String,
	},

	data() {
		return {
			loading: false,
			visible: true,
			syncType: "1",
			instantAvailableQuantityOpen: "1",
		};
	},

	created() {
		this.syncType = this.propSyncType;
		this.init();
	},

	methods: {
		init() {
			this.getInstantAvailableQuantityOpen();
		},

		// 获取实时可用量
		getInstantAvailableQuantityOpen() {
			this.loading = true;
			return commonGetConfigValueByKey({
				key: "stock_instant_available_quantity_switch",
			})
				.then((res) => {
					this.instantAvailableQuantityOpen = res.value;
				})
				.finally(() => {
					this.loading = false;
				});
		},

		// 是否打开：实时可用量开关
		handleSetK3CInstantAvailableQuantity(value) {
			this.loading = true;
			asyncSetConfigValue()
				.circle(
					{
						key: "stock_instant_available_quantity_switch",
						value,
						token: "",
					},
					(data, stop) => {
						if (data.resultCode == 1) stop(data);
					}
				)
				.res((res) => {
					this.loading = false;
					this.instantAvailableQuantityOpen = value;
				})
				.catch((error) => {
					this.loading = false;
					FS.crmUtil.remind(3, error);
				});
		},

		// 切换同步机制前的确认
		changeSynchronizationConfirm(value) {
			const confirmText = $t(
				"切换同步机制，将会将现有库存和批次库存的数量全部置为0，确定要继续吗？"
			);
			const confirmDialog = FS.crmUtil.confirm(
				confirmText,
				$t("切换提示"),
				() => {
					confirmDialog.hide();
					this.handleSynchronizationMechanismInput(value);
				}
			);
		},

		// 切换同步机制
		handleSynchronizationMechanismInput: _.throttle(function (value) {
			this.loading = true;
			return settingK3C({
				kingDeeK3CSyncType: value,
			})
				.then(() => {
					this.syncType = value;
					this.$emit("updateKingDeeK3CSyncType", value);
				})
				.finally(() => {
					this.loading = false;
				});
		}, 1000),

		handleClosed() {
			this.$destroy();
		},
	},
});

export { K3CDialog };
