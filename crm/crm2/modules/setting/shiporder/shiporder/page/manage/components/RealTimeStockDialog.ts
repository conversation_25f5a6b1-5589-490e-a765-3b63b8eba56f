/**
 * @description 实时可用库存
 */

const template = `
			<fx-dialog
			  :visible.sync='visible'
			  ref='dialog1'
			  size='small'
			  :append-to-body='true'
			  :title="$t('stock.stock_manage.erp.text_1')"
			  class='rts-dialog k3c-plugin-dialog'
			>
				<div v-loading="loading">
				  <div style='color: #181C25'>
				  		{{$t('stock.shiporder.erp_real_time_inventory.title')}}
				  		<span class="fx-icon-question" @click="handleClickERPHelp"></span>
				  </div>
				  <fx-radio-group v-model='erp_real_time_inventory.type'>
							<fx-radio label="1" class='rts-dialog__radio'>{{ $t('stock.shiporder.erp_real_time_inventory.by_integrated_platform') }}</fx-radio>
							<div class="radio-desc" style='margin-bottom: 0'>{{ $t("stock.shiporder.erp_real_time_inventory.by_integrated_platform_describe_text") }}</div>
							<fx-radio label="2" class='rts-dialog__radio'>
								<span>{{ $t('stock.stock_manage.erp.text_2') }}</span>
								<a 
									v-show='erp_real_time_inventory.type==2&&!erp_real_time_inventory.functionApiName' 
									class='icon-tips' 
									@click='addFun'
								>{{$t('stock.stock_manage.erp.text_3')}}</a>
							</fx-radio>
					</fx-radio-group>
					<div class='rts-dialog__apl radio-desc'  
					     style='margin-left: 46px'
					     v-show='erp_real_time_inventory.type==2&&erp_real_time_inventory.functionApiName'>
					        <div  class='rts-dialog__apl__row'>
					          <div>{{$t('stock.stock_manage.erp.text_4')}}</div>
					          <div>
					            <span class='rts-dialog__apl__btn' @click='handleDelete'>X</span>
					          </div>
					        </div>
					        <div  class='rts-dialog__apl__row'>
					          <div>{{$t('stock.stock_manage.erp.text_5')}}</div>
					          <div>{{erp_real_time_inventory.functionApiName}}</div>
					        </div>
					        <div  class='rts-dialog__apl__row'>
					          <div>{{$t('stock.stock_manage.erp.text_6')}}</div>
					          <div>{{erp_real_time_inventory.functionDes}}</div>
					        </div>
					</div>
					<div style='margin-top: 24px;'>
						<div style='color: #181C25'>{{$t('stock.stock_manage.erp.text_7')}}</div>
						<fx-radio-group v-model='erp_real_time_inventory.dataProcessingType'>
							<fx-radio label="0" class='rts-dialog__radio'>{{ $t('stock.stock_manage.erp.text_8') }}</fx-radio>
							<fx-radio label="1" class='rts-dialog__radio'>{{ $t('stock.stock_manage.erp.text_9') }}</fx-radio>
						</fx-radio-group>
					</div>
				</div>
				<span slot='footer' class='dialog-footer'>
    				<fx-button 
    					type='primary' 
    					@click='handleConfirm' 
    					size='small'
    					:disabled="loading"
    				>{{$t('确 定')}}</fx-button>
    				<fx-button 
    					@click='handleClosed' 
    					size='small'
    					:disabled="loading"
    				>{{$t('取 消')}}</fx-button>
    			</span>
			</fx-dialog>
			`;

const RealTimeStockDialog = Vue.extend({
	template,

	props: {
		default_erp_real_time_inventory: Object,
	},

	data() {
		return {
			loading: false,
			visible: true,
			erp_real_time_inventory: {
				type: "1", // 0:开关关闭，1:通过集成平台， 2:通过APL函数
				functionApiName: "", // type为1时， 该项可不填， type为2时， 该项必填
				functionDes: "", // 函数描述
				dataProcessingType: "0" //"0" : 忽略匹配不到的库存数据，不做处理。 “1”：在CRM创建对应的库存数据，并更新实时库存
			},
		};
	},

	created() {
		this.erp_real_time_inventory = this.default_erp_real_time_inventory;
	},

	methods: {
		handleClickERPHelp() {
			const HELP_URL = 'https://help.fxiaoke.com/1969/7d85/a93b';
			window.open(HELP_URL, '_blank');
		},
		// 自定义函数调用插件
		getSdkHelper() {
			this.loading = true
			return new Promise((resolve) => {
				seajs.use("paas-function/sdk", (sdkHelper) => {
					this.loading = false
					resolve(sdkHelper);
				});
			});
		},

		addFun() {
			this.getSdkHelper().then((sdkHelper) => {
				sdkHelper.getControllerFunction({}, (res) => {
					if (res.status) {
						this.erp_real_time_inventory.functionApiName =
							res.data.function.api_name; // 自定义函数apiName
						this.erp_real_time_inventory.functionDes =
							res.data.function.remark; // 自定义函数描述
					} else {
						FS.crmUtil.remind(3, $t("操作失败"));
					}
				});
			});
		},

		handleDelete() {
			this.erp_real_time_inventory.functionApiName = "";
			this.erp_real_time_inventory.functionDes = " ";
		},

		handleEdit() {
			return this.addFun();
		},

		handleConfirm() {
			this.$emit("confirm", this.erp_real_time_inventory);
			this.handleClosed();
		},

		handleClosed() {
			this.$emit("close");
			this.$destroy();
		},
	},
});

export { RealTimeStockDialog };
