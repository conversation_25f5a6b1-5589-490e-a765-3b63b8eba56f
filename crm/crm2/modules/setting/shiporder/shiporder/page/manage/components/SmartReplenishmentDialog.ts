/**
 * @description k3c插件，开启对话框
 */
 import {
	setSmartSettings,
	getSmartSettings
} from "crm-modules/common/stock/api";

const template = `
<fx-dialog
size="big"
:visible.sync="dialogVisible"
:showFullscreenSwitch="true"
:append-to-body="true"
:close-on-click-outside="false"
:close-on-click-modal="false"
:title="$t('设置')"
v-loading="loading"
>
	<div class="smartReplenishment_div">
		<div class="smartReplenishment_illustrate">
			<span>{{$t("说明")}}</span><br />
			1.{{$t("打开“智能预估”开关，在新增/编辑采购订单时预置“智能预估”按钮")}}<br />
			2.{{$t("本次仅支持“采购订单”进行预估")}}<br />
			3.{{$t("采购预估默认设置为“按照固定公式预估”")}}<br />
			4.{{$t("取值时间默认范围：取最近“30天”相关数据")}}<br />
			5.{{$t("建议采购量默认公式：销售总数量-库存余量-已锁定的库存量")}}<br />
		</div>

		<div class="smartReplenishment_type">
			<span class="title">{{$t("智能预估方式")}}:</span>
			<fx-radio v-model="radio" label="1">{{$t("按照预置公式")}}</fx-radio>
			<fx-radio v-model="radio" label="2">{{$t("按照函数")}}</fx-radio>
			<div class="APLDiv" v-if="radio=='2'">
				<span v-if="radio=='2' && !functionName" class="spl_span" @click="addAPL">{{$t("选择APL代码")}}</span>
				<div v-if="radio=='2' && functionName" class="APL-detail">
					<div class="APL-name">
						<div>{{ $t("采购订单产品智能预估函数") + functionName }}</div>
						<div>
							<span @click="closeAPL">{{ $t("删除") }}</span>
							<span @click="addAPL2()">{{$t("选择APL代码")}}</span>
						</div>
					</div>
					<div class="APL-dec">{{ $t("函数描述：") + functionDesc }}</div>
				</div>
			</div>
		</div>
		<!-- 预置公式 -->
		<div class="smartReplenishment_default" v-if="radio=='1'">
			<span class="left_name">{{$t("预置公式")}}</span>
			<span>{{$t("建议采购量")}}<fx-tooltip class="item" effect="dark" :content="$t('通过预置公式计算后得出的结果，将填入采购数量字段')" placement="top-start">
			<i class="shiporder-tip_icon">?</i></fx-tooltip>&nbsp;&nbsp;=&nbsp;&nbsp;{{$t("销售总数量")}}<fx-tooltip class="item" effect="dark" :content="$t('所选时间范围内的销售订单产品数量之和')" placement="top-start">
			<i class="shiporder-tip_icon">?</i></fx-tooltip>&nbsp;&nbsp;-&nbsp;&nbsp;{{$t("可用库存量")}}<fx-tooltip class="item" effect="dark" :content="$t('当前产品的可用库存数量之和')" placement="top-start">
			<i class="shiporder-tip_icon">?</i></fx-tooltip></span>
			<span>&nbsp;&nbsp;-&nbsp;&nbsp;<fx-checkbox v-model="onRoadchecked">{{$t("在途采购量")}}<fx-tooltip class="item" effect="dark" :content="$t('所选时间范围内的采购订单产品的未入库数量之和')" placement="top-start">
			<i class="shiporder-tip_icon">?</i></fx-tooltip></fx-checkbox></span>
			<span>&nbsp;&nbsp;-&nbsp;&nbsp;<fx-checkbox v-model="safeStockChecked">{{$t("安全库存量")}}<fx-tooltip class="item" effect="dark" :content="$t('产品在产品档案中设置的”安全库存“数量')" placement="top-start">
			<i class="shiporder-tip_icon">?</i></fx-tooltip></fx-checkbox></span>
			<div class="line"></div>
			<span class="left_name">{{$t("取值时间范围")}}:</span><span>{{$t("取最近")}}<fx-input class="smartReplenishment_day" :decimal-places="0" :integer-places="2" size="mini" ref="number" v-model="defaultDay"></fx-input>{{$t("天(不含今天)的销售量和在途数采购量")}}</span>
		</div>
	</div>
	<span slot="footer" class="dialog-footer">
		<fx-button v-if="radio == 2 && !functionName" disabled type="primary" @click="handleConfirm" size="small">{{$t("确定")}}</fx-button>
		<fx-button v-else type="primary" @click="handleConfirm" size="small">{{$t("确定")}}</fx-button>
		<fx-button @click="handleClosed" size="small">{{$t("取消")}}</fx-button>
	</span>
</fx-dialog>`;
const SmartReplenishmentDialog = Vue.extend({
	template,
	// intelligentReplenishmentSwitch 2 开启 1 未开启 3 关闭
	props: {
		intelligentReplenishmentSwitch: String
	},

	data() {
		return {
			loading: true,
			dialogVisible: true,
			radio: '1', // 1 预置公式 2 按照APL函数
			onRoadchecked: false, // 在途采购量
			safeStockChecked: false, // 安全库存量
			defaultDay: '', // 时间范围1-30天
			functionName: '',// 自定义函数apiName
			functionDesc: ''// 自定义函数描述
		};
	},
	created() {
		this.getSmartSetting();
	},
	methods: {
		handleConfirm() {
			// if (this.isDisabled) return
			// this.$emit("confirm");
			// this.$destroy();
			this.setSmartSetting();
		},
		handleClosed() {
			this.$destroy();
		},
		// 获取智能补货设置
		getSmartSetting(){
			getSmartSettings().then((res: any)=>{
				console.log('`````````````````````````')
				console.log(res);
				if(res.isSuccess){
					let intelligentPrediction = res.data.intelligentPrediction;
					this.radio = intelligentPrediction.type+''; // 1 预置公式 2 按照APL函数
					if(intelligentPrediction.type == 1){
						this.onRoadchecked = intelligentPrediction.inPurchase; // 在途采购量
						this.safeStockChecked = intelligentPrediction.safetyStock; // 安全库存量
						this.defaultDay = intelligentPrediction.timeFrame; // 时间范围1-30天
					}else{
						this.functionName = intelligentPrediction.functionName; // 自定义函数apiName
						this.functionDesc = intelligentPrediction.functionDesc; // 自定义函数描述
					}
				}
				this.loading = false
			})
		},
		// 设置智能补货设置
		setSmartSetting(){
			if(this.radio == 2 && !this.functionName){
				FS.crmUtil.remind(3, $t('stock.crm.setting.shiporder.please_set_the_function_first'));
				return
			}
			if(this.radio == 1 && !this.defaultDay){
				FS.crmUtil.remind(3, $t('stock.crm.setting.shiporder.please_set_a_time_range'));
				return
			}
			let _intelligentPrediction = {};
			if(this.radio == 2){
				_intelligentPrediction = {
					type: this.radio,
					functionName: this.functionName, //自定义函数名称
					functionDesc: this.functionDesc //自定义函数描述
				}
			}else{
				_intelligentPrediction = {
					type: this.radio,
					safetyStock: this.safeStockChecked, // 安全库存量 true 选中 false 未选中
					inPurchase: this.onRoadchecked, // 在途采购量true 选中 false 未选中
					timeFrame: this.defaultDay, // 时间设置 1-30 的正整数
				}
			}
			let _json = {
				intelligentReplenishmentSwitch: this.intelligentReplenishmentSwitch, // 2 开启 1 未开启 3 关闭
				intelligentPrediction: _intelligentPrediction
			}
			console.log(_json)

			this.loading = true
			setSmartSettings.circle(_json,(data,stop)=>{
				if(data.resultCode == 1) stop(data)
			}).res(res=>{
				this.loading = false
				FS.crmUtil.remind(1, $t('操作成功'));
				this.$destroy();
			}).catch((err)=>{
				FS.crmUtil.remind(3, err);
				this.loading = false
			})
		},
		// 关闭当前自定义函数
		closeAPL(){
			this.functionName = ''; //自定义函数名称
			this.functionDesc = ''; //自定义函数描述
		},
		// 打开对应的自定义插件 或者 打开函数新建页面 1 新增  2 更新 getStockReplenishmentFunction  update
		addAPL(arg){
			console.log('``````````````',arg)
			let type = this.functionName?2:1;
			let _json = {object_api_name: 'PurchaseOrderObj'};
			if(type==2){
				_json['api_name'] = this.functionName;
			}
			console.log(_json)
			const me = this;
			me.getSdkHelper().then((sdkHelper) => {
				if(type == 1){
					sdkHelper.getStockReplenishmentFunction( _json ,(res) => {
						console.log('!!!!!!!!!!!!!!!!!!!!',res)
						if(res.status){
							this.functionName = res.data.function.api_name;// 自定义函数apiName
							this.functionDesc = res.data.function.remark;// 自定义函数描述
						}else{
							FS.crmUtil.remind(3, $t('操作失败'));
						}
					});
				}else{
					sdkHelper.update( _json ,(res) => {
						
					});
				}
				
			});
		},
		addAPL2(){
			let _json = {object_api_name: 'PurchaseOrderObj'};
			const me = this;
			me.getSdkHelper().then((sdkHelper) => {
				sdkHelper.getStockReplenishmentFunction( _json ,(res) => {
					console.log('4444444444444444',res)
					if(res.status){
						this.functionName = res.data.function.api_name;// 自定义函数apiName
						this.functionDesc = res.data.function.remark;// 自定义函数描述
					}else{
						FS.crmUtil.remind(3, $t('操作失败'));
					}
				});
			});
		},
		// 自定义函数调用插件
		getSdkHelper () {
			return new Promise((resolve) => {
				seajs.use('paas-function/sdk', (sdkHelper) => {
				resolve(sdkHelper);
				});
			});
		},
	}
});

export { SmartReplenishmentDialog };
