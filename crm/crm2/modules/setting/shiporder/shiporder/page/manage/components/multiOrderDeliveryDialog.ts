/**
 * @description 多订单发货开启面板
 */

const template = `
			<fx-dialog
				:visible.sync='visible'
			  :append-to-body='true'
			  :title="$t('开启多订单发货')"
			  width='640px'
			  @closed='handleClosed'
				class='multiOrderDeliveryDialog'
			>
				<template >
					<p>{{ $t('开启多订单发货，会对现有的部分逻辑做出一些调整，请确保您已知晓以下可能产生的影响：') }}</p>
					<p>{{ $t('1、开启多订单发货后，新建/编辑时，销售订单字段会在发货单主对象上隐藏;') }}</p>
					<p>{{ $t('2、如果一张发货单对多个订单进行了发货，发货单主对象上的销售订单字段将不会被赋值，可能会影响原有的报表分析;') }}</p>
					<p>{{ $t('3、开启后，如果想要关闭多订单发货，则需要先将对应了多张销售订单的发货单删除之后，才能够关闭。') }}</p>
					<p>
					<input type="checkbox" name="checkbox" v-model:value="isAgree" :disabled="isDisabled"/>
					<span v-if='time'>{{time}} s </span>{{ $t('我已知晓开启多订单发货后的调整，并确定这些调整不会影响订单业务的正常流转。') }}
					</p>
				</template>
				<span slot='footer' class='dialog-footer'>
    				<fx-button
    					type='primary'
							:disabled="isDisabled ||!isAgree"
    					@click='handleConfirm'
    					size='small'
    				>{{$t('确 定')}}</fx-button>
    				<fx-button
    					@click='handleClosed'
    					size='small'
    				>{{$t('取 消')}}</fx-button>
    			</span>
			</fx-dialog>
			`;

const multiOrderDeliveryOpen = Vue.extend({
	template,

	data() {
		return {
			isAgree: false,
			visible: true,
			time: 29,
			isDisabled: true
		};
	},

	created() {
		let timer = setInterval(() => {
			this.time--
			if (this.time <= 0) {
				clearInterval(timer)
				this.isDisabled = false
			}
		}, 1000)
	},

	methods: {
		handleConfirm() {
			if (this.isDisabled) return
			this.$emit("confirm");
			this.$destroy();
		},
		handleClosed() {
			this.$destroy();
		},
	},
});




/**
 * @description 多订单发货关闭面板
 */

const template2 = `
	<fx-dialog
	:visible.sync='visible'
	:append-to-body='true'
	:title="$t('关闭多订单发货')"
	width='630px'
	@closed='handleClosed'
	class='multiOrderDeliveryDialog'
>
	<template >
    <p>{{ $t('关闭多订单发货之前，请确保您已知晓以下前提条件：') }}</p>
    <p>{{ $t('如果系统中存在关联多个销售订单的发货单，则必须先将这些发货单作废删除后，才能够关闭多订单发货功能。') }}</p>
    <p class="inquire-list-btn"><a :href=href target="_blank">{{ $t('点击查询关联多个销售订单的发货单列表') }}</a></p>
    <p>
    <input type="checkbox" name="checkbox" v-model:value="isAgree" :disabled="isDisabled"/>
    <span>{{time}} s </span>{{ $t('我已知晓开启多订单发货后的调整，并确定这些调整不会影响订单业务的正常流转。') }}
    </p>
	</template>
	<span slot='footer' class='dialog-footer'>
			<fx-button
				type='primary'
				:disabled="isDisabled ||!isAgree"
				@click='handleConfirm'
				size='small'
			>{{$t('确 定')}}</fx-button>
			<fx-button
				@click='handleClosed'
				size='small'
			>{{$t('取 消')}}</fx-button>
		</span>
</fx-dialog>
 `;

const multiOrderDeliveryClose = Vue.extend({
	template: template2,

	computed: {
		href() {
			return window.location.pathname + "#crm/list/=/DeliveryNoteObj/filteremptysalesorderid";
		}
	},

	data() {
		return {
			isAgree: false,
			visible: true,
			time: 29,
			isDisabled: true,
			dialogVisible2:false
		};
	},

	created() {
		let timer = setInterval(() => {
			this.time--
			if (this.time <= 0) {
				clearInterval(timer)
				this.isDisabled = false
			}
		}, 1000)
	},

	methods: {
		handleConfirm() {
			if (this.isDisabled) return
			this.$emit("confirm");
			this.$destroy();
		},
		handleClosed() {
			this.$destroy();
		}
	},
});


/**
 * @description CPQ组合发货，关闭面板。
 *
 * 多语词条：
 * 	stock.stock_manage.close_cpq_delivery	关闭CPQ组合发货
 *
 */

const template3 = `
	<fx-dialog
	:visible.sync='visible'
	:append-to-body='true'
	:title="$t('stock.stock_manage.close_cpq_delivery')"
	width='630px'
	@closed='handleClosed'
	class='multiOrderDeliveryDialog'
>
	<template >
		<p>{{ $t('CPQ组合发货插件属于已下架、且不再维护的插件，推荐关闭。') }}</p>
		<p>{{ $t('该插件关闭后，不可再次开启，后续插件管理页面也无法再次查询到该插件的信息。') }}</p>
		<p>{{ $t('确定要关闭CPQ组合发货插件吗？') }}</p>
		<p>
		<input type="checkbox" name="checkbox" v-model:value="isAgree" :disabled="isDisabled"/>
		<span v-if='time'>{{time}} s </span>{{ $t('我已知晓CPQ组合发货插件关闭后无法再次开启，并确定关闭该插件不会影响现有的业务。') }}
		</p>
	</template>
	<span slot='footer' class='dialog-footer'>
			<fx-button
				type='primary'
				:disabled="isDisabled ||!isAgree"
				@click='handleConfirm'
				size='small'
			>{{$t('确 定')}}</fx-button>
			<fx-button
				@click='handleClosed'
				size='small'
			>{{$t('取 消')}}</fx-button>
		</span>
</fx-dialog>
 `;

const cpqDeliveryClose = Vue.extend({
	template: template3,
	data() {
		return {
			isAgree: false,
			visible: true,
			time: 29,
			isDisabled: true,
			dialogVisible2:false
		};
	},

	created() {
		let timer = setInterval(() => {
			this.time--
			if (this.time <= 0) {
				clearInterval(timer)
				this.isDisabled = false
			}
		}, 1000)
	},

	methods: {
		handleConfirm() {
			if (this.isDisabled) return
			this.$emit("confirm");
			this.$destroy();
		},
		handleClosed() {
			this.$destroy();
		}
	},
});



export { multiOrderDeliveryOpen, multiOrderDeliveryClose, cpqDeliveryClose };
