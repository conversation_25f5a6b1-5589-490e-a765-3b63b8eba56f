/**
 * @description 未开启库存，选择想开启库存类型
 *
 * 上下游切换设计稿  https://www.figma.com/file/wjbaGzu1iSgJSoCfythakO/%E8%BF%9B%E9%94%80%E5%AD%98%E7%AE%A1%E7%90%86?node-id=0%3A1
 *
 * TODO 国际语言适配
 * TODO 逐步迁移ts 2021.9.14
 */

import { K3CDialog } from 'crm-modules/setting/shiporder/shiporder/page/manage/components/K3CDialog';
import { CostManagementDialog } from 'crm-modules/setting/shiporder/shiporder/page/manage/components/CostManagement';
import { BatchSnDialog } from 'crm-modules/setting/shiporder/shiporder/page/manage/components/BatchSnDialog';
import { RealTimeStockDialog } from 'crm-modules/setting/shiporder/shiporder/page/manage/components/RealTimeStockDialog';
import {
  multiOrderDeliveryOpen,
  multiOrderDeliveryClose,
  cpqDeliveryClose,
} from 'crm-modules/setting/shiporder/shiporder/page/manage/components/multiOrderDeliveryDialog';
import { CustomFunctionGuide } from 'crm-modules/setting/shiporder/shiporder/page/manage/components/CustomFunctionGuide';
import { CustomFunctionGuideStock } from 'crm-modules/setting/shiporder/shiporder/page/manage/components/CustomFunctionGuideStock';
import {
  AutoConfirmReceiptDialog,
  AutoConfrimReceiptValue,
} from 'crm-modules/setting/shiporder/shiporder/page/manage/components/AutoConfirmReceiptDialog';
import {
  DownParams,
  InterconnectionSettingDialog,
  UpParams,
  UpStreamListSelectOption,
} from 'crm-modules/setting/shiporder/shiporder/page/manage/components/InterconnectionSettingDialog';
import { SmartReplenishmentDialog } from 'crm-modules/setting/shiporder/shiporder/page/manage/components/SmartReplenishmentDialog';
import { ExchangeSettingDialog } from "crm-modules/setting/shiporder/shiporder/page/manage/components/ExchangeSettingDialog"
import { DeliveryNoteReturnSetting } from "crm-modules/setting/shiporder/shiporder/page/manage/components/DeliveryNoteReturnSetting"
import { FreezeInventoryAdjustment } from "crm-modules/setting/shiporder/shiporder/page/manage/components/FreezeInventoryAdjustment"
import {
	setAutoConfirmReceipt,
	getPluginConfig,
	getPluginsConfigValueByKey,
	getDownstreamQueryRelatedUpstream,
	setNegativeInventorySwitch,
	getSparePartConfig,
	setSparePart, //接口async
	enableCostAdjustment, //接口async
	enableBatch, //接口async
	isAllowSetStockConf,
	asyncSetConfigValue,
	enableStockSaleMulti,
	asyncEnableK3C,
	enableParentWareHouse,
	commonGetConfigValueByKeys,
	setSmartSettings,
	getSmartSettings,
    setDeliveryNoteSupportSignatures,
    enableExchangePlugin,
  getGrayForCurtAvailableStock,
  set_erp_real_time_inventory
} from 'crm-modules/common/stock/api';

const Manage = Backbone.View.extend({
  template: require('./tpl-html'),
  dialogReceive: require('./dialogs/receive-html'),
  cardTpl: require('./cardTpl-html'),

  // switch 开关开启的点击事件
  events: {
    'click .j-switch-autoreceive': '_handleSwitchAutoReceive',
    'click .j-switch-express': '_handleSwitchExpress',
    'click .j-setting-receive': '_handleSettingReceive',
    'click .j-setting-batch': '_handleSettingBatch',
    'click .j-switch-batch': '_handleEnableBatch',
    'click .j-multi-switch': '_handleMultiSwitch',
    'click .j-switch-warehouse-position': '_handleEnableWarehousePosition',
    'click .j-switch-cpq-delivery-way': '_cpqDeliveryWayChangeHandle',
    'click .j-switch-cost-adjustment': '_handleCostAdjustmentSwitch',
    'click .j-switch-delivery-note-return': '_handleSwitchDeliveryNoteReturn',
    'click .j-switch-return': '_handleSwitchReturn',
    'click .j-switch-exchange': '_handleSwitchExchange',
    'click .j-switch-action': 'handleActionSwitch',
    'click .j-switch-k3c': '_handleK3CSwtich',
    'click .j-setting-k3c': '_handleK3CSetting',
    'click .j-setting-cost-management': '_handleKCostManagementSetting',
    'click .j-cf-config': '_handleCustomFunctionConfigClick',
    'click .j-switch-negative-inventory': '_handleNegativeInventory',
    'click .j-setting-interconnection': '_handleSettingInterconnection',
    'click .j-switch-smart': '_handleSmartSwtich',
    'click .j-setting-smart': '_handleSmartSetting',
    'click .j-switch-distribution-stock': '_handleSwitchDistributionStock', // 开启渠道库存管理
    'click .j-unique-product-code-management': '_handleUniqueProductCodeManagement', // 开启 快消行业商品条码
    'click .j-sales-order-ato-pto-switch': '_handleSalesOrderAtoPtoSwitch', // 开启 销售订单ATO&PTO组合发货
    'click .j-setting-exchange': '_handleExchangeSetting', // 换货插件-参数设置
    'click .j-switch-realtimestock': '_handleRealTimeStockSwitch',
    'click .j-setting-realtimestock': '_handleSettingRealTimeStock', // 换货插件-参数设置
    'click .j-setting-delivery_return_product_add_mode': '_handleDeliveryNoteReturnSetting', // 基于发货单退货插件-参数设置
    "click .j-freeze-inventory-adjustment-switch": "handleFreezeInventoryAdjustment", // 冻结库存调整
  },

  handleFreezeInventoryAdjustment() {
    if (this.model.get('order_freeze_inventory_adjustment') == '1') {
      return;
    }

    // AT0&PTO 插件与“冻结库存调整”互斥
    const sales_order_ato_pto_switch = this.model.get('sales_order_ato_pto_switch');
    if (sales_order_ato_pto_switch === '1') {
      FS.crmUtil.remind(3, $t('stock.stock_manage.ato_pto.text_1')); // 已开启ATO&PTO功能，暂不支持开启冻结库存调整
      return;
    }

    const el = $('<div id="app"></div>');
    $('body').append(el);

    let instance = new FreezeInventoryAdjustment({
      el: el[0],
      propsData: {
        isStockBAndDistributionOpen: this.model.get('isForErp') == true && this.model.get('distribution_stock_switch') == '2',
      }
    });
    instance.$on('close', isConfirmSuccessful => {
      if (isConfirmSuccessful) {
        this.model.set({
          order_freeze_inventory_adjustment: '1',
        });
        $('.j-freeze-inventory-adjustment-switch').addClass('oprate-btn_switch--on oprate-btn_switch--disabled');
      }
      instance.$destroy();
      instance = null;
    });
  },

  _handleDeliveryNoteReturnSetting() {
    const el = $('<div id="app"></div>');
    $('body').append(el);
    new DeliveryNoteReturnSetting({
      el: el[0],
      propsData: {
        isATOPTOOpen: this.model.get('sales_order_ato_pto_switch') === '1',
      }
    });
  },

  initialize: function () {
    this.model.isShowStockLoading();
  },

  // 切换到二级tab时触发。点击 crm2/modules/setting/shiporder/shiporder/view/view.js 里的 tab 切换时，会触发 当前 render 方法
  render: function () {
    this.initPluginConfigs().then(() => {
      this.renderTemplate();
    });
  },

  renderTemplate() {
    var data = this.model.toJSON();
    this.$el && this.$el.html(this.template(data));
  },

  initPluginConfigs() {
    const tasks = [
      this.getPluginConfig(),
	    this.getCPQDeliveryWay(),
      this.getStockConfigData(),
      this.getPluginsGray(),
      this._getSmartSettings(),
      this.getGrayForCurtAvailableStock()
    ];
    return Promise.all(tasks);
  },

  // 是否打开实时可用库存
  getGrayForCurtAvailableStock: function () {
    return getGrayForCurtAvailableStock().then(res=>{
      this.model.set({
        isShowRealTimeAvailableStock: res.isShowRealTimeAvailableStock,
      });
    })
  },

  // 插件灰度接口。获取多订单发货 和 备件灰度控制等
  getPluginsGray() {
    // const断言 声明为只读元素
    // https://www.typescriptlang.org/docs/handbook/release-notes/typescript-3-4.html#const-assertions
    const configTypes = [
      'delivery_note_multi_order_deliver_switch',
      'negative_inventory_allowed_plugin_switch',
      'open_multi_sn',
      'intelligent_replenishment_switch',
      'dht_serial_number_param_switch',
      'erp_real_time_inventory',
      'distribution_stock_switch', // "渠道库存管理"插件
	  'unique_product_code_management', // “快消行业商品条码”插件（别名：“一物一码”）
	  'sales_order_ato_pto_switch', // 销售订单ATO&PTO组合发货
    ] as const;
    return isAllowSetStockConf({ configTypes })
      .then(res => {
        this.model.set({
          hasMultiOrderDeliveryGray: res.delivery_note_multi_order_deliver_switch,
          negative_inventory_allowed_plugin_switch: res.negative_inventory_allowed_plugin_switch,
          has_open_multi_sn: res.open_multi_sn,
          intelligent_replenishment_switch: res.intelligent_replenishment_switch,
          dht_serial_number_param_switch: res.dht_serial_number_param_switch,
          erp_real_time_inventory_gray: res.erp_real_time_inventory,
          // 渠道库存管理 灰度开关
          distribution_stock_switch_gray: res.distribution_stock_switch,
		  // “快消行业商品条码”插件，控制是否显示的开关
		  unique_product_code_management_gray: res.unique_product_code_management,
		  // 销售订单ATO&PTO组合发货
		  sales_order_ato_pto_switch_gray: res.sales_order_ato_pto_switch,
        });
      })
      .catch(e => {
        console.log('[GRAY] ERROR：', e);
      });
  },

  // 获取库存配置开启信息
  getStockConfigData() {
    return commonGetConfigValueByKeys({ keys: [], isAllConfig: true })
      .then(res => {
        let erp_real_time_inventory =  {
          'type': '0', // 0:开关关闭，1:通过集成平台， 2:通过APL函数
          'functionApiName': '', // type为1时， 该项可不填， type为2时， 该项必填
          'functionDes': '', // 函数描述
          'dataProcessingType':'0' //"0" : 忽略匹配不到的库存数据，不做处理。 “1”：在CRM创建对应的库存数据，并更新实时库存
        }
        if (res.erp_real_time_inventory) {
          erp_real_time_inventory = JSON.parse(res.erp_real_time_inventory);
        }

        // 销售订单冻结库存调整
        let order_freeze_inventory_adjustment = {
          status: '0',
          update_time: ''
        };
        if (res.order_freeze_inventory_adjustment) {
          order_freeze_inventory_adjustment = JSON.parse(res.order_freeze_inventory_adjustment);
        }

        this.model.set({
          // 销售订单冻结库存调整: 0-关闭 1-开启
          order_freeze_inventory_adjustment: order_freeze_inventory_adjustment.status,
          // 基于发货单退货模式：1 -> 先选发货单，再选发货单产品；2 -> 直接从发货单产品退货
          delivery_return_product_add_mode: res.delivery_return_product_add_mode,
          // 多订单
          multiOrderDeliverySwitch: +res.delivery_note_multi_order_deliver_switch,
          // CPQ组合发货：1 已开启；2 关闭
          delivery_note_cpq_deliver_switch: res.delivery_note_cpq_deliver_switch,
          // 发货单退货
          delivery_note_returned_goods_switch: res.delivery_note_returned_goods_switch,
          // 无源单退货
          non_note_returned_goods_switch: res.non_note_returned_goods_switch,
          // 换货
          returned_goods_exchange_switch: res.returned_goods_exchange_switch,
          // 渠道库存管理 key
          distribution_stock_switch: res.distribution_stock_switch,
          // 实时库存
          erp_real_time_inventory,
		  // 一物一码 key：1 关闭；2 已开启
		  unique_product_code_management: res.unique_product_code_management,
		  // 销售订单ATO&PTO组合发货：1 已开启；2 关闭
		  sales_order_ato_pto_switch: res.sales_order_ato_pto_switch,
       // 成本管理
       cost_management_calculate: res.cost_management_calculate,
		  // 快消CPQ插件：1 已经开启；非 1 关闭
		//   fmcg_cpq: res.cpq,
        });
				return res
      })
			// 发货单确认收货配置
			.then(res=>{
				// 自动确认收货
				const  autoR = res.delivery_note_automatic_receiving_config && JSON.parse(res.delivery_note_automatic_receiving_config);
				this.model.set({
					daysAfterShipment: autoR.daysAfterShipment,
					autoReceiveStatus: autoR.status,
					autoConfirmReceiptType: autoR.type,
				});
				return res;
			})
      .catch(e => {
        console.log('ERROR', e);
      });
  },

  // 获取CPQ组合发货控制
  getCPQDeliveryWay() {
    return new Promise((resolve, reject) => {
      this.model.getCPQDeliveryWay().then(
        data => {
          this.model.set(data);
          resolve(void 0);
        },
        error => {
          console.log('ERROR：', error);
          resolve(void 0);
        }
      );
    });
  },

  // 获取插件信息
  getPluginConfig() {
    return getPluginConfig()
      .then(data => {
        this.model.set({
          isOpenWareHousePosition: +data.isOpenWarePosition === 1,
          expressFuncCode: data.deliveryNoteExpressFuncType,
          batchSNSwitch: data.batchSnConfigInfo && data.batchSnConfigInfo.batchSNSwitch,
          willExpireStockWarningType: data.batchSnConfigInfo && data.batchSnConfigInfo.willExpireStockWarningType,
          multiSwitch: data.stockSalesModuleMultipleUnitOptimizeSwitch,
          displayAdvancedLogisticsQuery: data.displayAdvancedLogisticsQuery,
          costAdjustmentEnableSwitch: data.costAdjustmentEnableSwitch,
          kingDeeK3CSyncPluginSwitch: data.kingDeeK3CSyncPluginSwitch,
          kingDeeK3CSyncType: data.kingDeeK3CSyncType,
          isUpstreamEnterprise: data.isUpstreamEnterprise,
          isDownstreamEnterprise: data.isDownstreamEnterprise,
          upstreamDisplayStockInterconnection: data.upstreamDisplayStockInterconnection,
          downstreamDisplayStockInterconnection: data.downstreamDisplayStockInterconnection,
          purchaseReceivedWarehouseName: data.purchaseReceivedWarehouseName,
          autoGenerateDeliveryConfig: data.autoGenerateDeliveryConfig,
          negativeInventorySwitch: data.negativeInventorySwitch,
        });
        // 没开互联两个都为false, currentIdentity为none
        if (data.isUpstreamEnterprise) {
          this.model.set({
            currentIdentity: 'up',
          });
        } else if (data.isDownstreamEnterprise) {
          this.model.set({
            currentIdentity: 'down',
          });
        }
        return data;
      })
      .then(data => {
        if (data.isDownstreamEnterprise) {
          return getDownstreamQueryRelatedUpstream().then(res => {
            this.model.set({
              correspondingUpstreamList: res.upstreamEnterpriseInfos,
              correspondingUpstream: res.upstreamEnterpriseInfos[0].upstreamTenantId,
            });
          });
        }
      })
  },

  _renderBatch: function () {
    var me = this;
    this.model.fetchBatch(function (data) {
      $('.j-card-batch').html(
        me.cardTpl({
          title: '批次与序列号',
          content: '开启批次与序列号管理',
          isOn: data.batchSNSwitch == 2,
          disabled: data.batchSNSwitch == 2,
          settingTriger: 'j-setting-batch',
          switchTriger: 'j-switch-batch',
        })
      );
    });
  },

  _renderReceive: function () {
    var me = this;
    this.model.getReceivingInfo(function (data) {
      var daysAfterShipment = data.daysAfterShipment || 7;
      $('.j-card-receive').html(
        me.cardTpl({
          title: '自动确认收货',
          content: '当发货单确认后，超过 ' + daysAfterShipment + ' 天时，发货单会自动确认收货',
          isOn: data.autoReceiveStatus == 1,
          settingTriger: 'j-setting-receive',
          switchTriger: 'j-switch-autoreceive',
        })
      );
    });
  },

  _handleSwitch: function (e) {
    var $target = $(e.currentTarget);
    if ($target.hasClass('oprate-btn_switch--on')) {
      $target.removeClass('oprate-btn_switch--on');
    } else {
      $target.addClass('oprate-btn_switch--on');
    }
  },

  _handleSwitchAutoReceive: function (e) {
    setAutoConfirmReceipt({
      type: this.model.get('autoConfirmReceiptType'),
      daysAfterShipment: this.model.get('daysAfterShipment'),
      status: this.model.get('autoReceiveStatus') === 1 ? 2 : 1,
    }).then(() => {
      FS.crmUtil.remind(1, $t('操作成功'));
      this.model.set('autoReceiveStatus', this.model.get('autoReceiveStatus') === 1 ? 2 : 1);
      this.renderTemplate();
    });
  },

  _handleSwitchExpress: function (e) {
    var value = $(e.currentTarget).hasClass('oprate-btn_switch--on') ? 1 : 2;
    this.model.set('expressFuncCode', value);
    this.model.setExpressFun(value);
    this._handleSwitch(e);
  },

  _handleSettingReceive: function (e) {
    const el = $('<div></div>');
    $('body').append(el);
    const dialog = new AutoConfirmReceiptDialog({
      el: el[0],
      propsData: {
        defaultValue: this.model.get('autoConfirmReceiptType'),
        defaultDays: this.model.get('daysAfterShipment'),
			},
    });

    dialog.$on('confirm', (value: AutoConfrimReceiptValue) => {
			this.model.set({
				daysAfterShipment: value.days,
				autoConfirmReceiptType: value.value,
			});
			const promises = []
			promises.push(setAutoConfirmReceipt({
				type: value.value,
				daysAfterShipment: value.days,
				status: 1,
			}))
			if (value.hasOpenSign) {
				promises.push(
					setDeliveryNoteSupportSignatures({
						status: value.SSstatus,
						// 0:不选择，1：发货单外部负责人，2：收货人
						upStreamSigner: value.SSupStreamSigner,
						// 0:不选择，1：发货单外部负责人，2：收货人
						downStreamSigner: value.SSdownStreamSigner,
						// 打印模板id
						printTemplateId: value.SSprintTemplateId,
						// 电子签厂商
						signAppType: value.SSsignAppType,
					})
				);
			}
			Promise.all(promises)
				.then(() => {
					FS.crmUtil.remind(1, $t("操作成功"));
					this.renderTemplate();
				})
				.catch((e) => {
					FS.crmUtil.remind(3, $t(e));
				});
    });

    dialog.$on('close', (value: AutoConfrimReceiptValue) => {
      this.renderTemplate();
    });
  },

  _handleSettingBatch: function () {
    const el = $('<div id="app"></div>');
    $('body').append(el);
    const batchSnDialog = new BatchSnDialog({
      el: el[0],
      propsData: {
        propWillExpireStockWarningType: this.model.get('willExpireStockWarningType'),
        has_open_multi_sn: this.model.get('has_open_multi_sn'),
        propOpenMultiSn: this.model.get('openMultiSn'),
        dht_serial_number_param_switch: this.model.get('dht_serial_number_param_switch'),
      },
    });
    batchSnDialog.$on('updateWillExpireStockWarningType', value => {
      this.model.set('willExpireStockWarningType', value);
      FS.crmUtil.remind(1, $t('操作成功'));
    });
    batchSnDialog.$on('updateOpenMultiSn', value => {
      this.model.set('openMultiSn', value);
      FS.crmUtil.remind(1, $t('操作成功'));
    });
  },

  _handleEnableBatch: function (e) {
    var me = this;
    var $target = $(e.currentTarget);
    if ($target.hasClass('oprate-btn_switch--disabled')) {
      return;
    }

    var tips =
      '<p>' + $t('批次与序列号开启后，不可关闭。') + '</p><br><p>' + $t('是否确认要开启批次与序列号？') + '</p>';
    var confirm = FS.crmUtil.confirm(tips, $t('批次与序列号'), () => {
      confirm.hide();
      $target.addClass('oprate-btn_switch--disabled oprate-btn_switch--on');

      enableBatch
        .circle({}, (data, stop) => {
          if (data.resultCode == 1) stop(data);
        })
        .res(() => {
          me.model.set('batchSNSwitch', 2);
          me.renderTemplate();
          FS.crmUtil.remind(1, $t('批次与序列号开启成功'));
        })
        .catch(err => {
          FS.crmUtil.remind(3, err || $t('启用失败请稍后刷新重试或联系纷享客服'));
        });
    });
  },

  _handleMultiSwitch: function (e) {
    var $target = $(e.currentTarget);
    var me = this;
    if ($target.hasClass('oprate-btn_switch--disabled')) {
      return;
    }

    var tips =
      '<p>' + $t('快消行业多单位开启后，不可关闭。') + '</p><br><p>' + $t('是否确认要开启快消行业多单位？') + '</p>';
    var confirm = FS.crmUtil.confirm(tips, $t('快消行业多单位'), () => {
      confirm.hide();
      me.model.set('showMask', true);
      enableStockSaleMulti
        .circle({}, (data, stop) => {
          if (data.resultCode == 1) stop(data);
        })
        .res(res => {
          var msg = (res.Value && res.Value.Result.FailureMessage) || null;
          if (msg) {
            FS.crmUtil.remind(3, msg);
          } else {
            FS.crmUtil.remind(1, $t('操作成功'));
          }
          $target.addClass('oprate-btn_switch--on oprate-btn_switch--disabled');
          me.model.set('showMask', false);
        })
        .catch(err => {
          me.model.set('showMask', false);
          FS.crmUtil.remind(3, err || $t('启用失败请稍后刷新重试或联系纷享客服'));
        });
    });
  },

  _handleCostAdjustmentSwitch: function (e) {
    var $target = $(e.currentTarget);
    if ($target.hasClass('oprate-btn_switch--disabled')) {
      return;
    }
    var tips = '<p>' + $t('成本管理开启后，不可关闭。') + '</p><br><p>' + $t('是否确认要开启成本管理？') + '</p>';
    var confirm = FS.crmUtil.confirm(tips, $t('成本管理'), () => {
      confirm.hide();
      $target.addClass('oprate-btn_switch--disabled oprate-btn_switch--on');
      FS.crmUtil.remind(1, $t('正在开启成本管理...'), null, 2000);
      enableCostAdjustment
        .circle({}, (data, stop) => {
          if (data.resultCode == 1) stop(data);
        })
        .res(() => {
          FS.crmUtil.remind(1, $t('成本管理开启成功'));
        })
        .catch(err => {
          FS.crmUtil.remind(3, err || $t('启用失败请稍后刷新重试或联系纷享客服'));
        });
    });
  },

  _handleSwitchDeliveryNoteReturn(e) {
    return this._handleSwitchAsyncSetConfig(e, 'delivery_note_returned_goods_switch');
  },

  _handleSwitchReturn(e) {
    return this._handleSwitchAsyncSetConfig(e, 'non_note_returned_goods_switch');
  },

  _handleSwitchExchange(e) {
    var me = this;
    var $target = $(e.currentTarget);
    if ($target.hasClass('oprate-btn_switch--disabled')) {
      return;
    }

    var tips =
        '<p>' + $t('stock.shiporder.exchange_plugin_open.text') + '</p>';
    var confirm = FS.crmUtil.confirm($t('stock.shiporder.exchange_plugin_open.text'), $t('stock.shiporder.exchange_plugin_open.title'), () => {
      confirm.hide();
      $target.addClass('oprate-btn_switch--disabled oprate-btn_switch--on');

      enableExchangePlugin
        .circle({ key: 'returned_goods_exchange_switch', value: '2' }, (data, stop) => {
          if (data.resultCode == 1) stop(data);
        })
        .res(() => {
          me.model.set('returned_goods_exchange_switch', 2);
          me.renderTemplate();
          FS.crmUtil.remind(1, $t('stock.shiporder.exchange_plugin_open.success'));
        })
        .catch(err => {
          FS.crmUtil.remind(3, err || $t('启用失败请稍后刷新重试或联系纷享客服'));
        });
    });
  },

  // key 要配置的 key
  _handleSwitchAsyncSetConfig(e, key) {
    var $target = $(e.currentTarget);
    if ($target.hasClass('oprate-btn_switch--disabled')) {
      return;
    }
    this.model.set('showMask', true);
    const value = this.model.get(key);
    asyncSetConfigValue()
      .circle({ key, value: value === '1' ? '2' : '1', token: '' }, (data, stop) => {
        if (data.resultCode == 1) stop(data);
      })
      .res(res => {
        var msg = (res.Value && res.Value.Result.FailureMessage) || null;
        if (msg) {
          FS.crmUtil.remind(3, msg);
        } else {
          FS.crmUtil.remind(1, $t('操作成功'));
          this.model.set(key, '2');
          $target.addClass('oprate-btn_switch--on oprate-btn_switch--disabled');
        }
        this.model.set('showMask', false);
      })
      .catch(err => {
        FS.crmUtil.remind(3, err || $t('启用失败请稍后刷新重试或联系纷享客服'));
        this.model.set('showMask', false);
      });
  },

  /**
   * @description K3C 参数设置
   * @param e
   * @private
   */
  _handleK3CSetting(e) {
    const el = $('<div id="app"></div>');
    $('body').append(el);
    // 实例化k3c参数配置组件
    const k3cDialog = new K3CDialog({
      el: el[0],
      propsData: {
        propSyncType: this.model.get('kingDeeK3CSyncType'),
      },
    });
    // "1": 同步库存 "2": 同步库存明细
    k3cDialog.$on('updateKingDeeK3CSyncType', value => {
      this.model.set('kingDeeK3CSyncType', value);
      FS.crmUtil.remind(1, $t('操作成功'));
    });
  },

  _handleRealTimeStockSwitch(e) {
    const $target = $(e.currentTarget);
    const type = this.model.get('erp_real_time_inventory').type == '1' ? '0' : '1';
    const isOpen = type == '1';
    const v = {
      'type': type, // 0:开关关闭，1:通过集成平台， 2:通过APL函数
      'functionApiName': '', // type为1时， 该项可不填， type为2时， 该项必填
      'functionDes': '', // 函数描述
      'dataProcessingType':'0' //"0" : 忽略匹配不到的库存数据，不做处理。 “1”：在CRM创建对应的库存数据，并更新实时库存
    };

    set_erp_real_time_inventory(JSON.stringify(v)).then(() => {
      this.model.set('erp_real_time_inventory', v);
      FS.crmUtil.remind(1, $t('操作成功'));
      if (isOpen) {
        $target.addClass('oprate-btn_switch--on');
        $('.shiporder-manage_category-card-option__j-switch-realtimestock').addClass('active j-setting-realtimestock')
      } else {
        $target.removeClass('oprate-btn_switch--on');
        $('.shiporder-manage_category-card-option__j-switch-realtimestock').removeClass('active j-setting-realtimestock')
      }
    });
  },

  /**
   * 实时库存参数设置
   */
  _handleSettingRealTimeStock(e) {
    const el = $('<div id="app"></div>');
    $('body').append(el);
    // 实例化k3c参数配置组件
    const realTimeStockDialog = new RealTimeStockDialog({
      el: el[0],
      propsData: {
        default_erp_real_time_inventory: this.model.get('erp_real_time_inventory')
      }
    });
    // "1": 同步库存 "2": 同步库存明细
    realTimeStockDialog.$on('confirm', value => {
      this.model.set('erp_real_time_inventory', value);
      set_erp_real_time_inventory(JSON.stringify(value)).then(() => {
        FS.crmUtil.remind(1, $t('操作成功'));
      });
    });
  },


  /**
   * @description 成本管理 参数设置
   * @param e
   * @private
   */
  _handleKCostManagementSetting(e) {
    const el = $('<div id="app"></div>');
    $('body').append(el);
    // 实例化k3c参数配置组件
    const k3cDialog = new CostManagementDialog({
      el: el[0],
      propsData: {
        prop_cost_management_calculate: this.model.get('cost_management_calculate'),
      },
    });
    // "1": 同步库存 "2": 同步库存明细
    k3cDialog.$on('updatecost_management_calculate', value => {
      this.model.set('cost_management_calculate', value);
      FS.crmUtil.remind(1, $t('操作成功'));
    });
  },

  /**
   * 获取下游设置参数
   */
  getDownStreamParams() {
    const correspondingUpstreamList: [UpStreamListSelectOption] = this.model
      .get('correspondingUpstreamList')
      .map(item => {
        return {
          ...item,
          value: item.upstreamTenantId,
          label: item.upstreamName,
        };
      });
    return {
      correspondingUpstreamList,
      selectedCorrespondingUpstream: correspondingUpstreamList.length
        ? correspondingUpstreamList[0].upstreamTenantId
        : '',
    };
  },

  // 获取上游设置参数
  getPurchaseUpstreamParam() {
    let autoGenerateDeliveryConfig: string = this.model.get('autoGenerateDeliveryConfig');
    return { autoGenerateDeliveryConfig };
  },

  /**
   * @description K3C插件开关
   * @param e
   * @private
   */
  _handleK3CSwtich(e) {
    var $target = $(e.currentTarget);
    if ($target.hasClass('oprate-btn_switch--disabled')) {
      return;
    }
    var tips = '<p>' + $t('金蝶K3C对接插件开启后，不可关闭。') + '</p><br><p>' + $t('是否确认要开启该插件？') + '</p>';
    var confirm = FS.crmUtil.confirm(tips, $t('stock.crm.setting.shiporder.open_K3C_plugin'), () => {
      confirm.hide();
      this.model.set('showMask', true);

      asyncEnableK3C
        .circle({}, (data, stop) => {
          if (data.resultCode == 1) stop(data);
        })
        .res(res => {
          var msg = (res.Value && res.Value.Result.FailureMessage) || null;
          if (msg) {
            FS.crmUtil.remind(3, msg);
          } else {
            FS.crmUtil.remind(1, $t('操作成功'));
          }
          $target.addClass('oprate-btn_switch--on oprate-btn_switch--disabled');
          $('.shiporder-manage_category-card-option__k3c-param').addClass('active j-setting-k3c');
          this.model.set('showMask', false);
        })
        .catch(err => {
          this.model.set('showMask', false);
          FS.crmUtil.remind(3, err || $t('启用失败请稍后刷新重试或联系纷享客服'));
        });
    });
  },

  /**
   * @description 仓位插件开启
   */
  _handleEnableWarehousePosition(e) {
    var $target = $(e.currentTarget);
    if ($target.hasClass('oprate-btn_switch--disabled')) {
      return;
    }
    var tips = '<p>' + $t('仓位管理开启后，不可关闭。') + '</p><br><p>' + $t('是否确认要开启仓位管理？') + '</p>';
    var confirm = FS.crmUtil.confirm(tips, $t('开启仓位管理'), () => {
      confirm.hide();
      this.model.set('showMask', true);

      enableParentWareHouse
        .circle({}, (data, stop) => {
          if (data.resultCode == 1) stop(data);
        })
        .res(res => {
          var msg = (res.Value && res.Value.Result.FailureMessage) || null;
          if (msg) {
            FS.crmUtil.remind(3, msg);
          } else {
            FS.crmUtil.remind(1, $t('操作成功'));
          }
          $target.addClass('oprate-btn_switch--on oprate-btn_switch--disabled');
          this.model.set('showMask', false);
        })
        .catch(err => {
          this.model.set('showMask', false);
          FS.crmUtil.remind(3, err || $t('启用失败请稍后刷新重试或联系纷享客服'));
        });
    });
  },

  /**
   * @description CPQ 组合发货方式变更
   */
  _cpqDeliveryWayChangeHandle(e) {
	const sales_order_ato_pto_switch_gray = this.model.get('sales_order_ato_pto_switch_gray');
	// 关闭CPQ之后，不允许再开启。新用户，不再展示CPQ插件的入口
	this.cpqDeliveryWayChangeHandle_of_ato_pto(e);
	// if (!sales_order_ato_pto_switch_gray) {
	// 	this.cpqDeliveryWayChangeHandle(e);
	// 	// 如果未灰度ATO&PTO开关，则走老的CPQ 开启/关闭逻辑
	// } else {
	// 	// 如果灰度了ATO&PTO开关，则：关闭CPQ之后，不允许再开启。新用户，不再展示CPQ插件的入口
	// 	this.cpqDeliveryWayChangeHandle_of_ato_pto(e);
	// }
  },

  cpqDeliveryWayChangeHandle(e) {
    const $target = $(e.currentTarget);

    const way = this.model.get('cpqDeliveryWay');

    this.model.set('showMask', true);

    asyncSetConfigValue()
      .circle({ key: 'delivery_note_cpq_deliver_switch', value: way === 1 ? 2 : 1, token: '' }, (data, stop) => {
        if (data.resultCode == 1) stop(data);
      })
      .res(res => {
        var msg = (res.Value && res.Value.Result.FailureMessage) || null;
        if (msg) {
          FS.crmUtil.remind(3, msg);
        } else {
          FS.crmUtil.remind(1, $t('操作成功'));
        }
        $target.toggleClass('oprate-btn_switch--on');
        this.model.set('showMask', false);
      })
      .catch(err => {
        this.model.set('showMask', false);
        FS.crmUtil.remind(3, err || $t('启用失败请稍后刷新重试或联系纷享客服'));
      });
  },

  cpqDeliveryWayChangeHandle_of_ato_pto(e) {
    const $target = $(e.currentTarget);

    const way = this.model.get('cpqDeliveryWay');

	if (way == 1) {
		// 关闭 CPQ组合发货插件 时，需要弹窗提醒，提示该插件即将下架
		const el = $('<div></div>');
		$('body').append(el);
		const dialog = new cpqDeliveryClose({
			el: el[0],
		  });
		  dialog.$on('confirm', () => {
			this.model.set('showMask', true);

			asyncSetConfigValue()
			.circle({ key: 'delivery_note_cpq_deliver_switch', value: 2, token: '' }, (data, stop) => {
				if (data.resultCode == 1) stop(data);
			})
			.res(res => {
				var msg = (res.Value && res.Value.Result.FailureMessage) || null;
				if (msg) {
				FS.crmUtil.remind(3, msg);
				} else {
				FS.crmUtil.remind(1, $t('操作成功'));
				}
				$target.removeClass('oprate-btn_switch--on');
				this.model.set('showMask', false);
			})
			.catch(err => {
				this.model.set('showMask', false);
				FS.crmUtil.remind(3, err || $t('启用失败请稍后刷新重试或联系纷享客服'));
			});

		  });

	} else {
		this.model.set('showMask', true);
		asyncSetConfigValue()
		  .circle({ key: 'delivery_note_cpq_deliver_switch', value: 1, token: '' }, (data, stop) => {
			if (data.resultCode == 1) stop(data);
		  })
		  .res(res => {
			var msg = (res.Value && res.Value.Result.FailureMessage) || null;
			if (msg) {
			  FS.crmUtil.remind(3, msg);
			} else {
			  FS.crmUtil.remind(1, $t('操作成功'));
			}
			$target.toggleClass('oprate-btn_switch--on');
			this.model.set('showMask', false);
		  })
		  .catch(err => {
			this.model.set('showMask', false);
			FS.crmUtil.remind(3, err || $t('启用失败请稍后刷新重试或联系纷享客服'));
		  });
	}
  },

  // 多订单、单订单开关切换处理
  handleActionSwitch(e) {
    const $target = $(e.currentTarget);
    const el = $('<div></div>');
    $('body').append(el);
    let multiOrderDeliverySwitch = this.model.get('multiOrderDeliverySwitch');
    if (multiOrderDeliverySwitch) {
      // 多订单发货关闭
      const dialog = new multiOrderDeliveryClose({
        el: el[0],
      });
      dialog.$on('confirm', () => {
        this.model.set('showMask', true);

        asyncSetConfigValue()
          .circle({ key: 'delivery_note_multi_order_deliver_switch', value: 0, token: '' }, (data, stop) => {
            if (data.resultCode == 1) stop(data);
          })
          .res(res => {
            var msg = (res.Value && res.Value.Result.FailureMessage) || null;
            if (msg) {
              FS.crmUtil.remind(3, msg);
            } else {
              FS.crmUtil.remind(1, $t('操作成功'));
            }
            this.model.set({ multiOrderDeliverySwitch: !multiOrderDeliverySwitch });
            $target.removeClass('oprate-btn_switch--on');
            this.model.set('showMask', false);
          })
          .catch(err => {
            this.model.set('showMask', false);
            FS.crmUtil.remind(3, err || $t('启用失败请稍后刷新重试或联系纷享客服'));
          });
      });
    } else {
      // 多订单发货开启
      const dialog = new multiOrderDeliveryOpen({
        el: el[0],
      });
      dialog.$on('confirm', () => {
        this.model.set('showMask', true);

        asyncSetConfigValue()
          .circle({ key: 'delivery_note_multi_order_deliver_switch', value: 1, token: '' }, (data, stop) => {
            if (data.resultCode == 1) stop(data);
          })
          .res(res => {
            var msg = (res.Value && res.Value.Result.FailureMessage) || null;
            if (msg) {
              FS.crmUtil.remind(3, msg);
            } else {
              FS.crmUtil.remind(1, $t('操作成功'));
            }
            this.model.set({ multiOrderDeliverySwitch: !multiOrderDeliverySwitch });
            $target.toggleClass('oprate-btn_switch--on');
            this.model.set('showMask', false);
          })
          .catch(err => {
            this.model.set('showMask', false);
            FS.crmUtil.remind(3, err || $t('启用失败请稍后刷新重试或联系纷享客服'));
          });
      });
    }
  },

  // 函数校验实际库存
  _handleCustomFunctionConfigClick(e) {
    e.stopPropagation();
    const el = e.target;
    const type = $(el).data('type');
    const handler = {
      config: this._handleCustomFunctionConfig,
      flow: this._handleCustomFunctionFlow,
      guide: this._handleCustomFunctionGuide,
      guideStock: this._handleCustomFunctionGuideStock,
    };

    const fn = handler[type];
    if (fn) {
      fn.call(this);
    }
  },

  // 跳转
  _openNewTab(url) {
    const $el = $('<a></a>');
    $el.attr('href', url);
    $el.attr('target', '_blank');
    $('body').append($el);
    $el[0].click();
    $el.remove();
  },

  // 配置函数
  _handleCustomFunctionConfig() {
    const url = location.origin + location.pathname + '#crmmanage/=/module-myfunction';
    return this._openNewTab(url);
  },

  // 配置流程
  _handleCustomFunctionFlow() {
    const url = location.origin + location.pathname + '#crmmanage/=/module-approval';
    return this._openNewTab(url);
  },

  // 查看指引
  _handleCustomFunctionGuide() {
    const el = $('<div></div>');
    $('body').append(el);
    const customFunctionGuide = new CustomFunctionGuide({
      el: el[0],
      name: 'CustomFunctionGuide',
    });

    customFunctionGuide.$on('close', () => {
      customFunctionGuide.$destroy();
    });
  },

  // 查看指引
  _handleCustomFunctionGuideStock() {
    const el = $('<div></div>');
    $('body').append(el);
    const customFunctionGuideStock = new CustomFunctionGuideStock({
      el: el[0],
      name: 'customFunctionGuideStock',
    });

    customFunctionGuideStock.$on('close', () => {
      customFunctionGuideStock.$destroy();
    });
  },
  /**
   *  弹出采购业务联动参数面板
   */
  _handleSettingInterconnection() {
    // 互联关系
    const identityInfos = {
      isUp: this.model.get('isUpstreamEnterprise'),
      isDown: this.model.get('isDownstreamEnterprise'),
    };
    // 上游需要的参数
    const upStreamParams: UpParams = this.getPurchaseUpstreamParam();
    // 下游需要的参数
    const downStreamParams: DownParams = this.getDownStreamParams();
    // 挂载弹窗
    const el = $('<div></div>');
    $('body').append(el);
    const ICSD = new InterconnectionSettingDialog({
      el: el[0],
      name: 'InterconnectionSettingDialog',
      propsData: {
        identityInfos,
        upStreamParams,
        downStreamParams,
      },
    });
    ICSD.$on('updateUpStreamParams', (upStreamParams: UpParams) => {
      this.model.set('autoGenerateDeliveryConfig', upStreamParams.autoGenerateDeliveryConfig);
    });
  },

  _handleNegativeInventory(e) {
    const $target = $(e.currentTarget);
    if ($target.hasClass('oprate-btn_switch--disabled')) {
      return;
    }
    const negativeInventorySwitch = this.model.get('negativeInventorySwitch') === '1' ? '2' : '1';

    this.model.set('showMask', true);
    setNegativeInventorySwitch({
      negativeInventorySwitch,
    })
      .then(({ isSuccess }) => {
        if (!isSuccess) {
          FS.crmUtil.remind(1, $t('操作失败'));
          return;
        }
        FS.crmUtil.remind(1, $t('操作成功'));
        this.model.set({
          negativeInventorySwitch,
        });
        negativeInventorySwitch === '1'
          ? $target.addClass('oprate-btn_switch--on')
          : $target.removeClass('oprate-btn_switch--on');
      })
      .finally(() => {
        this.model.set('showMask', false);
      });
  },
  // 智能补货插件开启
  _handleSmartSwtich() {
    let me = this;
    me.model.set('showMask', true);
    let _type = me.model.get('intelligentReplenishmentSwitch') == '2' ? 3 : 2;
    setSmartSettings
      .circle(
        {
          intelligentReplenishmentSwitch: _type, // 2 开启 1 未开启 3 关闭
        },
        (data, stop) => {
          if (data.resultCode == 1) stop(data);
        }
      )
      .res(res => {
        me.model.set('showMask', false);
        if (res.resultCode == 1) {
          me.model.set({
            intelligentReplenishmentSwitch: _type,
          });
          FS.crmUtil.remind(1, $t('操作成功'));
        }
        me.renderTemplate();
      })
      .catch(err => {
        me.model.set('showMask', false);
      });
  },
  // 打开智能补货 参数设置页面
  _handleSmartSetting() {
    console.log('智能补货设置');
    const el = $('<div id="app"></div>');
    $('body').append(el);
    new SmartReplenishmentDialog({
      el: el[0],
      propsData: {
        intelligentReplenishmentSwitch: this.model.get('intelligentReplenishmentSwitch'),
      },
    });
  },

  // 开启渠道库存管理
  _handleSwitchDistributionStock(e) {
    const $target = $(e.currentTarget);
    if ($target.hasClass('oprate-btn_switch--disabled')) {
      return;
    }
    var tips =
      '<p>' + $t('渠道库存管理开启后，不可关闭。') + '</p><br><p>' + $t('是否确认要开启渠道库存管理？') + '</p>';
    var confirm = FS.crmUtil.confirm(tips, $t('开启渠道库存管理'), () => {
      confirm.hide();
      const key = 'distribution_stock_switch';
      FS.crmUtil.remind(1, $t('正在开启渠道库存管理...'), null, 5000);

      setSparePart
        .circle({ key, value: '2', token: '' }, (data, stop) => {
          if (data.resultCode == 1) stop(data);
        })
        .res(() => {
  		  $target.addClass('oprate-btn_switch--on oprate-btn_switch--disabled');
          FS.crmUtil.remind(1, $t('开启渠道库存管理成功'));
        })
        .catch(err => {
          FS.crmUtil.remind(3, err || $t('启用失败请稍后重试或联系纷享客服'), null, 3000);
        });
    });
  },

// 开启 快消行业商品条码
  _handleUniqueProductCodeManagement(e) {
    const $target = $(e.currentTarget);
    if ($target.hasClass('oprate-btn_switch--disabled')) {
      return;
    }
    var tips =
      '<p>' + $t('stock.crm.setting.shiporder.FMCG_industry_commodity_bar_code_opened') + '</p><br><p>' + $t('stock.crm.setting.shiporder.turn_on_the_bar_code_of_FMCG_industry_tip') + '</p>';
    var confirm = FS.crmUtil.confirm(tips, $t('stock.crm.setting.shiporder.FMCG_industry_commodity_bar_code'), () => {
      confirm.hide();
      const key = 'unique_product_code_management';
      FS.crmUtil.remind(1, $t('stock.crm.setting.shiporder.opening_FMCG_industry_commodity_bar_code'), null, 5000);

      setSparePart
        .circle({ key, value: '2', token: '' }, (data, stop) => {
          if (data.resultCode == 1) stop(data);
        })
        .res(() => {
		  $target.addClass('oprate-btn_switch--on oprate-btn_switch--disabled');
          FS.crmUtil.remind(1, $t('stock.crm.setting.shiporder.FMCG_industry_commodity_bar_code_success'));
        })
        .catch(err => {
          FS.crmUtil.remind(3, err || $t('启用失败请稍后重试或联系纷享客服'), null, 3000);
        });
    });
  },

// 开启 销售订单ATO&PTO组合发货
  _handleSalesOrderAtoPtoSwitch(e) {
    const $target = $(e.currentTarget);
    if ($target.hasClass('oprate-btn_switch--disabled')) {
      return;
    }

	// ATO&PTO插件 与 CPQ组合发货插件 互斥
	const delivery_note_cpq_deliver_switch = this.model.get('delivery_note_cpq_deliver_switch');
	if(delivery_note_cpq_deliver_switch == 1){
	  FS.crmUtil.remind(3, $t('该插件与CPQ组合发货插件互斥，请先关闭CPQ组合发货插件后重试。'));
	  return;
	}

    // AT0&PTO 插件与“直接从发货单产品退货”互斥
    const delivery_return_product_add_mode = this.model.get('delivery_return_product_add_mode');
    if (delivery_return_product_add_mode === '2') {
      FS.crmUtil.remind(3, $t('stock.shiporder.ato_pto.notice'));
      return;
    }

    // AT0&PTO 插件与“冻结库存调整”互斥
    const order_freeze_inventory_adjustment = this.model.get('order_freeze_inventory_adjustment');
    if (order_freeze_inventory_adjustment === '1') {
      // TODO 没有多语，后期处理
      FS.crmUtil.remind(3, $t('stock.crm.setting.shiporder.frozen_inventory_adjustment_function_has_been_enabled_not_ato'));
      return;
    }

	// 开启 ATO&PTO插件之前，需要先开启 BomCoreObj 对象。这个条件不需要前端判断，留给接口做校验，如果未开启成功，则接口直接报错就好
	/*
	const fmcg_cpq = this.model.get('fmcg_cpq');
	if(fmcg_cpq != 1){
	  // todo：文案待确认
	  FS.crmUtil.remind(3, $t('stock.crm.setting.shiporder.please_open_the_FMCG_CPQ_plugin_first'));
	  return;
	}
	*/

    var tips =
      '<p>' + $t('销售订单ATO&PTO组合发货，不可关闭。') + '</p><br><p>' + $t('是否确认要开启销售订单ATO&PTO组合发货？') + '</p>';
    var confirm = FS.crmUtil.confirm(tips, $t('销售订单ATO&PTO组合发货'), () => {
      confirm.hide();
      this.model.set('showMask', true);
      const key = 'sales_order_ato_pto_switch';
      FS.crmUtil.remind(1, $t('正在开启销售订单ATO&PTO组合发货...'), null, 5000);

      setSparePart
        .circle({ key, value: '1', token: '' }, (data, stop) => {
          if (data.resultCode == 1) stop(data);
        })
        .res(() => {
          FS.crmUtil.remind(1, $t('开启销售订单ATO&PTO组合发货成功'));
		  $target.addClass('oprate-btn_switch--on oprate-btn_switch--disabled');
          this.model.set('showMask', false);
        })
        .catch(err => {
		  FS.crmUtil.remind(3, err || $t('启用失败请稍后重试或联系纷享客服'), null, 3000);
		  this.model.set('showMask', false);
        });
    });
  },

  _handleExchangeSetting(...param) {
    console.log('_handleExchangeSetting_handleExchangeSetting_handleExchangeSetting', param);
    const el = $('<div id="app"></div>');
    $('body').append(el);
    new ExchangeSettingDialog({
      el: el[0],
    });
  },

  // 获取智能预估配置 是否开启 intelligentReplenishmentSwitch 2 开启 1 未开启 3 关闭
  _getSmartSettings() {
    return getSmartSettings().then((res: any) => {
      if (res.isSuccess) {
        if (res.data && res.data.intelligentReplenishmentSwitch) {
          this.model.set({
            intelligentReplenishmentSwitch: res.data.intelligentReplenishmentSwitch,
          });
        }
      }
    });
  },

  destroy: function () {
    this.$el.off();
    this.$el.empty();
    this.$el = this.el = this.options = null;
  },
});

export { Manage };
