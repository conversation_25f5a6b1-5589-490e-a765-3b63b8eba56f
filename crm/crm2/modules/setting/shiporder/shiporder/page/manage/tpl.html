<div class='shiporder-manage_wrapper'>
<!-- 开启 A类库存，或者开启 B类库存且开启渠道库存才显示销售订单的这个插件 -->
<!-- 由于目前销售订单插件只有一个，所以该条件就应该加在销售订单这里，而不是具体的插件那 -->
  ## if (isForErp == false && stockSwitch == 2 || isForErp == true && distribution_stock_switch == 2) { ##
  <div class='shiporder-manage_category'>
    <div class='shiporder-manage_category-title'>{{ $t("销售订单") }}</div>
    <div class='shiporder-manage_category-cards'>
      <div class='shiporder-manage_category-card j-card-receive'>
        <div class='shiporder-manage_category-card-title'>{{ $t("stock.shiporder.freeze_inventory_adjustment.title") }}</div>
        <div class='shiporder-manage_category-card-content'>{{ $t("stock.shiporder.freeze_inventory_adjustment.describe1") }}</div>
        ## if (isForErp == true && distribution_stock_switch == 2) { ##
        <div class='shiporder-manage_category-card-content'>{{ $t("stock.shiporder.freeze_inventory_adjustment.describe2") }}</div>
        ## } ##
        <div class='shiporder-manage_category-card-footer'>
          <div class='shiporder-manage_category-card-options'>
          </div>
          <div class='shiporder-manage_category-card-switcher'>
            <i class="oprate-btn_switch j-freeze-inventory-adjustment-switch {{obj.order_freeze_inventory_adjustment == 1 ? 'oprate-btn_switch--on oprate-btn_switch--disabled' : ''}}"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  ## } ##
  <div class='shiporder-manage_category'>
    <div class='shiporder-manage_category-title'>{{ $t("发货单") }}</div>
    <div class='shiporder-manage_category-cards'>
      <div class='shiporder-manage_category-card j-card-receive'>
        <div class='shiporder-manage_category-card-title'>{{ $t("确认收货") }}</div>
        <div class='shiporder-manage_category-card-content'>{{ $t("可设置系统自动确认收货，或手动收货时必须电子签名") }}</div>
        <div class='shiporder-manage_category-card-footer'>
          <div class='shiporder-manage_category-card-options'>
            <!-- <div class="shiporder-manage_category-card-option j-detail">{{ $t("详情") }}</div> -->
            <div class="shiporder-manage_category-card-option {{obj.autoReceiveStatus == 1 ? 'active j-setting-receive' : ''}}">{{ $t("参数设置") }}</div>
          </div>
          <div class='shiporder-manage_category-card-switcher'>
            <i class="oprate-btn_switch j-switch-autoreceive {{obj.autoReceiveStatus == 1 ? 'oprate-btn_switch--on' : ''}}"></i>
          </div>
        </div>
      </div>
      ## if (obj.stockStatus === 2) { ##
      <div class='shiporder-manage_category-card'>
        <div class='shiporder-manage_category-card-title'>{{ $t("函数校验实际库存-发货单") }}</div>
        <div class='shiporder-manage_category-card-content'>
          {{ $t("配置自定义函数，作为审批前置条件校验，避免实际库存不足导致发货单被作废") }}
        </div>
        <div class='shiporder-manage_category-card-footer'>
          <div class='shiporder-manage_category-card-options j-cf-config kc-left'>
            <div class='shiporder-manage_category-card-option active' data-type='config'>{{ $t("配置函数") }}</div>
            <div class='shiporder-manage_category-card-option active' data-type='flow'>{{ $t("配置流程") }}</div>
            <div class='shiporder-manage_category-card-option active' data-type='guide'>{{ $t("查看指引") }}</div>
          </div>
        </div>
      </div>
      ## } ##
	 <!-- 890迭代需求：对于新租户，以及未开启CPQ组合发货插件的现有租户，均下架“CPQ组合发货”插件，前端不可见，不允许再开启。 -->
	 ## if (!obj.isUpstreamEnterprise && !obj.isDownstreamEnterprise && obj.cpqDeliveryWay == 1) { ##
      <div class='shiporder-manage_category-card'>
        <div class='shiporder-manage_category-card-title'>CPQ {{ $t("组合发货") }}</div>
        <div class='shiporder-manage_category-card-content'>{{ $t("开启CPQ组合发货") }}</div>
        <div class='shiporder-manage_category-card-footer'>
          <div class='shiporder-manage_category-card-options'>

          </div>
          <div class='shiporder-manage_category-card-switcher'>
            <i class="oprate-btn_switch j-switch-cpq-delivery-way {{obj.cpqDeliveryWay == 1 ? 'oprate-btn_switch--on' : ''}}"></i>
          </div>
        </div>
      </div>
      ## } ##

      ## if (obj.hasMultiOrderDeliveryGray && (!obj.isUpstreamEnterprise && !obj.isDownstreamEnterprise)) { ##
      <div class='shiporder-manage_category-card'>
        <div class='shiporder-manage_category-card-title'>{{ $t("多订单发货") }}</div>
        <div class='shiporder-manage_category-card-content'>{{ $t("启用后，支持对同一个客户的多张订单同时发货") }}</div>
        <div class='shiporder-manage_category-card-footer'>
          <div class='shiporder-manage_category-card-options'>

          </div>
          <div class='shiporder-manage_category-card-switcher'>
            <i class="oprate-btn_switch j-switch-action {{obj.multiOrderDeliverySwitch === 1 ? 'oprate-btn_switch--on' : ''}}"></i>
          </div>
        </div>
      </div>
      ## } ##

      <div class='shiporder-manage_category-card'>
        <div class='shiporder-manage_category-card-title'>{{ $t("销售订单ATO&PTO组合发货") }}</div>
        <div class='shiporder-manage_category-card-content'>{{ $t("开启后，将根据订单产品上的BOM标识，来决定订单产品以ATO或者PTO的模式发货") }}</div>
        <div class='shiporder-manage_category-card-footer'>
        <div class='shiporder-manage_category-card-options'>

        </div>
        <div class='shiporder-manage_category-card-switcher'>
          <i class="oprate-btn_switch j-sales-order-ato-pto-switch {{obj.sales_order_ato_pto_switch == 1 ? 'oprate-btn_switch--on oprate-btn_switch--disabled' : ''}}"></i>
        </div>
        </div>
      </div>

    </div>
  </div>
  <div class='shiporder-manage_category'>
    <div class='shiporder-manage_category-title'>{{ $t("退货单") }}</div>
    <div class='shiporder-manage_category-cards'>
      <div class='shiporder-manage_category-card j-card-return'>
        <div class='shiporder-manage_category-card-title'>{{ $t("基于发货单退货") }}</div>
        <div class='shiporder-manage_category-card-content'>{{ $t("开启后，可基于发货单进行退货，退货单将同时与发货单、销售订单进行联动") }}</div>
        <div class='shiporder-manage_category-card-footer'>
          <div class='shiporder-manage_category-card-options'>
            <div class="shiporder-manage_category-card-option {{obj.delivery_note_returned_goods_switch == 2 ? 'active j-setting-delivery_return_product_add_mode' : ''}}">
              {{ $t("参数设置") }}
            </div>
          </div>
          <div class='shiporder-manage_category-card-switcher'>
            <i class="oprate-btn_switch j-switch-delivery-note-return {{obj.delivery_note_returned_goods_switch == 2 ? 'oprate-btn_switch--on  oprate-btn_switch--disabled' : ''}}"></i>
          </div>
        </div>
      </div>
      <div class='shiporder-manage_category-card j-card-return'>
        <div class='shiporder-manage_category-card-title'>{{ $t("无源单退货") }}</div>
        <div class='shiporder-manage_category-card-content'>{{ $t("开启后，可不基于任何单据进行退货，也可基于其他预设对象或者自定义对象进行退货（需自行添加相关对象的查找关联字段）") }}</div>
        <div class='shiporder-manage_category-card-footer'>
          <div class='shiporder-manage_category-card-options'>
          </div>
          <div class='shiporder-manage_category-card-switcher'>
            <i class="oprate-btn_switch j-switch-return {{obj.non_note_returned_goods_switch == 2 ? 'oprate-btn_switch--on  oprate-btn_switch--disabled' : ''}}"></i>
          </div>
        </div>
      </div>
      <div class='shiporder-manage_category-card j-card-return'>
        <div class='shiporder-manage_category-card-title'>{{ $t("换货") }}</div>
        <div class='shiporder-manage_category-card-content'>{{ $t("退货单换货插件") }}</div>
        <div class='shiporder-manage_category-card-footer'>
          <div class='shiporder-manage_category-card-options'>
            ## if (!obj.isForErp && obj.stockStatus === 2) { ##
            <div class="shiporder-manage_category-card-option {{obj.returned_goods_exchange_switch == 2 ? 'active j-setting-exchange' : ''}}">{{ $t("参数设置") }}</div>
            ## } ##
          </div>
          <div class='shiporder-manage_category-card-switcher'>
            <i class="oprate-btn_switch j-switch-exchange {{obj.returned_goods_exchange_switch == 2 ? 'oprate-btn_switch--on  oprate-btn_switch--disabled' : ''}}"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  ## if (obj.isOpenWareHousePosition == 1) { ##
  <div class='shiporder-manage_category'>
    <div class='shiporder-manage_category-title'>{{ $t("仓库") }}</div>
    <div class='shiporder-manage_category-cards'>
      <div class='shiporder-manage_category-card j-card-receive'>
        <div class='shiporder-manage_category-card-title'>{{ $t("仓位") }}</div>
        <div class='shiporder-manage_category-card-content'>{{ $t("开启仓位管理") }}</div>
        <div class='shiporder-manage_category-card-footer'>
          <div class='shiporder-manage_category-card-options'>
          </div>
          <div class='shiporder-manage_category-card-switcher'>
            <i class="oprate-btn_switch j-switch-warehouse-position {{obj.isOpenWareHousePosition == 1 ? 'oprate-btn_switch--on oprate-btn_switch--disabled' : ''}}"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  ## } ##
  ## if ((obj.isUpstreamEnterprise && obj.upstreamDisplayStockInterconnection)||(obj.isDownstreamEnterprise && obj.downstreamDisplayStockInterconnection)) { ##
  <div class='shiporder-manage_category'>
    <div class='shiporder-manage_category-title shiporder-manage_category__current-identity'>
      <span>{{ $t("互联插件") }}</span>
    </div>
    <div class='shiporder-manage_category-cards'>
      <div class='shiporder-manage_category-card'>
        <div class='shiporder-manage_category-card-title'>{{ $t("采购业务联动") }}</div>
        <div class='shiporder-manage_category-card-content'>{{ $t("开启后，上游的销售订单同步生成采购订单，上游的发货单确认收货后生成采购入库单") }}</div>
        <div class='shiporder-manage_category-card-footer'>
          <div class='shiporder-manage_category-card-options'>
            <div class='shiporder-manage_category-card-option active j-setting-interconnection'>{{ $t("参数设置") }}</div>
          </div>
          <div class='shiporder-manage_category-card-switcher'>
            ## if (obj.isDownstreamEnterprise && obj.downstreamDisplayStockInterconnection) { ##
            <i class='oprate-btn_switch oprate-btn_switch--on oprate-btn_switch--disabled'></i>
            ## } else if(obj.isUpstreamEnterprise&&obj.upstreamDisplayStockInterconnection){ ##
            <i class='oprate-btn_switch oprate-btn_switch--on oprate-btn_switch--disabled'></i>
            ## } ##
          </div>
        </div>
      </div>
      <div class='shiporder-manage_category-card '>
        <div class='shiporder-manage_category-card-title'>{{ $t("退货业务联动") }}</div>
        <div class='shiporder-manage_category-card-content'>{{ $t("开启后，可跨企业提交退货申请，上游审核通过后自动生成相应的采购退货单") }}</div>
        <div class='shiporder-manage_category-card-footer'>
          <div class='shiporder-manage_category-card-options'>
          </div>
          <div class='shiporder-manage_category-card-switcher'>
            <i class='oprate-btn_switch oprate-btn_switch--on oprate-btn_switch--disabled'></i>
          </div>
        </div>
      </div>
      <!--                        <div class="shiporder-manage_category-card ">-->
      <!--                                <div class="shiporder-manage_category-card-title">{{ $t("在途库存") }}</div>-->
      <!--                                <div class="shiporder-manage_category-card-title">{{ $t("stock.stock_manage.info.text6") }}</div>-->
      <!--                                <div class="shiporder-manage_category-card-content">{{ $t("开启后，将新增库存明细对象，可接收库存明细数据，并关联对应的库存记录") }}</div>-->
      <!--                                <div class="shiporder-manage_category-card-footer">-->
      <!--                                        <div class="shiporder-manage_category-card-options">-->
      <!--                                        </div>-->
      <!--                                        <div class="shiporder-manage_category-card-switcher">-->
      <!--                                                <i class="oprate-btn_switch oprate-btn_switch&#45;&#45;on oprate-btn_switch&#45;&#45;disabled"></i>-->
      <!--                                        </div>-->
      <!--                                </div>-->
      <!--                        </div>-->
    </div>
  </div>
  ## } ##
  ## if ((+obj.stockSwitch !== 1)||(!obj.isUpstreamEnterprise && !obj.isDownstreamEnterprise)) { ##
  <div class='shiporder-manage_category'>
    <div class='shiporder-manage_category-title'>{{ $t("行业插件") }}</div>

    <div class='shiporder-manage_category-cards'>
      ## if (+obj.stockSwitch !== 1) { ##
      <div class='shiporder-manage_category-card j-card-batch'>
        <div class='shiporder-manage_category-card-title'>{{ $t("批次与序列号") }}</div>
        <div class='shiporder-manage_category-card-content'>{{ $t("开启批次与序列号管理") }}</div>
        <div class='shiporder-manage_category-card-footer'>
          <div class='shiporder-manage_category-card-options'>
            <div class="shiporder-manage_category-card-option {{obj.stockSwitch == 2 && obj.batchSNSwitch == 2 ? 'active j-setting-batch' : ''}}">{{ $t("参数设置") }}</div>
          </div>
          <div class='shiporder-manage_category-card-switcher'>
            <i class="oprate-btn_switch j-switch-batch {{obj.batchSNSwitch == 2 ? 'oprate-btn_switch--on oprate-btn_switch--disabled' : ''}}"></i>        </div>
        </div>
      </div>
      ## } ##

      ## if (!obj.isUpstreamEnterprise && !obj.isDownstreamEnterprise) { ##
      <div class='shiporder-manage_category-card'>
        <div class='shiporder-manage_category-card-title'>{{ $t("快消行业多单位") }}</div>
        <div class='shiporder-manage_category-card-content'>{{ $t("针对快消行业的特性做优化，同时支持多种单位输入数量，新增数量合计等") }}</div>
        <div class='shiporder-manage_category-card-footer'>
          <div class='shiporder-manage_category-card-options'>
          </div>
          <div class='shiporder-manage_category-card-switcher'>
            <i class="oprate-btn_switch j-multi-switch {{obj.multiSwitch == 1 ? 'oprate-btn_switch--on oprate-btn_switch--disabled' : ''}}"></i>
          </div>
        </div>
      </div>
      ## } ##

	  <!-- 一物一码：由后端控制该插件是否展示 -->
      ## if (obj.unique_product_code_management_gray) { ##
		<div class='shiporder-manage_category-card'>
		  <div class='shiporder-manage_category-card-title'>{{ $t("stock.crm.setting.shiporder.FMCG_industry_commodity_bar_code") }}</div>
<!--		  <div class='shiporder-manage_category-card-content'>{{ $t("支持快消行业的商品条码管理") }}</div>-->
		  <div class='shiporder-manage_category-card-content'>{{ $t("stock.stock_manage.info.text7") }}</div>
		  <div class='shiporder-manage_category-card-footer'>
			<div class='shiporder-manage_category-card-options'>
			</div>
			<div class='shiporder-manage_category-card-switcher'>
			  <i class="oprate-btn_switch j-unique-product-code-management {{obj.unique_product_code_management == 2 ? 'oprate-btn_switch--on oprate-btn_switch--disabled' : ''}}"></i>
			</div>
		  </div>
		</div>
		## } ##

      <!-- 智能补货 active j-setting-batch  参数设置是否可点击  oprate-btn_switch--on 是否开启-->
      ## if (obj.intelligent_replenishment_switch) {##
        <div class='shiporder-manage_category-card j-card-batch'>
          <div class='shiporder-manage_category-card-title'>{{ $t("智能补货") }}</div>
          <div class='shiporder-manage_category-card-content'>{{ $t("根据销售、采购、库存、退货、换货等数据进行采购数量的预估") }}</div>
          <div class='shiporder-manage_category-card-footer'>
            <div class='shiporder-manage_category-card-options'>
              <div class="shiporder-manage_category-card-option  {{obj.intelligentReplenishmentSwitch==2?' active j-setting-smart':''}}">{{ $t("参数设置") }}</div>
            </div>
            <div class='shiporder-manage_category-card-switcher'>
              <i class="oprate-btn_switch j-switch-smart {{obj.intelligentReplenishmentSwitch==2?'oprate-btn_switch--on':''}}"></i>
            </div>
          </div>
        </div>
        ## } ##
    </div>
  </div>
  ## } ##
  ## if (!obj.isForErp && obj.stockStatus === 2) { ##
  <div class='shiporder-manage_category'>
    <div class='shiporder-manage_category-title'>{{ $t("库存插件") }}</div>
    <div class='shiporder-manage_category-cards'>
      <div class='shiporder-manage_category-card j-card-receive'>
        <div class='shiporder-manage_category-card-title'>{{ $t("成本管理") }}</div>
        <div class='shiporder-manage_category-card-content'>{{ $t("stock.manage.cost.content") }}</div>
        <div class='shiporder-manage_category-card-footer'>
          <div class='shiporder-manage_category-card-options'>
            <div class="shiporder-manage_category-card-option   {{obj.costAdjustmentEnableSwitch == 2 ? 'active j-setting-cost-management' : ''}}">{{ $t("参数设置") }}</div>
          </div>
          <div class='shiporder-manage_category-card-switcher'>
            <i class="oprate-btn_switch j-switch-cost-adjustment {{obj.costAdjustmentEnableSwitch == 2 ? 'oprate-btn_switch--on oprate-btn_switch--disabled' : ''}}"></i>
          </div>
        </div>
      </div>
      ## if (obj.negative_inventory_allowed_plugin_switch) { ##
      <div class='shiporder-manage_category-card j-card-receive'>
        <div class='shiporder-manage_category-card-title'>{{ $t("负库存出库") }}</div>
        <div class='shiporder-manage_category-card-content'>{{ $t("启用负库存出库插件，并在仓库上设置允许负库存出库后，创建出库相关单据时，即使该仓库的实际库存为负，也允许出库") }}</div>
        <div class='shiporder-manage_category-card-footer'>
          <div class='shiporder-manage_category-card-options'>
          </div>
          <div class='shiporder-manage_category-card-switcher'>
            <i class="oprate-btn_switch j-switch-negative-inventory {{obj.negativeInventorySwitch == '1' ? 'oprate-btn_switch--on ' : ''}}"></i>
          </div>
        </div>
      </div>
      ## } ##

      <div class='shiporder-manage_category-card'>
        <div class='shiporder-manage_category-card-title'>{{ $t("函数校验实际库存-调拨单等") }}</div>
        <div class='shiporder-manage_category-card-content'>
          {{ $t("配置自定义函数，作为审批前置条件校验，避免实际库存不足导致库存相关业务单据被作废") }}<br />
          {{ $t("可配置单据包括：调拨单、出库单、采购退货单") }}
        </div>
        <div class='shiporder-manage_category-card-footer'>
          <div class='shiporder-manage_category-card-options j-cf-config kc-left'>
            <div class='shiporder-manage_category-card-option active' data-type='config'>{{ $t("配置函数") }}</div>
            <div class='shiporder-manage_category-card-option active' data-type='flow'>{{ $t("配置流程") }}</div>
            <div class='shiporder-manage_category-card-option active' data-type='guideStock'>{{ $t("查看指引") }}</div>
          </div>
        </div>
      </div>

    </div>

  </div>
  ## } ##
  ## if (obj.isForErp) { ##
  <div class='shiporder-manage_category'>
    <div class='shiporder-manage_category-title'>{{ $t("对接插件") }}</div>
    <div class='shiporder-manage_category-cards'>
      ## if (!obj.isShowRealTimeAvailableStock && obj.erp_real_time_inventory_gray) { ##
      <div class='shiporder-manage_category-card j-card-receive'>
<!--        <div class='shiporder-manage_category-card-title'>{{ $t("ERP实时库存") }}</div>-->
        <div class='shiporder-manage_category-card-title'>{{ $t("stock.stock_manage.info.text8") }}</div>
<!--        <div class='shiporder-manage_category-card-content'>{{ $t("开启后，可在库存对象、批次库存对象的列表页和详情页以及销售订单的新建页，获取ERP实时库存，并基于实时库存进行销售订单的相关校验。") }}</div>-->
        <div class='shiporder-manage_category-card-content'>{{ $t("stock.stock_manage.info.text9") }}</div>
        <div class='shiporder-manage_category-card-footer'>
          <div class='shiporder-manage_category-card-options'>
            <div class="shiporder-manage_category-card-option  shiporder-manage_category-card-option__j-switch-realtimestock {{obj.erp_real_time_inventory.type != 0 ? 'active j-setting-realtimestock' : ''}}">{{ $t("参数设置") }}</div>
          </div>
          <div class='shiporder-manage_category-card-switcher'>
            <i class="oprate-btn_switch j-switch-realtimestock {{obj.erp_real_time_inventory.type != 0 ? 'oprate-btn_switch--on' : ''}}"></i>
          </div>
        </div>
      </div>
      ## } ##
      <div class='shiporder-manage_category-card j-card-receive'>
        <div class='shiporder-manage_category-card-title'>{{ $t("金蝶") }} K3C {{ $t("对接插件") }}</div>
        <div class='shiporder-manage_category-card-content'>{{ $t("开启后，将新增库存明细对象，可接收库存明细数据，并关联对应的库存记录") }}</div>
        <div class='shiporder-manage_category-card-footer'>
          <div class='shiporder-manage_category-card-options'>
            <div class="shiporder-manage_category-card-option  shiporder-manage_category-card-option__k3c-param {{obj.kingDeeK3CSyncPluginSwitch == 1 ? 'active j-setting-k3c' : ''}}">{{ $t("参数设置") }}</div>
          </div>
          <div class='shiporder-manage_category-card-switcher'>
            <i class="oprate-btn_switch j-switch-k3c {{obj.kingDeeK3CSyncPluginSwitch == 1 ? 'oprate-btn_switch--on oprate-btn_switch--disabled' : ''}}"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  ## } ##

  <!-- A类库存和B类库存均可见渠道插件 -->
  ## if (obj.isForErp == false && obj.stockSwitch == 2 || obj.isForErp == true) { ##
  <div class='shiporder-manage_category'>
    <div class='shiporder-manage_category-title'>{{ $t("渠道插件") }}</div>
    <div class='shiporder-manage_category-cards'>

      <div class='shiporder-manage_category-card'>
        <div class='shiporder-manage_category-card-title'>{{ $t("渠道库存管理") }}</div>
        <div class='shiporder-manage_category-card-content'>{{ $t("管理经销商的库存，对经销商的业务进行赋能") }}</div>
        <div class='shiporder-manage_category-card-footer'>
          <div class='shiporder-manage_category-card-options'>
          </div>
          <div class='shiporder-manage_category-card-switcher'>
            <i class="oprate-btn_switch j-switch-distribution-stock {{obj.distribution_stock_switch == 2 ? 'oprate-btn_switch--on oprate-btn_switch--disabled' : ''}}"></i>
          </div>
        </div>
      </div>

    </div>
  </div>
  ## } ##

</div>
