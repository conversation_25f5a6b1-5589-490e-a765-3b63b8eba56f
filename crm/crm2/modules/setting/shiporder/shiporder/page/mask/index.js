define(function(require, exports, module) {
	module.exports = Backbone.View.extend({
		template: require("./tpl-html"),

		initialize: function() {
			this.name = this.options.name;
		},

		render: function() {
			var showMask = this.model.get('showMask');
			var maskTips = this.model.get('maskTips');
            var $target = $(".b-g-con");
            if (showMask) {
                if ($target.find(".j-shiporder-mask").length) {
					$target.find(".j-shiporder-mask").show();
				} else {
					$target.append(this.template({
						tips: maskTips
					}));
				}
            } else {
                $target.find(".j-shiporder-mask").hide();
            }
		},

		destroy: function() {
			this.model = null;
			this.$el.off();
			this.$el.empty();
			this.$el = this.el = this.options = null;
		}
	});
});
