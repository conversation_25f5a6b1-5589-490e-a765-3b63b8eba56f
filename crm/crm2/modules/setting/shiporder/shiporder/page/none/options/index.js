/**
 * @description 启用引导，选择库存版本
 */
define(function (require, exports, module) {
    module.exports = Backbone.View.extend({
        template: require('./tpl-html'),

        events: {
            'click .j-back': '_handleGoback',
            'click .j-shiporder-checkbox': '_handleCheckBox',
            'click .j-confirm': '_handleConfirm'
        },

        initialize: function (options = {}) {
			this.doStockType = options.value;
			this.onBack = options.onBack || $.noop;
			this.onConfirm = options.onConfirm || $.noop;
        },

        data: {
            delivery: true,
            stock: false,
            purchase: false,
            returned: false
        },

        render: function () {
            var data = _.extend({}, this.model.toJSON(), this.data, {
				doStockType: this.doStockType,
			});
            this.$el.html(this.template(data));
        },

        _handleGoback: function () {
            // this.model.set('stockStep', 1);
			this.onBack();
        },

        _handleCheckBox: function (e) {
            var $target = $(e.currentTarget);
            var name = $target.data('name');
            var isChecked = $target.is(':checked')
            this.data[name] = isChecked;
            // 如果库存选项未勾选，则勾选采购或者退换货时，库存选项自动勾选
            if (!this.data['stock']) {
                if (['purchase', 'returned'].indexOf(name) !== -1 && isChecked) {
                    this.data['stock'] = true;
                    $(".j-shiporder-checkbox[data-name='stock']").attr('checked', true);
                }
            }
            // 如果库存取消勾选，则采购和退换货也应该取消勾选
            if (name === 'stock' && !isChecked) {
                var me = this;
                ['purchase', 'returned'].forEach(function(item) {
                    me.data[item] = false;
                    $(".j-shiporder-checkbox[data-name='"+ item +"']").attr('checked', false);
                });
            }
        },

        _handleConfirm: function (e) {
            var $target = $(e.currentTarget);
            var data = {
                enableDeliveryNote: this.data.delivery,
                enableStock: this.data.stock,
                enablePurchase: this.data.purchase,
                enableReturnExchange: this.data.returned,
                // isForErp: +this.model.get('doStockType') === 1
            }
			this.onConfirm(data, $target);
            // this.model.initShipOrder(data, function (){
            //     location.reload();
            // }, $target);
        },

        destroy: function () {
            this.model = null;
            this.$el.off();
            this.$el.empty();
            this.$el = this.el = this.options = null;
        }
    })
})
