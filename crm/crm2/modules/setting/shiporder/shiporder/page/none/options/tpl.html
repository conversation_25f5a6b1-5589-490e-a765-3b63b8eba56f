<div class="shiporder-setting_wrapper">
    <!-- <div class="shiporder-setting_intr">
        <p class="shiporder-setting_intr-title">说明:</p>
        <ul>
            <li class="shiporder-setting_intr-tips">1. 订单审核后，可新建发货单，可维护发货产品和数量，支持分批发货。</li>
            <li class="shiporder-setting_intr-tips">2. 单独开启发货单，未开启库存的情况下，创建发货单不会校验及扣减库存。</li>
            <li class="shiporder-setting_intr-tips">3. 同时开启了发货单和库存的情况下，创建发货单时会校验发货仓库的实际库存，发货单确认后会自动创建出库单，并扣减发货仓库的实际库存。</li>
            <li class="shiporder-setting_intr-tips">4. 发货单一旦开启，不可关闭。</li>
            <li class="shiporder-setting_intr-tips">5. 为保证发货单功能的正常使用，请将移动客户端的版本升级至6.3及以上。</li>
        </ul>
    </div> -->
    <div class="shiporder-setting_options">
        <p class="shiporder-setting_options-title">{{ (Number(obj.doStockType) === 0 ? $t('完整版纷享库存') : $t('对接版纷享库存')) + $t("，请勾选您需要开启的功能") }}</p>
        <div class="shiporder-setting_options-container">
            <div class="shiporder-setting_options-item">
                <div class="shiporder-checkbox">
                    <input class="j-shiporder-checkbox checkbox-disabled" type="checkbox" data-name="delivery" checked disabled>
                    <span>{{ $t("发货单") }}</span>
                </div>
                <p class="shiporder-setting_options-tips">{{ $t("开启后，会初始化发货单对象") }}</p>
            </div>
        </div>
        <div class="shiporder-setting_options-container">
            <div class="shiporder-setting_options-item">
                <div class="shiporder-checkbox">
                    <input class="j-shiporder-checkbox" {{obj.stock ? 'checked' : null}} type="checkbox" data-name="stock">
                    <span>{{ $t("库存") }}</span>
                </div>
                ## if (Number(obj.doStockType) === 0) {##
                    <p class="shiporder-setting_options-tips">{{ $t("开启后，会初始化仓库、库存、入库单、出库单、调拨单、盘点单、出入库明细7个对象") }}</p>
                ## } else { ##
                    <p class="shiporder-setting_options-tips">{{ $t("开启后，会初始化仓库、库存两个对象") }}</p>
                ## } ##
            </div>
            ## if (Number(obj.doStockType) === 0) {##
                <div class="shiporder-setting_options-item child-option">
                    <div class="shiporder-checkbox">
                        <input class="j-shiporder-checkbox" {{obj.purchase ? 'checked' : null}}  type="checkbox" data-name="purchase">
                        <span>{{ $t("采购") }}</span>
                    </div>
                    <p class="shiporder-setting_options-tips">{{ $t("开启后，会初始化供应商、采购订单两个对象") }}</p>
                </div>
            ## } ##
            <div class="shiporder-setting_options-item child-option" style="display: none;">
                <div class="shiporder-checkbox">
                    <input class="j-shiporder-checkbox" {{obj.returned ? 'checked' : null}}  type="checkbox" data-name="returned">
                    <span>{{ $t("退换货") }}</span>
                </div>
                <p class="shiporder-setting_options-tips">$t("开启后，会初始化退换货单、退款单两个对象")</p>
            </div>
        </div>
    </div>
    <div class="shiporder-setting_btns">
        <div class="shiporder-setting_btn-back j-back">{{ $t("返回上一步") }}</div>
        <div class="shiporder-setting_btn-confirm crm-btn-primary j-confirm">{{ $t("确认开通") }}</div>
    </div>
</div>
