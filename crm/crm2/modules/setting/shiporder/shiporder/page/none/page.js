/**
 * @description 未开启库存，选择想开启库存类型
 * @TODO 国际语言适配
 */
define(function (require, exports, module) {
	module.exports = Backbone.View.extend({
		initialize: function () {
			this.selectType = 0;
		},

		render: function () {
			this.renderSelect();
			// this.renderPage();
		},

		renderPage: function () {
			if (this.stockStep === 1) {
				this.renderSelect();
			} else {
				this.renderOptions();
			}
		},

		renderSelect () {
			let me = this;
			let options = this.model.get("hasStockPackage")
				? {
					el: $(".j-stock-con"),
					value: this.selectType,
					title: $t("请根据贵企业的业务需求，选择开启相应版本的库存"),
					options: [
						{
							title: $t("完整版"),
							icon: FS.CRM_MODULE.ASSETS_PATH + "/images/stock/icon-service.png",
							tips: [
								$t('未使用第三方（金蝶、用友、SAP）ERP系统；'),
								$t('只需要管理常规的库存业务，例如库存的出入库、调拨、盘点、批次/序列号、查询明细等；'),
								$t('不需要对库存进行复杂的财务管理；'),
							],
							value: 0,
						},
						{
							title: $t("对接版"),
							icon: FS.CRM_MODULE.ASSETS_PATH + "/images/stock/icon-erp.png",
							tips: [
								$t('已有自研ERP或第三方（金蝶、用友、SAP）ERP系统；'),
								$t('库存的精细化管理程度高，例如会有单据红字冲正、实际库存为负数等；'),
								$t('需要对库存进行成本核算及财务管理；'),
							],
							value: 1,
						},
					],
					onConfirm(value) {
						me.selectType = +value;
						me.renderOptions();
					},
				}
				: {
					el: $(".j-stock-con"),
					value: this.selectType, // 默认选择第一个
					options: [
						{
							title: $t("发货单"),
							icon: FS.CRM_MODULE.ASSETS_PATH + "/images/stock/icon-erp.png",
							tips: [
								$t('发货后可进行物流进度查询，支持市面上所有主流物流公司；'),
								$t('支持一张发货单对多张订单合并发货；'),
								$t('支持对一张订单进行分批发货，支持进行收货确认，并更新相应订单的发货信息；'),
							],
							value: 0,
						},
					],
					btnLabel: $t('开启'),
					onConfirm(value, el) {
						me.selectType = +value;
						me.initShipOrder({
							enableDeliveryNote: true,
							enableStock: false,
							enablePurchase: false,
							enableReturnExchange: false,
							isForErp: me.selectType === 1,
						}, el);
					},
				};

			require.async("./select/index", (Page) => {
				this.page && this.page.destroy();
				let page = new Page(options);
				page.render();
				this.page = page;
			});
		},

		renderOptions: function () {
			var me = this;
			require.async("./options/index", function (Page) {
				me.page && me.page.destroy();
				me.page = new Page({
					el: $(".j-stock-con"),
					model: me.model,
					value: me.selectType,
					onBack() {
						me.renderSelect();
					},
					onConfirm(data, el) {
						me.initShipOrder(_.extend({}, data, {
							isForErp: me.selectType === 1,
						}), el);
					},
				});
				me.page.render();
			});
		},

		initShipOrder(param, el) {
			this.model.set('showMask', true);
			this.model.initShipOrder(param, el)
				.then(() => {
					this.model.set('showMask', false);
					this.model.reload();
				}, err => {
					this.model.set('showMask', false);
					var tip = err && err.errMsg || $t("启用失败请稍后重试或联系纷享客服");
					FS.crmUtil.alert(tip);
					console.log(err);
				})
		},

		destroy: function () {
			this.page && this.page.destroy();
			this.page = null;
			this.remove();
		},
	});
});
