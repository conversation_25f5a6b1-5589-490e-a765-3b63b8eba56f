/**
 * @description 启用引导，选择库存版本
 */
define(function (require, exports, module) {
    module.exports = Backbone.View.extend({
        template: require('./tpl-html'),

        events: {
            'click .shiporder-intr_selection-item': '_handleSelect',
            'click .shiporder-intr_btn-next': '_handleNext'
        },

        initialize: function (options = {}) {
			this.attrData = _.extend({
				title: '',
				options: [],
				btnLabel: $t('下一步'),
			}, _.pick(options, 'title', 'options', 'btnLabel'));
			this.value = options.value;
			this.onConfirm = options.onConfirm || $.noop;
        },

        render: function () {
            this.$el.html(this.template(_.extend({
				value: this.value
			}, this.attrData)));
        },

        _handleSelect: function (e) {
            var $target = $(e.currentTarget);
            var value = String($target.attr('data-value'));
            var oValue = String(this.value);
            if (value !== oValue) {
				this.value = value;
                this.$('.shiporder-intr_selection-item').removeClass('active');
                $target.addClass('active');
            }
        },

        _handleNext: function () {
            // this.model.set('stockStep', 2);
			this.onConfirm(this.value, this.$el.find('.shiporder-intr_btn-next'));
        },

        destroy: function () {
            this.model = null;
            this.$el.off();
            this.$el.empty();
            this.$el = this.el = this.options = null;
        }
    })
})
