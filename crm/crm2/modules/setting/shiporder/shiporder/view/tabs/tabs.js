/**
 * @description 顶部模块切换
 */
define(function (require, exports, module) {
	module.exports = Backbone.View.extend({
		template: require('./tpl-html'),

		options: {
			tabs: [],
            curTab: 0,
		},

		events: {
			'click .j-tab': 'selectHandle',
		},

		render: function () {
			this.$el.html(this.template(this.options));
		},

		selectHandle: function (e) {
			var $target = $(e.currentTarget);
			var index = $target.attr('data-tab') - 0;
			if (index !== this.options.curTab) {
				this.setValue(index, $target);
				this.trigger('change', index);
			}
		},

		setValue: function (index, $el) {
			var $target = $el || this.$('[data-tab='+index+']');
			$target.addClass('cur').siblings('.cur').removeClass('cur');
			this.options.curTab = index;
		},

		destroy: function () {
			this.$el.remove();
		}
	});
});