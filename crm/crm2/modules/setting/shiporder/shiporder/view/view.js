/**
 * @description 管理页面类型
 */
define(function (require, exports, module) {
	var Tabs = require("./tabs/tabs");

	var View = Backbone.View.extend({
		template: require("./tpl-html"),

		options: {
			title: $t("stock.stock_management.stock_management"),
			titleLabel: "",
			tabs: [],
			curTab: 0
		},

		initialize: function (options) {
			this.super = View.prototype;
			this.model = this.options.model;
			this.options = _.extend({}, this.super.options, this.options, options);
			this.options.tabs = this.formatTabs(this.options.tabs);
			this.setElement(options.wrapper);
			this.widgets = {};
		},

		render: function () {
			var options = this.options;
			this.model.set({
				title: options.title,
				titleLabel: options.titleLabel
			});
			this.$el.html(
				this.template({
					tabs: options.tabs
				})
			);
			this.$container = this.$(".j-container");
			this.initTabs();
			this.renderTpl();
		},
		// 格式化
		formatTabs: function (tabs) {
			if (_.isEmpty(tabs) || !_.isArray(tabs) || tabs.length < 2) {
				return [];
			}
			return tabs;
		},
		// 初始化顶部 tab 组件
		initTabs: function () {
			var me = this;
			var options = this.options;
			var curTab = options.getCurTab
				? options.getCurTab()
				: options.curTab || 0;
			this.options.curTab = curTab;
			this.widgets.tabs && this.widgets.tabs.destroy();
			this.widgets.tabs = new Tabs({
				el: me.$(".j-crm-tabs").get(0),
				curTab: curTab,
				tabs: options.tabs
			});
			this.widgets.tabs.render();
			// tab 切换
			this.widgets.tabs.on("change", function (curTab) {
				me.options.curTab = curTab;
				me.renderTpl();
			});
		},
		// 二级页面渲染
		renderTpl: function () {
			var me = this;
			var Page = this.getCurPage();
			this.widgets.page && this.widgets.page.destroy();
			this.widgets.page = new Page({
				el: me.$container.get(0),
				model: me.model
			});
			//  这里的render()，会调用 crm2/modules/setting/shiporder/shiporder/page/manage/index.ts 里的 render 方法
			this.widgets.page.render();
		},

		getCurPage: function () {},
		// 销毁
		destroy: function () {
			this.$el.off();
			this.$el.empty();
			_.each(this.widgets, function (a) {
				a && a.destroy && (a.destroy(), (a = null));
			});
			this.widgets = null;
			this.remove();
		}
	});

	module.exports = View;
});
