/**
 * @file   智能表单详情-流量统计
 * <AUTHOR>
 */

define(function(require, exports, module) {
    var util = require('crm-modules/common/util');
    var tpl = require('./template/datastatistic-html');
    var Table = require('crm-widget/table/table');

    var CRM_SELFOBJECT = {
        text: 1,
        long_text: 1,
        number: 2,
        currency: 3,
        date: 10,
        date_time: 4,
        select: 6,
        select_one: 6,
        record_type: 6,
        select_many: 7,
        checkbox: 7,
        image: 9, //9
        signature: 9,
        true_or_false: 5,
        employee: 8,
        department: 8,
        object_reference: 100,
        master_detail: 100,
        file_attachment: 17,
        quote: 100,
        url: 1,
        location: 39,
        auto_number: 1,
        comment: 1,
        time: 30,
        percentile: 33,
        multi_level_select_one: 11,
        embedded_object_list: 100 //相关团队
    };
    var compare = {
        1: 'EQ', //等于
        2: 'N', //不等于
        3: 'GT', //大于
        4: 'GTE', //大于等于
        5: 'LT', //小于
        6: 'LTE', //小于等于
        7: 'LIKE', //包含
        8: 'NLIKE', //不包含
        9: 'IS', //为空
        10: 'ISN', //不为空
        11: 'LIKE'
    };

    var recompare = _.invert(compare);
    recompare.LIKE = 7;

    var ObjectTable = Backbone.View.extend({

        CRM_SELFOBJECT: CRM_SELFOBJECT,

        options: {
            display_name: '', // 自定义对象显示名称
            error_code: '',   // 状态
            queryParam: null, // 筛选项
            form_fields: []   // 表头列表
        },

        events: {
            'click .status-item':      'getDataByStatusHandle',
            'click .j-import-out':     'importOutHandle'
        },

        initialize: function(data) {
            // TODO: remove
            this.el.innerHTML = tpl();
            this.data = data.data;
            this.render();
        },

        render: function() {
            var me = this;

            if (!me.$el) return;

            me.fetchColunms(function() {
                me.table && me.table.destroy();
                me.table = new Table(me.getOptions());
                me.table.on('trclick', function(data) {
                    me.showDetail(data);
                })
            });
        },

        showDetail: function(obj) {
            var id = obj.object_data_id;
            var apiname = obj.object_describe_api_name;
            if(!id|| !apiname) return;
            var type;
            var crmId;
            if(util.isCrmPreObject(apiname)) {//老对象
                type = {
                    AccountObj: 'customer',
                    LeadsObj: 'saleclue',
                    ContactObj: 'contact'
                }[apiname];
            } else {
                type = 'myobject';
                id = id + ',' + apiname;
            }
            if(!type) return;
            
            FS.MEDIATOR.trigger('crm.detail', {
                type: type,
                data: {
                    crmId: id
                },
                callback: function() {
                    //me.updateTable();
                }
            })
        },

        fetchColunms: function(cb) {
            var me = this;

            me.columnsAjax && me.columnsAjax.abort();
            me.columnsAjax = util.FHHApi({
                url: '/EM1HCRMSMARTFORM/smartform/findFormDataListHeader',
                data: {
                    formDescribeId: me.data.formDescribeId
                },
                success: function(res) {
                    var columns;
                    var rv = res.Value;
                    
                    if (res.Result.StatusCode === 0) {
                        me.options.form_fields = me.parseColumns(rv.describe.fields);
                        me.options.display_name = rv.describe.display_name;
                        me.options.isMyObject = !/Obj$/.test(rv.describe.ref_object_api_name);

                        cb();
                        return;
                    }
                    util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"));
                },
                complete: function() {
                    me.columnsAjax = null;
                },
                error: function() {
                    me.columnsAjax = null;
                }
            }, {
                errorAlertModel: 1
            })
        },

        parseColumns: function(formFields) {
            var columns = [];
            _.each(formFields, function(formField) {
                var type = formField.type;
                var rtype = CRM_SELFOBJECT[type] || 1;
                if (type === 'group') return;
                var tr = {
                    title: formField.label,
                    data: formField.api_name,
                    dataType: rtype,
                    type: type,
                    not_use_multitime_zone: formField.not_use_multitime_zone || false,
                    options: _.map(formField.options || [], function(a) {
                        return {
                            ItemCode: a.value,
                            ItemName: a.label,
                            Children: _.map(a.child_options, function(b) {
                                return {
                                    ItemCode: b.value,
                                    ItemName: b.label
                                }
                            })
                        }
                    }),
                    referRule: rtype === 8 && type !== 'department' && 'Employee',
                    isId: true
                };
                rtype === 1 && (tr.filterCompare = [11, 12]) //去掉开始于结束于
                columns.push(tr);
            })

            return columns;
        },

        getOptions: function() {
            var me = this;

            return {
                $el: me.$('.statistic-table'),
                url: '/EM1HCRMSMARTFORM/smartform/findFormDataList',
                requestType: 'FHHApi',
                zIndex: 501,
                showSize: false,
                showFilerBtn: false,
                showTerm: false,
                showManage: false,
                postData: {},
                isMyObject: me.options.isMyObject,
                columns: me.options.form_fields,
                formatData: function(data) {
                    return me.parseData(data);
                },
                paramFormat: function(param) {
                    return me.parseParam(param);
                }
            }
        },

        parseData: function(obj) {
            var me = this;
            var list = obj.dataList;
            var dataObj;

            _.each(list, function(item) {
                _.each(item, function(v, k) {
                    dataObj = _.findWhere(me.options.form_fields, {data: k}) || {};
                    console.log('@@@@',dataObj)
                    switch (dataObj.type) {
                        case 'select_many':
                            {
                                v && (item[k] = v.join('|'));
                                break;
                            }
                        case 'image':
                        case 'signature':
                            {
                                v && (item[k] = _.map(v, function(a) {
                                    return a.path + '.' + a.ext
                                }).join('|'));
                                break;
                            }
                            // {
                            //     if (v && v.length > 0) {
                            //         var path = v[0].path;
                            //         if (path.slice(path.lastIndexOf('.')) == v[0].ext) {
                            //             item[k] = _.map(v, function(a) {
                            //                 return a.path
                            //             }).join('|');
                            //         }
                            //     }
                            //     break;
                            // }
                        case 'master_detail':
                        case 'object_reference':
                            {
                                item[k] = item[k + '__r'];
                                break;
                            }
                        case 'date':
                        case 'date_time':
                            {
                                if(dataObj.not_use_multitime_zone) {
                                    v && _.isNaN(+v) && (v = (new Date(FS.util.convertTimestampFromTZ8(v * 1))).getTime());
                                } else {
                                    v && _.isNaN(+v) && (v = (new Date(v)).getTime());
                                }
                                item[k] = (v == -1 ? 0 : v);
                                break;
                            }
                        case 'true_or_false':
                            {
                                item[k] = _.isUndefined(v) ? '--' : v ? 'true' : 'false'
                                break;
                            }
                    }
                })
            })
            return {
                totalCount: obj.total,
                data: list
            };
        },

        parseParam: function(obj) {
            var param = {
                formDescribeId: this.data.formDescribeId
            };
            var queryInfo = {
                page: {
                    pageSize: obj.pageSize,
                    pageNumber: obj.pageNumber
                },
                filters: []
            };
            var error_code = this.options.error_code;

            obj.SortField && (queryInfo.orders = [{
                fieldName: obj.SortField,
                isAsc: obj.SortType == 2
            }]);

            if (obj.QueryInfo && obj.QueryInfo.Conditions) {
                queryInfo.filters = _.map(obj.QueryInfo.Conditions, function(a) {
                    return {
                        field_name: a.FieldName,
                        field_values: [a.FilterValue],
                        operator: compare[a.Comparison]
                    }
                })
            }

            if (_.isNumber(error_code)) {
                queryInfo.filters.push({
                    field_name: 'error_code',
                    field_values: [0],
                    operator: error_code ? 'N' : 'EQ'
                })
            }

            param.searchQueryInfo = JSON.stringify(queryInfo);

            this.options.queryParam = param;
            return param;
        },

        // 通过状态筛选用户组
        getDataByStatusHandle: function(e) {
            var $target = $(e.currentTarget);

            if ($target.hasClass('cur')) return;

            $target.addClass('cur')
                .siblings().removeClass('cur');

            this.options.error_code = $target.data('status');
            
            this.table.setParam({}, true, true);
        },

        //导出
        importOutHandle: function(e) {
            var me = this;
            CRM.api.export({
                apiname: me.data.apiname,
                displayName: me.options.display_name,
                queryParam: me.options.queryParam,
                showUniqueID: false,
                showExportfield: false,
                async: false,
                getTokenUrl: '/EM1HCRMSMARTFORM/smartform/exportFormData',
            });

            CRM.util.uploadLog('smartform', 'detail', {
                eventId: 'dataimportout',
                eventType: 'cl'
            });

            e.preventDefault();
        },      

        destroy: function() {
            this.$el.off();
            this.table && this.table.destroy();
            this.importOut && this.importOut.destroy();
            this.table = this.importOut = null;
        }
    })

    module.exports = ObjectTable;
})