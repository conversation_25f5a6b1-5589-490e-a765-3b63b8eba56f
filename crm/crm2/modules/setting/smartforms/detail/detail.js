define(function(require, exports, module) {
    var util  = require('crm-modules/common/util'),
        Slide = require('crm-modules/common/slide/slide'),
        tpl = require('./template/index-html'),
        summaryPage = require('./summary');

    module.exports = Slide.extend({
        options: {
            showMask: false,
            top: 61,
            zIndex: 500,
            className: 'crm-d-smartform crm-d-detail crm-nd-detail',
            entry: 'crm'
        },

        events: {
            'click .j-nav-item': 'toggleContent',
            'click [data-action]': 'actionHandle'
        },

        show: function(formDescribeId) {
            var me = this;
            
            Slide.prototype.show.apply(this);
            this.formDescribeId = formDescribeId;
            this.render(function() {
                me.renderPage(me.$selectedTarget || me.$('.nav-selected'));
            }); 
        },

        render: function(cb) {
            var me = this;
            var formDescribe;

            this.el.innerHTML = tpl();
            this.$container = this.$('.container');
            this.getFormDetail(function(data) {
                me.formDetailData = data;
                formDescribe = data.formDescribe;

                me.$('.display_name').html(formDescribe.display_name);
                me.$('.display_name').attr({title: formDescribe.display_name});
                me.$('.status').addClass(formDescribe.is_active ? 'status1' : 'status2')
                    .html(formDescribe.is_active ? $t("已启用") : $t("已停用"));
                me.$('.status3').html(formDescribe.expire_time && (new Date().getTime() - formDescribe.expire_time > 0) ? $t("已过期") : '');
                (!formDescribe.ref_object_display_name || (formDescribe.ref_object_is_active === false)) && me.$('.b-item').addClass('disabled');

                me.page1 = new summaryPage({
                    el: me.$container,
                    data: me.formDetailData
                });
                cb && cb();
            });
        },

        getFormDetail: function(cb) {
            var me = this;

            util.FHHApi({
                url: '/EM1HCRMSMARTFORM/smartform/findFormDetail',
                data: {
                    formDescribeId: me.formDescribeId,
                    includeStatistics: true,
                    includeCard: true
                },
                success: function(res){
                    if( res.Result.StatusCode == 0 ){
                        cb(res.Value);
                    }else {
                        util.alert(res.Result.FailureMessage);
                    }
                }
            },{
                errorAlertModel: 1
            })
        },

        actionHandle: function(e) {
            var action = $(e.currentTarget).data('action');
            if (action == 'hide') {
                this.hide();
                return;
            }

            var formDescribe = this.formDetailData.formDescribe;
            var param = {
                formDescribeId: formDescribe._id, 
                ref_object_api_name: formDescribe.ref_object_api_name,
                record_type: formDescribe.record_type,
                type: action
            };
             
            if (action == 'refresh') {
                this.refresh();
                return;
            }else {
                if ($(e.currentTarget).hasClass('disabled')) return;
                this.createAction(param);
            }
        },

        createAction: function(param) {
            var me = this;
            CRM.api.smart_form(_.extend({
                api: 'Editor',
                refresh: function() {
                    me.trigger('refresh');
                }
            }, param))
        },

        toggleContent: function(e) {
            var $target = this.$selectedTarget = $(e.currentTarget);

            if ($target.hasClass('nav-selected')) return;
            
            this.renderPage($target);
        },

        renderPage: function($target) {
            var me = this;
            var pageName = $target.data('name');

            me.$('.nav-item').eq(pageName[4] - 1).addClass('nav-selected')
                .siblings().removeClass('nav-selected');
            me.page1 && (me.page1.destroy(), me.page1 = null);
            me.page2 && (me.page2.destroy(), me.page2 = null);
            me.page3 && (me.page3.destroy(), me.page3 = null);
            switch(pageName) {
                case 'page2':
                    require.async('./flowstatistic', function(Page) {
                        me.page2 = new Page({
                            el: me.$container,
                            data: {
                                formDescribeId: me.formDescribeId,
                                display_name: me.formDetailData.formDescribe.display_name,
                                apiname: 'smartform'
                            }
                        });
                    });
                    break;
                case 'page3': 
                    require.async('./datastatistic', function(Page) {
                        me.page3 = new Page({
                            el: me.$container,
                            data: {
                                formDescribeId: me.formDescribeId,
                                apiname: 'smartform'
                            }
                        });
                    });
                    break;
                default: 
                    me.page1 = new summaryPage({
                        el: me.$container,
                        data: me.formDetailData
                    });
            }
        },

        refresh: function() {
            var me = this;

            this.page1 && this.page1.destroy();
            this.page2 && this.page2.destroy();
            this.page3 && this.page3.destroy();
            this.render(function() {
                me.renderPage(me.$selectedTarget || me.$('.nav-selected'));
            });
        },

        //销毁
        destroy: function() {
            var me = this;
            _.each(['action', 'page1', 'page2', 'page3'], function(item){
                me[item] && me[item].destroy();
                me[item] && (me[item] = null);
            });
            Slide.prototype.destroy.call(me);
        }
    });
});
