
.crm-d-smartform{
    .container {
        position: absolute;
        top: 100px;
        bottom: 0;
        width: 100%;
        overflow: auto;
    }

    .crm-loading {
        height: 100%;
    }
    .d-obj-icon {
        background-position: -928px 0;
    }
    .tit .status,
    .status3 {
        display: inline-block;
        width: 44px;
        border-radius: 100px;
        font-size: 10px;
        color: var(--color-neutrals01);
        text-align: center;
        line-height: 18px;
        margin: 0 10px;
    }
    .tit {
        p {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 70%;
        }
        .status,.status3 {
            overflow: hidden;
        }
    }
    .status1 {
        background-color: #7bbb5a;
    }
    .status2 {
        background-color: #9b9b9b;
    }
    .status3 {
        background-color: #ff7663;
    }
    .display_name {
        display: inline-block;
    }
    .b-main-btn.disabled {
        color: #bbb;
        background-color: #eee;
        border: 1px solid #ddd;
        cursor: not-allowed !important;
        &:hover {
            background-color: #eee;
            border-color: #ddd;
        }
    }

    .summary-statistic {
        display: flex;
        margin: 0 15px 10px;
        .item {
            flex: 1;
            display: flex;
            border: 1px solid #ebebeb;
            border-radius: 3px;
            background: var(--color-neutrals01);
            padding: 15px 0 15px 24px;
            margin-right: 16px;
            &:last-child {
                margin-right: 0;
            }
        }
        .icon {
            width: 48px;
            height: 48px;
            margin-right: 16px;
            background-image: url('@{imgUrl}/smartform/all.png');
            background-repeat: no-repeat;
        }
        .icon1 {
            background-position: -283px 0;
        }
        .icon2 {
            background-position: -283px -58px;
        }
        .icon3 {
            background-position: -283px -121px;
        }
        .title {
            color: #999;
        }
        .amount {
            font-size: 20px;
            color: #333333;
        }
    }

	.field-item {
        display: flex;
        margin: 0 15px;
        border-radius: 3px;
        background-color: #f6f9fc;
        .content {
            display: block;
            width: 100%;
            height: 100px;
            margin: 16px 0;
        }
        .content3 {
            padding: 10px;
            height: 80px;
            width: 90%;
        }
    }
    .field-table {
        margin: 0 15px 40px;
    }

    .url-text {
        word-break: break-all;
    }

    .pulish-item {
        flex: 1;
        margin: 16px;
        border-right: 1px solid #e9edef;
        padding-right: 16px;
        &:last-child {
            border-right: none;
            padding-right: 0;
        }
        .pulish-item-title {
            display: flex;
            height: 16px;
            justify-content: space-between;
            align-items: center;
        }
        .select-container {
            position: relative;
            display: inline-block;
            width: 100px;
            .g-select-title-wrapper {
                border-color: transparent;
                background-color: transparent;
            }
        }
    }

    .flowstatistic-header .crm-btn {
        float: right;
        margin-right: 16px;
        padding: 0 25px;
        margin-top: 10px;
    }
    .flow-chart {
        height: 370px;
        margin-right: 160px;
    }
    .flow-table {
        position: absolute;
        top: 474px;
        bottom: 0;
        width: 100%;
    }

    .statistic-header { 
        padding: 9px 20px 10px;
        border-top: 1px solid #d8dde8;
        background-color: #f4f6f9;
        line-height: 28px;
        min-height: 28px;
        background-color: #f4f6f9; 
        .items {
            position: relative;
            float: left;
        }
        .item-tit {
            float: left;
            padding-right: 3px;
            color: #999;
        }
        .cur {
            border-radius: 3px;
            background-color: #77b8ff;
            color: var(--color-neutrals01);
        }
        .status-item {
            display: inline-block;
            height: 28px;
            padding: 0 12px;
            margin-right: 8px;
            text-align: center;
            cursor: pointer;
        }
        .crm-btn {
            float: right;
            padding: 0 25px;
        }
    }
    .statistic-table {
        position: absolute;
        top: 48px;
        bottom: 0;
        width: 100%;
    }
}
