/**
 * @file   智能表单详情-流量统计
 * <AUTHOR>
 */
define(function(require, exports, module) {
    var util  = CRM.util,
        moment = util.moment,
        echarts = require('base-echarts'),
        Table = require('crm-widget/table/table'),
        tpl = require('./template/flowstatistic-html');
   
    module.exports = Backbone.View.extend({
    	initialize: function(data) {
            this.data = data.data;
            this.el.innerHTML = tpl();
            this.initEcharts();
            this.initTable();
        },

        events: {
            'click .j-import-out': 'importOutHandle'
        },

        initEcharts: function() {
            var myChart = echarts.init(this.el.querySelector('.flow-chart'));
            var endDate = new Date(moment().format('YYYY-MM-DD') + 'T00:00:00').getTime() + 86400000;
            var beginDate = endDate - 604800000; // 7*24*3600*1000
            var pvList = [];
            var uvList = [];
            var submitList = [];
            var dayList = [];
            var statisticsList;

            util.FHHApi({
                url: '/EM1HCRMSMARTFORM/smartform/findFormStatisticsByDate',
                data: { 
                    beginDate: beginDate,
                    endDate: endDate,  
                    formDescribeId: this.data.formDescribeId
                },
                success: function(res){
                    if( res.Result.StatusCode == 0 ){
                        statisticsList = res.Value.statisticsList;
                        for(var time = beginDate, statisticData;time < endDate;time = time + 86400000){
                            statisticData = _.findWhere(statisticsList, {access_date: time}) || {};
                            pvList.push(statisticData.pv_count || 0);
                            uvList.push(statisticData.uv_count || 0);
                            submitList.push(statisticData.submit_count || 0);
                            dayList.push(moment(time).format('YYYY-MM-DD'));
                        };

                        var option = {
                            color: ['#00b4f1', '#00cfcd', '#fc923f'],
                            tooltip: {
                                trigger: 'axis'
                            },
                            legend: {
                                data:[$t("浏览量")+' (PV)',$t("独立访客")+' (UV)',$t("提交数")]
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: dayList
                            },
                            yAxis: {
                                type: 'value'
                            },
                            series: [
                                {
                                    name:$t("浏览量")+' (PV)',
                                    type: 'line',
                                    data: pvList
                                },
                                {
                                    name:$t("独立访客")+' (UV)',
                                    type: 'line',
                                    data: uvList
                                },
                                {
                                    name:$t("提交数"),
                                    type: 'line',
                                    data: submitList
                                }
                            ]
                        };
                        myChart.setOption(option);
                    };
                }
            },{
                errorAlertModel: 1
            });
        },

        //初始化表格
        initTable: function() {
            var me = this;
            var statisticsList;

            if (me.dt) {
                me.dt.setParam({}, true);
                return;
            }
            me.dt = new Table({
                $el: me.$('.flow-table'),
                requestType: 'FHHApi',
                url: '/EM1HCRMSMARTFORM/smartform/findFormStatistics',
                showMultiple: false,
                columns: [{
                    data: 'access_date',
                    title: $t("访问时间"),
                    render: function(data) {
                        return data ? moment(data).format('YYYY-MM-DD') : '--';
                    }
                }, {
                    data: 'pv_count',
                    title: $t("浏览量")+' (PV)'
                }, {
                    data: 'uv_count',
                    title: $t("独立访客")+' (UV)'
                }, {
                    data: 'submit_count',
                    title: $t("提交数")
                }],
                paramFormat: function(param) {
                    return me.parseParam(param);
                },
                formatData: function(data) {
                    return {
                        totalCount: data.total,
                        data: data.statisticsList
                    }
                }
            });
        },

        parseParam: function(obj) {
            var param = {
                formDescribeId: this.data.formDescribeId
            };

            param.searchQueryInfo = JSON.stringify({
                page: {
                    pageSize: obj.pageSize,
                    pageNumber: obj.pageNumber
                }
            });
            this.queryParam = param;

            return param;
        },

        //导出
        importOutHandle: function(e) {
            var me = this;

            CRM.api.export({
                apiname: me.data.apiname,
                displayName: me.data.display_name,
                queryParam: me.queryParam,
                showUniqueID: false,
                showExportfield: false,
                async: false,
                getTokenUrl: '/EM1HCRMSMARTFORM/smartform/exportFormStatistics',
            });

            CRM.util.uploadLog('smartform', 'detail', {
                eventId: 'flowimportout',
                eventType: 'cl'
            });

            e.preventDefault();
        },     

        destroy: function() {
            this.$el.off();
            this.dt && this.dt.destroy();
            this.importOut && this.importOut.destroy();
            this.dt = this.importOut = null;
        }
    });
});