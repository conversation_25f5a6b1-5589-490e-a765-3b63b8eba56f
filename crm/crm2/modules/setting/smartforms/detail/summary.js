/**
 * @file   智能表单详情-概览
 * <AUTHOR>
 */
define(function(require, exports, module) {
    var Select = require('crm-widget/select/select'),
        util  = require('crm-modules/common/util'),
        summaryTpl = require('./template/summary-html');
   
    module.exports = Backbone.View.extend({
    	initialize: function(data) {
            var res = data.data;
            this.el.innerHTML = summaryTpl(res);
            this.initSelect(res.card.form_url);

            this.canvas = this.$('canvas')[0];
            var ctx = this.canvas.getContext('2d');
            var img = new Image();
            this.displayName = res.formDescribe && res.formDescribe.display_name;
           
            img.src = res.card.qrcode_url;
            img.onload = function() {
                ctx.drawImage(img, 0, 0, 1000, 1000);
            }
        },

        events: {
            'click .j-downloadimg': 'downloadQrcode',
            'click .j-copyurl': 'copyUrl',
            'click .j-copycode': 'copyCode'
        },

        initSelect: function(url) {
            var me = this;
            var $container = this.$('.content3');

            var jsUrl = ['<script>',
            'var iframe = document.createElement("iframe")',
            'iframe.src = "' + url + '"',
            'document.body.appendChild(iframe)',
            '</script>'].join(';');
            var iFrameUrl = '<iframe src=' + url + '></iframe>';

            $container.text(jsUrl);
            this.select = new Select({
                $wrap: this.$('.j-select'),
                zIndex: 1010,
                stopPropagation: true,
                options:  [{
                        value: 'js',
                        name: 'JavaScript'
                    }, {
                        value: 'iFrame',
                        name: 'iFrame'
                    }]
            });
            this.select.on('change', function(v, data) {
                switch(data.value) {
                    case 'js': 
                        $container.text(jsUrl);
                        break;
                    case 'iFrame': 
                        $container.text(iFrameUrl);
                        break;
                }
            });
        },

        downloadQrcode: function(e) {
            var image = new Image();
            image.src = this.canvas.toDataURL("image/png");

            var $a = $("<a></a>").attr("href", image.src).attr("download", this.displayName || $t('paas.object.smartform.code', null, '智能表单二维码')).appendTo(this.el);
            $a[0].click();
            $a.remove();
            
            CRM.util.uploadLog('smartform', 'detail', {
                eventId: 'downloadqrcode',
                eventType: 'cl'
            });

            e.preventDefault();
            e.stopPropagation();
            return false;
        },

        copyUrl: function(e) {
            var range = document.createRange();
            range.selectNode($(e.target).prev().children()[0]);

            var selection = window.getSelection();
            if(selection.rangeCount > 0) selection.removeAllRanges();
            selection.addRange(range);
            document.execCommand('Copy');
            util.remind(1, $t("复制成功！"));

            CRM.util.uploadLog('smartform', 'detail', {
                eventId: 'copyurl',
                eventType: 'cl'
            });
        },

        copyCode: function(e) {
            $(e.target).prev()[0].select();
            document.execCommand("Copy"); 
            util.remind(1, $t("复制成功！"));

            CRM.util.uploadLog('smartform', 'detail', {
                eventId: 'copycode',
                eventType: 'cl'
            });
        },

        destroy: function() {
            this.$el.off();
            this.select && (this.select.destroy(), this.select = null);
        }
    });
});