##var moment = CRM.util.moment;##
<div class="sec-tit"><h3>{{$t("统计信息")}}</h3></div>
<div class="summary-statistic">
    <div class="item">
        <div class="icon icon1"></div>
        <div>
            <span class="title">{{$t("浏览量")}}(PV)</span>
            <div class="amount"><em>{{statistics.pv_count}}</em></div>
        </div>
    </div>
    <div class="item">
        <div class="icon icon2"></div>
        <div>
            <span class="title">{{$t("独立访客")}}(UV)</span>
            <div class="amount"><em>{{statistics.uv_count}}</em></div>
        </div>
    </div>
    <div class="item">
        <div class="icon icon3"></div>
        <div>
            <span class="title">{{$t("提交总数")}}</span>
            <div class="amount"><em>{{statistics.submit_count}}</em></div>
        </div>
    </div>
</div>

<div class="sec-tit"><h3>{{$t("发布信息")}}</h3></div>

<canvas height="1000" width="1000" class="hide"></canvas>

<div class="field-item b-g-clear">
    <div class="pulish-item">
        <p>{{$t("二维码")}}</p>
        <img src="{{card.qrcode_url}}" class="content" style="width: 100px;">
        <span class="crm-btn crm-btn-cancel j-downloadimg" style="margin-right: 16px;">{{$t("下载二维码")}}</span>
    </div>
    <div class="pulish-item">
        <p>{{$t("访问地址")}}</p>
        <div class="content">
            <a href="{{card.form_url}}" class="url-text" target="_blank">
                {{card.form_url}}
            </a>
        </div>
        <span class="crm-btn crm-btn-cancel j-copyurl">{{$t("复制网址")}}</span>
    </div>
    <div class="pulish-item">
        <p class="pulish-item-title">{{$t("网页嵌入代码")}}<span class="j-select select-container"></span></p>
        <textarea  class="content content3"></textarea>
        <span class="crm-btn crm-btn-cancel j-copycode">{{$t("复制代码")}}</span>
    </div> 
</div>

<div class="sec-tit"><h3>{{$t("详细信息")}}</h3></div>
##var getEmployeeById = function(id){
  var emp = CRM.util.getEmployeeById(id);
  return emp ? emp.fullName : '--';}##
<div class="field-table b-g-clear">
    <div class="crm-d-comp-infolist">
        <div class="i-item b-g-clear">
            <div class="i-l">
                <div class="i-wrap">
                    <div class="tit">{{$t("截止时间")}}</div>
                    <div class="con">{{formDescribe.expire_time ? moment(formDescribe.expire_time).format('YYYY-MM-DD HH:mm:ss') : '--'}}</div>
                </div>
            </div>
            <div class="i-r">
                <div class="i-wrap">
                    <div class="tit">{{$t("创建人")}}</div>
                    <div class="con">{{getEmployeeById(formDescribe.created_by)}}</div>
                </div>
            </div>
        </div>
        <div class="i-item b-g-clear">
            <div class="i-l">
                <div class="i-wrap">
                    <div class="tit">{{$t("创建时间")}}</div>
                    <div class="con">{{formDescribe.create_time ? moment(formDescribe.create_time).format('YYYY-MM-DD HH:mm:ss') : '--'}}</div>
                </div>
            </div>
            <div class="i-r">
                <div class="i-wrap">
                    <div class="tit">{{$t("最后修改人")}}</div>
                    <div class="con">{{getEmployeeById(formDescribe.last_modified_by)}}</div>
                </div>
            </div>
        </div>
        <div class="i-item b-g-clear">
            <div class="i-l">
                <div class="i-wrap">
                    <div class="tit">{{$t("最后跟进时间")}}</div>
                    <div class="con">{{formDescribe.last_modified_time ? moment(formDescribe.last_modified_time).format('YYYY-MM-DD HH:mm:ss') : '--'}}</div>
                </div>
            </div>
            <div class="i-r"><div class="line-white"></div></div>
        </div>
        <div class="i-line"></div>
    </div>
</div>
