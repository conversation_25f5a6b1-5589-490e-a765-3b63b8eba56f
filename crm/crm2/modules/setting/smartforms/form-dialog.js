/**
 * @desc 编辑分类 新建分类
 */
define(function(require, exports, module) {
    var util  = require('crm-modules/common/util'),
        Select = require('crm-widget/select/select'),
        Dialog = require('crm-widget/dialog/dialog');

    var formDialog = Dialog.extend({
        attrs: {
            width:        500,
            height:       390,
            title:       $t("新建表单"),
            showBtns:     true,
            content:     '<div class="crm-g-form crm-g-form-smart-form">' +
                            '<div class="fm-item fm-title" style="line-height:18px;">'+ $t("为了使表单数据能自动生成CRM对象请先选择对象")+'</div>' +
                            '<div class="fm-item">' +
                                '<label class="fm-lb" style="width:80px;line-height:18px">'+ $t("请选择对象")+'</label>' +
                                '<div class="object-select" style="float: left;width:325px;"></div>' +
                            '</div>' +
                            '<div class="fm-item">' +
                                '<label class="fm-lb" style="width:80px;">'+ $t("业务类型")+'</label>' +
                                '<div class="type-select" style="float: left;width:325px;"></div>' +
                            '</div>' +
                            '<div class="fm-item fm-language">' +
                                '<label class="fm-lb" style="width:80px;">'+ $t("选择语言")+'</label>' +
                                '<div class="language-select" style="float: left;width:325px;"></div>' +
                            '</div>' +
                         '</div>' 
        },

        render: function() {
            return formDialog.superclass.render.call(this); 
        },
        
        events: {
            'click .b-g-btn-cancel':  'hide',
            'click .b-g-btn':         'onEnter'
        },
        
        onEnter: function(e) {
            if ($(e.currentTarget).hasClass('b-g-btn-disabled')) return;
            let params = this.typeSelect ? {
                ref_object_api_name: this.objectSelect.getValue(),
                record_type: this.typeSelect.getValue(),
                language: this.languegeSelect.getValue()
            } : {
                ref_object_api_name: this.objectSelect.getValue()
            };
            if(this.isCopy) {
                params.formDescribeId = this.formDescribeId
            }
            this.trigger('success', params);
        },

        show: function(data){
            var result = formDialog.superclass.show.call(this);
            var me = this;
            let { formDescribeId, isCopy, defaultRecordType, defaultLang } = data;

            me.objectSelect && (me.objectSelect.destroy(), me.objectSelect = null);

            this.formDescribeId = formDescribeId
            this.isCopy = isCopy
            this.defaultRecordType = defaultRecordType
            this.defaultLang = defaultLang
            me.objectSelect = new Select({
                $wrap: this.$('.object-select'),  
                // prefix: 'f-g-',
                zIndex: 1010,
                options: data.options,
                defaultValue: data.defaultValue,
                disabled: isCopy
            });
            me.objectSelect.on('change', function(v, item){
               me.getRecordTypeList(item.value);
            });

            me.getRecordTypeList(data.defaultValue);

            let title = isCopy ? $t("复制表单") : $t("新建表单")
            $('.dialog-tit', this.element).html(title);

            return result;
        },

        getRecordTypeList: function(objectApiName) {
            var me = this;

            me.$('.fm-error').remove();
            util.FHHApi({
                url: '/EM1HCRMSMARTFORM/smartform/findValidRecordTypeList',
                data: {
                    objectApiName: objectApiName
                },
                success: function(res){
                    me.$('.fm-item').eq(2).hide();
                    me.$('.fm-item').eq(3).hide();
                    if(_.contains([304601010, 400], res.Result.FailureCode)) {
                        util.showErrmsg(me.$('.object-select'), res.Result.FailureMessage); 
                        me.$('.b-g-btn').addClass('b-g-btn-disabled');
                        return;
                    };

                    if(res.Result.StatusCode) return;

                    if (!res.Value.recordTypeList.length) {
                        me.resizedialog();
                        return;
                    };
                    
                    me.$('.b-g-btn').removeClass('b-g-btn-disabled');
                    me.$('.fm-item').eq(2).show();
                    me.$('.fm-item').eq(3).show();

                    me.typeSelect && (me.typeSelect.destroy(), me.typeSelect = null);
                    me.languegeSelect && (me.languegeSelect.destroy(), me.languegeSelect = null);
                    let defaultRecordType = me.defaultRecordType, 
                        defaultValue = defaultRecordType ? defaultRecordType : res.Value.recordTypeList && res.Value.recordTypeList[0] && res.Value.recordTypeList[0].api_name
                    let defaultLang = me.defaultLang || 'zh-CN';
                    let hasDefaultLang = _.find(res.Value.languages, function(item) {
                        return item.value === defaultLang;
                    });
                    if (!hasDefaultLang) {
                        defaultLang = res.Value.languages[0].value;
                    }
                    me.typeSelect = new Select({
                        $wrap: me.$('.type-select'),  
                        // prefix: 'f-g-',
                        zIndex: 1010,
                        defaultValue: defaultValue,
                        options: _.map(res.Value.recordTypeList, function(item) {
                            return {
                                value: item.api_name,
                                name: item.label,
                            }
                        })
                    });
                    if (!res.Value.enableI18N) {
                        me.$('.fm-language').hide();
                    }
                    me.languegeSelect = new Select({
                        $wrap: this.$('.language-select'),  
                        // prefix: 'f-g-',
                        zIndex: 1010,
                        options: _.map(res.Value.languages, function(item) {
                            return {
                                value: item.value,
                                name: item.displayName,
                            }
                        }),
                        defaultValue: defaultLang
                    });
                    me.resizedialog();
                }
            },{
                errorAlertModel: 1
            })
        },

        hide: function() {
            return formDialog.superclass.hide.call(this);
        },

        destroy: function() {
            var result = formDialog.superclass.destroy.call(this);
            this.objectSelect && this.objectSelect.destroy();
            this.typeSelect && this.typeSelect.destroy();
            this.languegeSelect && this.languegeSelect.destroy();
            return result;
        }
    });

    module.exports = formDialog;
});
