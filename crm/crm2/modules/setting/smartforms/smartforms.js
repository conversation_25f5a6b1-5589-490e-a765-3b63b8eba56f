/**
 * @file 智能表单
 * <AUTHOR>
 */
define(function(require, exports, module) {
    var util  = require('crm-modules/common/util'),
        Table = require('crm-widget/table/table'),
        formDialog = require('./form-dialog');

    module.exports = Backbone.View.extend({


        initialize: function(opts) {
            this.setElement(opts.wrapper);
            this.createEl();
        },

        createEl: function () {
            this.$el.html(
                '<div class="list-view"><div class="crm-loading"></div></div>'
            );
        },

        //渲染
        render: function() {
            var me = this;
            me.getAvailableObjectList(function() {
                me.initTable();
                window.$st = function(data,defaultData) {
                    if(data) {
                            return defaultData
                    }
                    return data
                }
            });
        },

        events: {
            'click .j-add': 'onAdd'
        },

        getAvailableObjectList: function(cb) {
            var me = this;
            me._ajax && me._ajax.abort();
            me._ajax = util.FHHApi({
                url: '/EM1HCRMSMARTFORM/smartform/findAvailableObjectList',
                data: {},
                success: function(res){
                    if( res.Result.StatusCode == 0 ){
                        me.objectList = _.map(res.Value.objectDescribeList, function(item) {
                            return {
                                id: item.api_name,
                                name: item.display_name,
                            }
                        });
                        me.objectApiName = me.objectList[0].id;
                        cb && cb();
                        me._ajax = null;
                    }
                }
            })
        },

        //初始化表格
        initTable: function() {
            var me = this;
            if (me.dt) {
                me.refresh();
                return;
            }
            me.dt = new Table({
                $el: me.$('.list-view'),
                title: $t("智能表单"),
                requestType: 'FHHApi',
                url: '/EM1HCRMSMARTFORM/smartform/findFormList',
                showMultiple: false,
                operate: {
                    pos: 'C',
                    btns: [{
                        text: $t("新建"),
                        className: 'j-add'
                    }]
                },
                search: {
                    placeHolder: $t("搜索智能表单"),
                    type: 'Keyword',
                    highFieldName: 'display_name'
                },
                searchTerm: {
                    pos:    'C',
                    type:   'objectApiName',
                    options: [{id: '', name: $t("全部")}].concat(me.objectList)
                },
                columns: [{
                    data: 'display_name',
                    title: $t("表单名称"),
                    fixed: true,
                    render: function (data, type, full) {
                        return '<a href="javascript:;">' + data + '</a>';
                    }
                }, {
                    data: 'ref_object_display_name',
                    title: $t("所属对象"),
                    width: 200,
                    render: function (data, type, full) {
                        return full.ref_object_display_name ? full.ref_object_display_name + (full.ref_object_is_active === false ? '<span style="color: #fc923f">('+ $t("已禁用") +')</span>' : '') : '-- <span style="color: #fc923f">('+ $t("对象已删除") +')</span>';
                    }
                }, {
                    data: 'record_type_label',
                    title: $t("业务类型")
                }, {
                    data: '',
                    title: $t("截止时间"),
                    render: function(data, name, full) {
                        return full.expire_time ? FS.moment(full.expire_time).format('YYYY-MM-DD HH:mm') : '--';
                    }
                }, {
                    data: 'created_by',
                    title: $t("创建人"),
                    render: function(data) {
                        return me.getEmployeeById(data);
                    }
                }, {
                    data: 'create_time',
                    title: $t("创建日期"),
                    dataType: 4
                }, {
                    data: 'last_modified_time',
                    title: $t("修改日期"),
                    dataType: 4
                }, {
                    data: 'last_modified_by',
                    title: $t("修改人"),
                    render: function(data) {
                        return me.getEmployeeById(data);
                    }
                }, {
                    data: 'is_active',
                    title: $t("状态"),
                    render: function(data) {
                        return data ? $t("已启用") : $t("已停用");
                    }
                }, {
                    data: '',
                    title: $t("操作"),
                    lastFixed: true,
                    width: 220,
                    render: function(data, name, full) {
                        var className = ((full.ref_object_display_name && full.ref_object_is_active === false) || (!full.ref_object_display_name && !full.ref_object_is_active)) ? 'disabled' : '';
                        var is_active = full.is_active;

                        return ['<div class="ops-btns">',
                                    '<a data-operate="edit" class=' + className + '>'+ $t("编辑") +'</a>',
                                    '<a data-operate="preview" class=' + className + '>'+ $t("预览") +'</a>',
                                    '<a data-operate="toggle">' + (is_active ? $t("停用") : $t("启用")) + '</a>',
                                    '<a data-operate="copy" class=' + className + '>'+ $t("复制") +'</a>',
                                    (is_active ? '' : '<a data-operate="del">'+ $t("删除") +'</a>'),
                                '</div>'].join('');
                    }
                }],
                paramFormat: function(param) {
                    return me.parseParam(param);
                },
                formatData: function(data) {
                    return {
                        totalCount: data.total,
                        data: data.formList
                    }
                },
                initComplete:function(){
                    this.$el
						.find(".dt-tit")
						.append(
							[
								'<div class="crm-tip">',
								'<span class="tip-btn"></span>',
								'<div class="tip-text">',
								"<p>&nbsp;&nbsp;" +
									$t(
										"由CRM对象生成的表单以二维码或访问链接的形式开放给外部用户通过扫码或链接的方式打开表单提交表单数据后可自动生成CRM对象的记录数据。"
									) +
									"</p>",
								"<a href='https://help.fxiaoke.com/dbde/33de/b4bb/1186' target='_blank'>" +
									$t(
										"查看帮助文档"
									) +
									"</a>",
								"</div>",
								"</div>",
							].join("")
						);
                },
            });

            me.dt.on('term.change', function(data) {
                me.objectApiName = data;
            });
            me.dt.on('trclick', function(data, $tr, $target) {
                var operate = $target.data('operate');

                if ($target.hasClass('disabled')) return;

                if (operate) {
                    me.operateHandle(operate, data);
                } else {
                    me.showDetail(data._id);
                }

            });
        },



        parseParam: function(obj) {
            var param = {
                objectApiName: obj.objectApiName
            };
            var filters = obj.Keyword ? [{
                field_name: "display_name",
                field_values: [
                    obj.Keyword
                ],
                field_value_type: "text",
                operator: "LIKE"
            }] : [];

            param.searchQueryInfo = JSON.stringify({
                filters: filters,
                page: {
                    pageSize: obj.pageSize,
                    pageNumber: obj.pageNumber
                }
            });

            return param;
        },

        getEmployeeById: function(id) {
            var empty = '--';
            if (!id) {
                return empty;
            }
            var emp = util.getEmployeeById(id);
            return emp ? emp.fullName : empty;
        },

        //显示详情
        showDetail: function(data) {
            var me = this;
            require.async('./detail/detail', function(Detail) {
                if (!me.detail) {
                    me.detail = new Detail();

                    // TODO: 事件回调，后续搞一个全局的
                    me.detail.on('refresh', function() {
                        me.refresh();
                    });
                }
                me.detail.show(data);
            });
        },

        //创建操作对象
        createAction: function(data) {
			var me = this;
			data.api = 'Editor';
            CRM.api.smart_form(_.extend({
                refresh: function(formDescribeId) {
                    me.refresh();
                    if (formDescribeId) {
                        me.showDetail(formDescribeId);
                    };
                }
            }, data));
        },

        //执行操作
        operateHandle: function(operate, data) {
            var postData = {
                formDescribeId: data._id,
                ref_object_api_name: data.ref_object_api_name,
                record_type: data.record_type,
                lang: data.lang,
                type: operate
            };

            switch (operate) {
                case 'edit':
					this.createAction(postData);
                    break;
                case 'preview':
                    this.createAction(postData);
                    break;
                case 'copy':
                    this.onCopy(postData);
                    break;
                case 'toggle':
                    this.onToggle(data);
                    break;
                case 'del':
                    this.onDel(data);
                    break;
            }
        },

        onToggle: function(data) {
            var me = this;

            util.FHHApi({
                url: data.is_active ? '/EM1HCRMSMARTFORM/smartform/disableForm' : '/EM1HCRMSMARTFORM/smartform/enableForm',
                data: {
                    formDescribeId: data._id
                },
                success: function(res){
                    if( res.Result.StatusCode == 0 ){
                        util.remind(1, $t("操作成功！"));
                        me.refresh();
                    }

                    if (data.is_active) {
                        util.uploadLog('smartform', 'list', {
                            eventId: 'disableform',
                            eventType: 'cl'
                        });
                    }
                }
            })
        },

        onCopy: function(data) {
            this.onAdd('copy',data)
        },

        onDel: function(data) {
            var me = this;
            var confirm = util.confirm('<p>'+ $t("确认删除该表单") +'</p>',$t("删除"),function(){
                util.FHHApi({
                    url: '/EM1HCRMSMARTFORM/smartform/deleteForm',
                    data: {
                        formDescribeId: data._id
                    },
                    success: function(res){
                        if( res.Result.StatusCode == 0 ){
                            util.remind(1, $t("操作成功！"));
                            me.refresh();
                            confirm.hide();
                        }
                    }
                })
            });
        },

        onAdd: function(type, params = {}) {
            var me = this;
            me.type = type = typeof type === 'string' ? type : 'add'

            let { formDescribeId, ref_object_api_name, record_type, lang } = params
            util.FHHApi({
                url: '/EM1HCRMSMARTFORM/smartform/checkFormLicense',
                success: function(res){
                    if(res.Result.StatusCode !== 0 || !res.Value.success) return;

                    if (!me.formdialog) {
                        me.formdialog = new formDialog();
                        me.formdialog.on('success', function(data) {
                            me.formdialog.hide();
                            console.log('新建类型', me.type)
                            me.createAction(_.extend({}, data, { type: me.type}));
                        });
                    };
                    me.formdialog.show({
                        options: _.map(me.objectList, function(item) {
                                return {
                                    value: item.id,
                                    name: item.name,
                                }
                            }),
                        defaultValue: ( type === 'copy' ? ref_object_api_name : me.objectApiName ) || me.objectList[0].id,
                        isCopy: type === 'copy',
                        formDescribeId: formDescribeId,
                        defaultRecordType: record_type,
                        defaultLang: lang
                    });
                    util.uploadLog('smartform', 'list', {
                        eventId: 'add',
                        eventType: 'cl'
                    });
                }
            })
        },

        refresh: function() {
            this.dt.setParam({}, true);
        },

        //销毁
        destroy: function() {
            var me = this;
            me._ajax && me._ajax.abort();
            _.each(['dt', 'action', 'detail', 'formdialog'], function(item) {
                me[item] && me[item].destroy && me[item].destroy();
            });
        }
    });
});
