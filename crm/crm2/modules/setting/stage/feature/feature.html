<ul class="crm-rocket-feature">
    <li class="feature_1">
        <div class="feature_1img"></div>
        <span>{{{{-$t('第一步：维护基础信息')}}}}</span>
        <span class="feature-content">{{{{-$t('填写流程名称、流程描述、适用范围、选择阶段')}}}}</span>
    </li>
    <li class="feature-ground"></li>
    <li class="feature_2">
        <div class="feature_2img"></div>
        <span class="feature-content1">{{{{-$t('第二步：维护阶段/任务')}}}}</span>
        <span class="feature-content">{{{{-$t('设置：阶段的"完成条件"和"审批配置" 以及任务的"基础信息"和"完成条件"')}}}}</span>
    </li>
</ul>