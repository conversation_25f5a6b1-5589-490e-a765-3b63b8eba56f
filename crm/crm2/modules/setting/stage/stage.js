/**
 * @desc
 * author qigb
 */
define(function(require, exports, module) {
    var util = require("crm-modules/common/util"),
        Table = require("crm-widget/table/table"),
        featureView = require("./feature/feature");

    var Api = require("./api");

    var Saleaction = Backbone.View.extend({
        initialize: function(opts) {
            var me = this;
            me.setElement(opts.wrapper);
            util.getUserGroups(function(groups) {
                me.groups = groups;
                util.getUserRoles(function(roles) {
                    me.roles = roles;
                    me.initTable();
                });
            });

            var has_showFisrt_rocket_feature = window.localStorage.getItem('has_showFisrt_rocket_feature');
            if (has_showFisrt_rocket_feature != 'firstRocket') {
                me.featureView = new featureView({});
                me.featureView.on('featureClose', function() {
                    window.localStorage.setItem('has_showFisrt_rocket_feature', 'firstRocket');
                });
            }

        },

        events: {
            "click .j-add": "handleAdd"
        },
        // 销售流程列表
        initTable: function() {
            var me = this;
            if (me.dt) return;
            me.dt = new Table({
                $el: me.$el,
                url: "/EM1HSTAGE/Definition/GetDefinitionList",
                tableName: "saleaction",
                showMultiple: false,
                showFilerBtn: false,
                showMoreBtn: false,
                showManage: false,
                showCustom: false,
                title: $t("阶段推进器"),
                search: {
                    placeHolder: $t("搜索阶段推进器名称"),
                    type: "name",
                    highFieldName: "Name"
                },
                requestType: "FHHApi",
                searchTerm: {
                    pos:  'C',
                    type: "enable",
                    options:[{
                        id: 1,
                        isdef: true,
                        name: $t('使用中')
                    }, {
                        id: 3,
                        name: $t('已停用')
                    }]
                },
                columns: [{
                    data: "name",
                    title: $t("阶段推进器名称"),
                    width: 220,
                    fixed: true,
                    render: function(label) {
                        return label;
                    }
                }, {
                    data: null,
                    title: $t("适用范围"),
                    render: function(range, type, full) {
                        var scope = [];
                        _.each(full.rangeCircleIds, function(id) {
                            var cir = util.getCircleById(id);
                            scope.push(cir ? cir.name : '--');
                        });
                        _.each(full.rangeEmployeeIds, function(id) {
                            var emp = util.getEmployeeById(id);
                            scope.push(emp ? emp.name : '--');
                        });
                        _.each(full.rangeGroupIds, function(id) {
                            var group = _.findWhere(me.groups, {
                                id: id
                            });
                            scope.push(group ? group.name : '--');
                        });
                        _.each(full.rangeRoleIds || [], function(id) {
                            var role = _.findWhere(me.roles, {
                                id: id
                            });
                            scope.push(role ? role.name : '--');
                        });
                        if (scope.length) {
                            return scope.join($t("，"));
                        } else {
                            return '--';
                        }
                        // return (
                        //     helper.empCirclesFormat(full.rangeEmployeeIds, {
                        //         referRule: "Employee"
                        //     }) +
                        //     "," +
                        //     helper.empCirclesFormat(full.rangeCircleIds, {})
                        // );
                    }
                }, {
                    data: "creator",
                    title: $t("创建人"),
                    render: function(creator) {
                        return (FS.contacts.getEmployeeById(creator) || {})
                            .name;
                    }
                }, {
                    data: "createTime",
                    title: $t("创建时间"),
                    render: function(create_time) {
                        return FS.moment(create_time).format(
                            "YYYY-MM-DD HH:mm"
                        );
                    }
                }, {
                    data: "modifier",
                    title: $t("最后修改人"),
                    render: function(modifier) {
                        return (FS.contacts.getEmployeeById(modifier) || {})
                            .name;
                    }
                }, {
                    data: "modifyTime",
                    title: $t("最后修改时间"),
                    render: function(modifyTime) {
                        return FS.moment(modifyTime).format(
                            "YYYY-MM-DD HH:mm"
                        );
                    }
                }, {
                    data: "enable",
                    title: $t("状态"),
                    width: 100,
                    render: function(enable) {
                        return [$t("禁用"), $t("启用")][(enable || 0) - 0];
                    }
                }, {
                    data: null,
                    title: $t("操作"),
                    width: 180,
                    lastFixed: true,
                    lastFixedIndex: 1000,
                    render: function(data, type, full) {
                        var html = "";
                        var label = [$t("停用"), $t("启用")][full.enable ? 0 : 1];

                        _.each(
                            [{
                                label: $t("编辑"),
                                value: "Edit"
                            }, {
                                label: label,
                                value: "Enable"
                            }, {
                                label: $t("复制并新建"),
                                value: "CopyAdd"
                            }, {
                                label: full.enable ? "" : $t("删除"),
                                value: "Delete"
                            }],
                            function(item) {
                                html +=
                                    '<a href="javascript:;" style="padding-right: 10px;" data-action="' +
                                    item.value +
                                    '">' +
                                    item.label +
                                    "</a>";
                            }
                        );
                        return html;
                    }
                }],
                operate: {
                    btns: [{
                        text: $t("新建"),
                        className: "j-add"
                    }]
                },

                paramFormat: function(param) {
                    var ret = {
                        page: param.pageNumber,
                        pageSize: param.pageSize,
                        enable: param.enable,
                        name: param.name || ""
                    };
                    return ret;
                },
                formatData: function(data) {
                    return {
                        totalCount: data.total || 0,
                        data: data.data
                    };
                }
            });

            me.dt.on("trclick", function(data, $tr, $target) {
                var action = $target.data("action");
                if (action) {
                    me["handle" + action] && me["handle" + action](data);
                } else {
                    require.async("paas-rocket/detail", function(Detail) {
                        me.detail && me.detail.destroy();
                        me.detail = new Detail({
                            sourceWorkflowId: data.sourceWorkflowId
                        });
                        me.detail.on('refresh', function() {
                            var param = me.dt.getParam();
                            me.dt.setParam(param, true);
                        });
                    })
                }
            });
        },

        handleEnable: function(data) {
            var me = this;
            var enable = !data.enable;
            var label = enable ? $t("启用") : $t("禁用");
            var fn = function() {
                Api.enable({
                        flowid: data.sourceWorkflowId,
                        enable: enable
                    },
                    function() {
                        FS.util.remind(label + $t("成功"));
                        me.dt && me.dt.setParam({}, true);
                    }
                );
            };
            if (enable) {
                fn();
            } else {
                FS.util.confirm($t("确认停用吗"), null, function() {
                    fn();
                    this.destroy();
                });
            }
        },

        // 添加销售流程处理事件
        handleAdd: function(e) {
            var me = this;
            // Api.quota(function(value) {
            // console.log(value);
            me.renderEdit({
                _type: "add"
            });
            // });
        },

        // 编辑
        handleEdit: function(data) {
            var me = this;
            me.renderEdit({
                _type: "edit",
                id: data.sourceWorkflowId
            });
        },

        handleCopyAdd: function(data) {
            var me = this;
            me.renderEdit({
                _type: "copy_add",
                id: data.sourceWorkflowId
            });
        },

        handleDelete: function(data) {
            var me = this;
            var confirm = FS.util.confirm($t("确认删除吗"), null, function() {
                Api.delete(data.sourceWorkflowId, function() {
                    FS.util.remind($t("删除成功"));
                    me.dt && me.dt.setParam({}, true);
                    confirm.hide();
                    //me.destroy();
                });
            });
            // Api.delete(data.sourceWorkflowId, function() {
            //     FS.util.confirm($t("确认删除吗？"), null, function() {
            //         FS.util.remind($t("删除成功"));
            //         me.dt && me.dt.setParam({}, true);
            //         this.destroy();
            //     });
            // });
        },

        //渲染新建编辑页面
        renderEdit: function(options, callBack) {
            var me = this;
            var type = options._type;
            delete options._type;
            require.async("paas-rocket/sdk", function(Sdk) {
                if (!me.rocketInstance) {
                    me.rocketInstance = new Sdk(options);
                    me.rocketInstance.on('refresh', function() {
                        me.dt.setParam({}, true);
                    });
                }

                var cb = function() {
                    $(document.body).append(me.rocketInstance.$el);
                    callBack && callBack();
                };
                if (type == "add") {
                    Api.quota(function(value) {
                        if (value.quota >= value.totalQuota) {
                            return FS.util.alert($t('当前流程总数已达到配额上限{{number}}条', {
                                number: value.totalQuota
                            }));
                        }
                        me.rocketInstance.add();
                        cb()
                    })
                } else if (type == "edit") {
                    Api.get(options.id, function(res) {
                        me.rocketInstance.edit({
                            definition: res
                        });
                        cb()
                    });
                } else if (type == "copy_add") {
                    Api.get(options.id, function(res) {
                        var data = $.extend(true, {}, res);
                        data.name = data.name + $t("副本");
                        //删除source id
                        delete data.sourceWorkflowId;
                        me.rocketInstance.add({
                            definition: data
                        });
                        cb()
                    });
                }
            });
        },
        //渲染详情页
        renderDetail: function(options, callBack) {
            var me = this;
            require.async("paas-rocket/sdk", function(Sdk) {
                var instance = Sdk.renderDetail(options);
                me.sdkInstance = instance;
                callBack && callBack();
            });
        },

        //销毁
        destroy: function() {
            var me = this;
            _.each(["dt", "detail", "_add", "_edit"], function(item) {
                me[item] && me[item].destroy();
            });
            this.sdkInstance && this.sdkInstance.destroy();
        }
    });

    module.exports = Saleaction;
});
