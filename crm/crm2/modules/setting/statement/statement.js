define(function(require, exports, module) {

    var tpl = require('./template/tpl-html'),
        util = require('crm-modules/common/util');

	var Statement = Backbone.View.extend({

		events: {
            'click .mn-radio-item': 'onChoose',
            'click .switch-sec': 'switchOn',
            'click .crm-statement_save button': 'save'
		},
		
		
		initialize: function(options) {
			var me = this;
            me.options = _.extend({
                opened: false,
                receivableBy: 'SalesOrderObj',
                needCrmNotice: false
            }, options);
		
            me.setElement(options.wrapper);
    	},
		
    	render: function() {
			var me = this;
            me.getConfig(function() {
            	me.$el.html(tpl(me.options));
            });
        },
		
        /**
         * @desc 获取规则
         */
        getConfig: function(cb) {
			var me = this;
            util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/statement/service/get_statement_config',
                success: function (data) {
                    if (data.Result.StatusCode === 0) {
                        me.options.opened = data.Value.statementOpened;
                        me.options.receivableBy = data.Value.receivableBy ||  me.options.receivableBy;
                        me.options.needCrmNotice = data.Value.needCrmNotice || me.options.needCrmNotice;
                        cb();
                    }
                }
            });
        },
		
        onChoose: function(e) {
			var me = this;
            var type = $(e.currentTarget).data('type'),
                value = $(e.currentTarget).data('value');
		
            if (type === 'receivableBy') {
                me.options.receivableBy = value;
            } else if (type === 'needCrmNotice') {
                me.options.needCrmNotice = Boolean(value)
            }
        },
		
        save: function() {
			var me = this;
            var data = {
                receivableBy: me.options.receivableBy,
                needCrmNotice: me.options.needCrmNotice
            };
		
            util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/statement/service/update_statement_config',
                data: data,
                success: function (res) {
                    if (res.Result.StatusCode === 0) {
                    	util.remind(1, $t("设置成功"));
                    }
                }
            });
        },
		
        switchOn: function(e) {
			var me = this;
            var $target = $(e.currentTarget);
            if ($target.hasClass('on')) return;
		
            util.confirm($t("确认要启用对账单吗启用后将无法停用"), $t("启用对账单"), function() {
                this.destroy();
                util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/statement/service/enable_statement',
                    data: {},
                    success: function (data) {
                        if (data.Result.StatusCode === 0) {
                            util.remind(1, $t("启用成功"), function() {
                                $target.closest('.on-off').addClass('first-time-on')
                                    .end().addClass('on');
                                $('.crm-statement_switches').addClass('opened');
                            }, 1500);
                        }
                    }
                });
            });
        }
	});

	module.exports = Statement;
});
