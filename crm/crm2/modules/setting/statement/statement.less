.crm-statement {
	.crm-p20 .crm-intro {
		h3 {
			font-size: 16px;
		}
		p {
			font-size: 13px;
		}
	}
	&_content{
		padding: 10px 20px;
		font-size: 14px;
		.crm-ico-qus {
			vertical-align: middle;
			margin-right: 15px;
		}
		.mn-radio-item{
			margin-left: 28px;
			vertical-align: text-bottom;
		}
	}
	&_title{
		margin-bottom: 18px;
		display: inline-block;
		color: #333;
	}
	&_label {
		margin-right: 5px;
	}
	&_name {
		width: 100px;
		display: inline-block;
	}
	.on-off {
		.switch-sec {
			vertical-align: middle;
			display: inline-block;
			position: relative;
			overflow: hidden;
			margin-left: 25px;
			width: 60px;
			height: 28px;
			background: #dee1e6;
			border-radius: 66px;
			text-align: left;
			cursor: pointer;
			transition: all .3s;

			&:before {
				content: '';
				display: inline-block;
				width: 24px;
				height: 24px;
				margin: 2px;
				border-radius: 50%;
				background: var(--color-neutrals01);
				#css3 > .transition(left .1s);
			}

			&:after {
				position: absolute;
				right: 12px;
				content: '';
				line-height: 28px;
				color: var(--color-neutrals11);
				font-size: 14px;
			}
		}
		.on{
			text-align: right;
			cursor: not-allowed;
			&:after {
				left: 12px;
				content: '';
				text-align: left;
			}
		}
		&.first-time-on {
			.switch-sec {
				background: #407fff;
			}
			.on{
				cursor: not-allowed;
				&:after {
					color: var(--color-neutrals01);
				}
			}
		}
	}
	.mn-radio-box, .on-off {
		margin-bottom: 20px;
	}
	&_radio {
		width: 90px;
		display: inline-block;
		margin-left: 30px;
	}
	.mn-radio-item {
		margin-left: 0!important;
		margin-right: 5px;
	}

	&_save {
		margin-top: 40px;
		button {
			background: #3E84E9;
			color: var(--color-neutrals01);
			font-size: 13px;
			border-radius: 4px;
			width: 80px;
			height: 36px;
			line-height: 36px;
			outline: none;
			border: none;
			cursor: pointer;
			&:active {
				background: darken(#3E84E9, 5%);
			}
		}
	}
	&_switches {
		display: none;
		&.opened {
			display: block;
		}
	}

}
