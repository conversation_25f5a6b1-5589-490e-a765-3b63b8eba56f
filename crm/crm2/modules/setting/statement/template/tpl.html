<div class="crm-tit">
    <h2><span class="tit-txt">{{$t("对账单设置")}}</span></h2>
</div>
<div class="crm-module-con crm-scroll crm-statement">
    <div class="crm-p20">
        <div class="crm-intro">
            <h3>{{$t("说明：")}}</h3>
            <p>{{$t("对账单是基于一定周期统计客户代理商的交易往来的数据包括订单退货单回款退款等数据。")}}</p>
            <p>{{$t("当开启互联后代理商经销商可在线对账确认")}}</p>
        </div>
    </div>
    <div class="crm-statement_content">

        <div class="on-off">
            <div class="crm-statement_title">{{$t("启用对账单")}}</div>
            <div class="switch-sec{{ opened ? ' on j-set-on': ' j-set-config'}}">
            </div>
        </div>

        <div class="crm-statement_switches {{ opened ? 'opened' : '' }}">
            <p class="mn-radio-box">
				<span class="crm-statement_name">
					<span class="crm-statement_label">{{$t("应收确认")}}</span>
					<span class="crm-ico-qus" title="{{$t("开启发货单后，才能按发货单确认应收")}}"></span>
				</span>
				<span class="crm-statement_radio">
					<span class="mn-radio-item {{ receivableBy === 'SalesOrderObj' ? 'mn-selected' : '' }}"
						  data-value="SalesOrderObj" data-type="receivableBy">
					</span>
					<span class="crm-statement_label">{{$t("按订单")}}</span>
				</span>
				<span class="crm-statement_radio">
					<span class="mn-radio-item {{ receivableBy === 'DeliveryNoteObj' ? 'mn-selected' : '' }}"
						  data-value="DeliveryNoteObj" data-type="receivableBy">
					</span>
					<span class="crm-statement_label">{{$t("按发货单")}}</span>
				</span>
			</p>

            <p class="mn-radio-box">
				<span class="crm-statement_name">
					<span class="crm-statement_label">{{$t("CRM通知")}}</span>
					<span class="crm-ico-qus" title="{{$t("对账单新建后系统将在后台获取数据生成对账单明细当等待较长时间时可开启CRM通知。待明细创建完成后系统将会发送通知给创建人")}}"></span>
				</span>
				<span class="crm-statement_radio">
					<span class="mn-radio-item {{ needCrmNotice ? 'mn-selected' : '' }}"
						  data-value="1" data-type="needCrmNotice"></span>
					<span class="crm-statement_label">{{$t("需要")}}</span>
				</span>
				<span class="crm-statement_radio">
					<span class="mn-radio-item {{ needCrmNotice ? '' : 'mn-selected' }}"
						  data-value="0" data-type="needCrmNotice"></span>
					<span class="crm-statement_label">{{$t("不需要")}}</span>
				</span>
			</p>

            <div class="crm-statement_save">
                <button>{{$t("保存")}}</button>
            </div>
        </div>
    </div>
</div>
