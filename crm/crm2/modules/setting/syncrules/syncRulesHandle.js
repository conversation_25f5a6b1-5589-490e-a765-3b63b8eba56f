/*
 * @Description: 添加同步规则目前仅支持客户
 * @Author: sunsh
 * @Date: 2021-07-05 16:01:40
 * @LastEditors: sunsh
 * @LastEditTime: 2021-07-05 16:38:24
 */

define(function (require, exports, module) {
	var Dialog = require('crm-widget/dialog/dialog');

	var syncRulesDialog = Dialog.extend({

		attrs: {
			'width': 640,
			'className': 'crm-s-customer-follow',
			'title': $t("添加跟进行为"),
			'showBtns': true,
			'content': '<div class="follow-box"><div class="follow-objTitle">' + $t('所属对象') + '</div><div class="follow-objSelect" style="width:280px;"></div><div class="follow-wrap"></div></div>'
		},

		events: {
			'click .b-g-btn-cancel': 'hide',
			'click .b-g-btn': 'saveData'  						 // 确定按钮事件
		},

		/*
		* 	保存
		* */
		saveData: function () {
			this.trigger('suc', _.omit(this.state, ['filterData']));
			this.hide();
		},

		// 获取对象列表
		getObjectList: function () {
			let _this = this;
			return new Promise(resolve => {
				if (_this.objectList) {
					resolve(_this.objectList);
				} else {
					CRM.util.ajax_base('/EM1HNCRM/API/v1/object/master_data_app/service/object_list', {}).then(res => {
						_this.objectList = _this.mapData(res.result || []);
						_this.objectList = _this.filterApiName(_this.objectList);
						resolve(_this.objectList);
					});
				}
			})
		},

		mapData: function (data) {
			return data.map(item => {
				return {
					name: item.display_name,
					value: item.api_name
				}
			})
		},

		// 过滤掉已有的对象；
		filterApiName: function (data) {
			return _.filter(data, item => !this.state.filterData.includes(item.value))
		},

		/*
    	* 	创建对象单选组件
    	* */
		createObjSelect: async function () {
			let _this = this;
			let ObjectList = await this.getObjectList();
			this.select && this.select.destroy();

			require.async('crm-widget/select/select', function (Select) {
				_this.select = new Select({
					$wrap: _this.$('.follow-objSelect'),
					zIndex: 999,
					width: 280,
					options: ObjectList,
					defaultValue: _this.state.object_api_name || ''
				});
				_this.updateSelectValue(_this.select.getValue());
				_this.select.on('change', function (val) {
					_this.updateSelectValue(val);
				})
			})
		},

		updateSelectValue: function (val) {
			if (!val) return;
			this.setState('object_api_name', val);
			let name = _.find(this.objectList, item => item.value === val).name;
			this.setState('displayName', name);
		},

		// 更新数据;
		setState: function (key, val) {
			this.state[key] = val;
		},

		/*
		* 	显示dialog
		* @ param  type:add/edit （新增/编辑）新增显示对象下拉框，编辑不显示
		* */
		show: function (currentObj, type, filterData) {
			var result = syncRulesDialog.superclass.show.call(this);
			this.type = type;
			this.state = _.extend({}, currentObj || {});
			this.state.filterData = filterData || [];
			if (type === 'add') {
				this.$('.follow-objTitle').show();
				this.createObjSelect();
			}
			this.resizedialog();
			return result;
		},

		hide: function () {
			var result = syncRulesDialog.superclass.hide.call(this);
			this.destroy();
			return result;
		},

		// 销毁
		destroy: function () {
			var result = syncRulesDialog.superclass.destroy.call(this);
			this.select && this.select.destroy();
			return result;
		}
	});

    module.exports = syncRulesDialog;
})
