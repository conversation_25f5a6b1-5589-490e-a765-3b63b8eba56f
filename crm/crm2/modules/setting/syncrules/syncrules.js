/*
 * @Description: 1+n集团主数据管理套件-同步规则（按对象同步）
 * @Author: sunsh
 * @Date: 2021-07-05 14:10:51
 * @LastEditors: sunsh
 * @LastEditTime: 2022-03-29 10:42:52
 * @use: require.async('crm-modules/setting/syncrules/syncrules', function(component) {
            new component({
                el: '.partner-repeatrule-table'
            });
        });
 */
define(function(require, exports, module) {
    let util = CRM.util,
        tpl = require('./template/tpl-html'),
        syncRulesHandle = require('./syncRulesHandle');

    return Backbone.View.extend({
        template: tpl,

        events: {
            'click .add-btn': function(e) {
               this.showSyncRule(); 
            }
        },

        initialize: function(opts) {
            this.$el.html(this.template());
            this.render();
        },

        render: async function() {
            let { result } = await this.getRules();
            this.renderTable(result || []);
        },

        getRules: function() {
            return util.ajax_base('/EM1HNCRM/API/v1/object/master_data_app/service/list', {});
        },

        renderTable: function(dataList) {
            var _this = this;
            this.table && this.table.destroy();
            require.async('crm-widget/table/table', function (Table) {
                // 创建表格
                _this.table = new Table({
                    $el: _this.$('.sync-rules-table'),
                    showPage: false,
                    doStatic: true,
                    colResize: true,
                    columns: [
                        {
                            data: 'display_name',
                            title: $t('对象名称')
                        },
                        {
                            data: null,
                            title: $t('操作'),
                            width: '100px',
                            render: function (data, type, full) {
                                return '<span class="btn js-detail">' + $t('查看详情') + '</span>'
                            }
                        }
                    ]
                });

                _this.table.on('trclick', function (data, $tr, $target) {
                    if ($target.hasClass('js-detail')) {
                        // 目前只有一个客户对象，跳转暂不区分对象
                        location.hash = 'app/shuttles/index';
                    }
                    // if ($target.hasClass('js-stop')) {                        // 停用操作
                    //     _this.handleRule('stop', { api_name: data.api_name });
                    // } else if ($target.hasClass('js-start')) {
                    //     _this.handleRule('start', { api_name: data.api_name });
                    // } else if ($target.hasClass('js-del')) {                     // 删除
                    //     _this.handleRule('delete', { api_name: data.api_name });
                    // }
                });

                _this.table.doStaticData(dataList);
            })
        },

        // 添加同步规则（按对象）
        showSyncRule: function(type = 'add', rowData) {
            var me = this,
                title = $t('添加操作行为');

            me.sr && me.sr.destroy();

            me.sr = new syncRulesHandle({
                title: title
            });

            me.sr.on('suc', function (curObj) {
                if (curObj.object_api_name) {
                    me.handleRule('add', {
                        api_name: curObj.object_api_name,
                    });
                };
            });

            me.sr.show(rowData, type, me.getTableData());
        },

        handleRule: async function(type, param, cb) {
            let url = {
                add: '/EM1HNCRM/API/v1/object/master_data_app/service/add',
                stop: '/EM1HNCRM/API/v1/object/master_data_app/service/stop',
                start: '/EM1HNCRM/API/v1/object/master_data_app/service/start',
                delete: '/EM1HNCRM/API/v1/object/master_data_app/service/delete'
            };

            let res = await util.ajax_base(url[type], param);
            // 根据返回值，自定义处理逻辑
            if (cb) {
                cb(res);
                return;
            }
            // 返回true
            if (res) this.refreshTable();
        },
        
        // 表格当前已有数据；组件内需要过滤掉
        getTableData: function () {
            let apinameList = [];
            _.each(this.table.getCurData() || [], function (item) {
                apinameList.push(item.api_name)
            });
            return apinameList;
        },

        refreshTable: async function () {
            let res = await this.getRules();
            this.table.doStaticData(res.result || []);
        }
    });
});
