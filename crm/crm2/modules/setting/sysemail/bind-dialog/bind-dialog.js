define(function (require, exports, module) {
	const template = `
    <fx-dialog
      :visible.sync="showPanel"
      :title="$t('验证邮箱帐号')"
      custom-class="account-editor"
      width="480px"
      @close="handleDialogClose"
      :close-on-click-modal="false"
      has-scroll
      append-to-body
    >
      <div class="panel-body" v-loading="isLoading">
        <ul class="setting-list">

          <li class="setting-item l__form-line__item">
            <div class="item__label">
              <div class="label required">{{$t('邮箱账号')}}</div>
            </div>
            <div class="item__comp">
              <fx-input
                size="small"
                :placeholder="$t('请输入')"
                v-model="account"
                @change="error.mailAccountErr = ''"
                disabled
                autocomplete="off"
              ></fx-input>
              <p class="error-msg" v-show="error.mailAccountErr">
                <i class="el-icon-warning"></i>
                {{ error.mailAccountErr }}
              </p>
            </div>
          </li>

          <li class="setting-item l__form-line__item">
            <div class="item__label">
              <div class="label required">{{$t('邮箱密码')}}</div>
            </div>
            <div class="item__comp">
              <fx-input
                size="small"
                show-password
                :placeholder="$t('请输入')"
                v-model="password"
                @change="error.password = ''"
                autocomplete="new-password"
                @input.native="handlePswInput"
              ></fx-input>
              <p class="error-msg" v-show="error.password">
                <i class="el-icon-warning"></i>
                {{ error.password }}
              </p>
            </div>
          </li>

          <li class="setting-item l__form-line__item" v-show="useUsername">
            <div class="item__label">
              <div class="label required">{{$t('用户名称')}}</div>
            </div>
            <div class="item__comp">
              <fx-input
                size="small"
                :placeholder="$t('请输入')"
                v-model="username"
                @change="error.username = ''"
              ></fx-input>
              <p class="error-msg" v-show="error.username">
                <i class="el-icon-warning"></i>
                {{ error.username }}
              </p>
            </div>
          </li>
        </ul>
      </div>
      <span slot="footer" class="dialog-footer">
  			<fx-checkbox style="flex: 1" v-model="useUsername">{{$t('使用用户名验证')}}</fx-checkbox>
        <fx-button type="primary" @click="handleSubmit" size="small">
          {{ $t('保存') }}
        </fx-button>
        <fx-button @click="hidePanel" size="small">{{ $t('取消') }}</fx-button>
      </span>
    </fx-dialog>
  `;
	const util = require("crm-modules/common/util");
	const Editor = Vue.extend({
		name: "account-editor",
		template,
		props: {
			account: {
				type: String,
				default: "",
			},
			pUsername: {
				type: String,
				default: "",
			},
		},
		data() {
			return {
				showPanel: true,
				isLoading: false,
				password: "",
				username: "",
				useUsername: false,
				error: {
					password: "",
					username: "",
				},
			};
		},
		methods: {
			handleDialogClose() {
				this.$emit("cancel");
			},
			allowSubmit() {
				let result = true;
				if (!this.password) {
					this.error.password = $t("请输入邮箱密码");
					result = false;
				}
				if (this.useUsername && !this.username) {
					this.error.username = $t("请输入用户名称");
					result = false;
				}

				return result;
			},
			submitEditingData() {
				if (this.submittingData) return Promise.reject();
				if (!this.allowSubmit()) return Promise.reject();

				this.isLoading = true;
				this.submittingData = true;
				return new Promise((resolve, reject) => {
					util.api(
						{
							url: "/FHH/EM1HEMAILPROXY/emailproxy/changePassword",
							data: {
								account: this.account,
								password: this.password,
								userName: this.useUsername ? this.username : "",
								isSystemEmail: true,
							},
							success: (res) => {
								if (res.Value) {
									Object.assign(res, res.Value)
								}
								this.isLoading = false;
								this.submittingData = false;
								if (res && res.errorCode === 0) {
									resolve();
									return;
								}

								FS.util.alert(res.errorMessage);
								reject();
							},
						},
						{
							errorAlertModel: 1,
							autoPrependPath: false,
						}
					);
				});
			},
			handleSubmit() {
				this.submitEditingData().then(() => {
					this.$emit("submit");
					this.hidePanel();
				});
			},
			hidePanel() {
				this.$emit("cancel");
			},
			handlePswInput: _.debounce(function () {
				const val = this.password;
				const reg = /[\u3a00-\ufa99]/g;
				if (reg.test(val)) {
					console.log(val);
					this.$set(this, "password", val.replace(reg, ""));
				}
			}, 200),
			loadData() {
				this.username = this.pUsername;
				this.useUsername = !!this.pUsername;
			},
		},
		created() {
			this.loadData();
		},
	});

	Editor.$show = (propsData) => {
		return new Promise((resolve, reject) => {
			const $vm = new Editor({
				el: document.createElement("div"),
				propsData,
			});

			$vm.$on("hide", (...args) => {
				$vm.showPanel = false;
				setTimeout(() => {
					$vm.$destroy();
					$vm.$el.remove();
				}, 1000);
			});

			$vm.$on("submit", (...args) => {
				$vm.$emit("hide");
				resolve(...args);
			});
			$vm.$on("cancel", (...args) => {
				$vm.$emit("hide");
				reject(...args);
			});

			$("body").append($vm.$el);
			setTimeout(() => {
				$vm.showPanel = true;
			}, 20);
			$("body").append($vm.$el);
		});
	};

	module.exports = Editor;
});
