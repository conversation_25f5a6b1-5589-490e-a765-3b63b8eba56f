.account-editor {
  .error-msg {
    color: red;
    .el-icon-warning {
      margin-right: 5px;
    }
  }
  .el-dialog__body {
    padding: none;
  }
  .panel-body {
    padding-bottom: 16px;
    .setting-list {
      box-sizing: border-box;
      width: 100%;
      .setting-item {
        display: flex;
        align-items: center;
        .item__label {
          flex-basis: 125px;
          .label {
            &.required::before {
              content: '*';
              color: red;
            }
          }
        }
        .item__comp {
          flex: 1;
        }
        &.setting-opera-item {
          margin-top: 34px;
        }
        &.server-item {
          .item__comp {
            .item-comp {
              display: flex;
              align-items: center;
              .comp-item {
                &.server-input {
                  flex: 1 1 129px;
                }
                &.host-type {
                  margin: 0 6px 0 8px;
                }
                &.host-input {
                  flex: 1 1 64px;
                }
              }
            }
          }
        }
      }
      .setting-item + .setting-item {
        margin-top: 10px;
      }
    }
    .right {
      padding: 0 24px;
      border-left: 1px solid var(--color-neutrals07);
      .title {
        margin-bottom: 18px;
        line-height: 18px;
        font-weight: bold;
        font-size: 14px;
        color: var(--color-neutrals19);
      }
      .r-item {
        margin-bottom: 17px;
        font-size: 12px;
      }
    }
  }
  .dialog-footer {
    display: flex;
    align-items: center;
    text-align: left;
  }
}