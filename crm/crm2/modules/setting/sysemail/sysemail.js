/**
 * @desc 对象映射规则
 */
 define(function(require,exports,module) {

	var util = require('crm-modules/common/util'),
			tpl  = require('./template/tpl-html');
	
	const accountBinder = require('./bind-dialog/bind-dialog');

	const SUBORDINATES_STATUS_MAP = {
		0: $t("不可查看下级邮件"),
		1: $t("所有下级邮件"),
		2: $t("直属下级邮件")
	}

	var ObjMap = Backbone.View.extend({

		initialize: function(opts) {
			// this.initOptionData();
			this.setElement(opts.wrapper);
		},
		initOptionData: function() {
			this.options.emailConfig = {
				isForceSSL: true,
			}
		},
		// 状态-5 绑定成功
		render: function() {
			this.initDefaultData()
			this.initSystemMail()
			// this.uploadData().then(() => {
			// 	this.renderSwitch();
			// })
		},
		initDefaultData() {
			this.comps = {}		
		},
		initSystemMail($root) {
			$root || ($root = this.el);
			require.async('app-standalone/app', (StandAloneApp) => {
				StandAloneApp.getModuleComp('system-mail/system-mail-vue.js').then(SystemMail => {
					const systemMailInstance = new SystemMail({
						el: document.createElement('div')
					})
					this.comps.systemMailInstance = systemMailInstance;
					$root.append(systemMailInstance.$el);
				})
			})
		},

		uploadHtml(extraData) {
			this.$el.html(tpl(_.extend({
				account: '',
				nickname: '',
				signature: '',
				passwordErr: false,
			}, this.options, extraData)));
			$('.crm-see-sub-email .setting-result__value').html(SUBORDINATES_STATUS_MAP[this.options.subordinatesStatus]);
		},
		uploadData() {
			const me = this;
			return Promise.all([me._getEmailSet(), me.getViewStatus(), me.loadEmailConfig()]).then(([data]) => {
				var isBind = [5,2].includes(+data.status);
				me.options.passwordErr = data.status == 2;
				me.options.username = data.userName;
				me.uploadHtml();

				$('.flowemail-show-box', me.$el).toggle(isBind);
				$('.flowemail-set-box', me.$el).toggle(!isBind);
				$('input:radio[name="crm-email-see"]').change(function() {
					me.changeValue = this.value;
					// me.setViewStatus(this.value)
					me.$el.find('.crm-see-email-save').removeClass('b-g-btn-disabled');
				});
				
			});
		},
		events: {
			'click .j-set': 'onManualSet',
			'click .j-unbind': 'onUnbindEmail',
			'click .j-account-setting': 'onAccountSetting',
			'click .j-setItem': 'onItemSet',
			'click .j-cancelItem': 'onItemCancel',
			'click .j-sureItem': 'onItemSure',
			'click .j-validate': 'onSubmit',
			'click .j-crm-set-email':'onSetEmailFun',
			'click .j-use-username': 'onUseUsernameChange',
			'click .sedi-mes__lt':'onClickSediChoice',
			'click .crm-see-email-save':'onSaveFun',
			'click .change-official__link-btn' : 'showOfficialSetting',
			'click .crm-see-sub-email__submit-setting' : 'submitOfficialSetting',
			'click .crm-see-sub-email__cancel-setting' : 'exitOfficialSetting',
			'blur .flowemail-set-box input[name="account"]' : 'checkAccount',
			'blur .flowemail-set-box input[name="password"]' : 'checkPassword',
			'blur .flowemail-set-box input[name="username"]' : 'checkUsername',
			'blur .flowemail-set-box input[name="host"]' : 'checkManualSet',
			'blur .flowemail-set-box input[name="port"]' : 'checkManualSet',
		},
		onUseUsernameChange() {
			if (!this.options.useUsername) {
				$('.j-use-username .checkbox-input').attr('checked', true);
				$('.set-item.username').removeClass('hide');
			} else {
				$('.j-use-username .checkbox-input').attr('checked', false);
				$('.set-item.username').addClass('hide');
			}
			this.options.useUsername = !this.options.useUsername;
			this.checkUsername();
		},
		showOfficialSetting() {
			this.renderRadioGroup();
			$('.crm-see-sub-email').hasClass('setting-official') || $('.crm-see-sub-email').addClass('setting-official');
		},
		submitOfficialSetting() {
			this.setViewStatus(this.comps['customer_radio-group'].subordinatesStatus).then(() => {
				this.options.subordinatesStatus = this.comps['customer_radio-group'].subordinatesStatus;
				$('.crm-see-sub-email .setting-result__value').html(SUBORDINATES_STATUS_MAP[this.options.subordinatesStatus]);

				this.exitOfficialSetting();
			})
		},
		exitOfficialSetting() {
			$('.crm-see-sub-email').removeClass('setting-official');
		},
		renderRadioGroup() {
			let me = this;
			this.comps || (this.comps = {})
			this.comps['customer_radio-group'] && this.comps['customer_radio-group'].destroy();
			this.comps['customer_radio-group'] = FxUI.create({
				wrapper: '.fx-radio-group',
				template: `
					<fx-radio-group 
						size="small"
						v-model="subordinatesStatus"
					>
						<fx-radio :label="0">{{$t("不可查看下级邮件")}}</fx-radio>
						<fx-radio :label="1">{{$t("所有下级邮件")}}</fx-radio>
						<fx-radio :label="2">{{$t("直属下级邮件")}}</fx-radio>
					</fx-radio-group>
				`,
				data() {
					return {
						subordinatesStatus: me.options.subordinatesStatus
					}
				},
			})
		},
		renderSwitch() {
			let me = this;
			this.comps || (this.comps = {})
			this.comps['customer_switch'] = FxUI.create({
				wrapper: '.fx-switch',
				template: `
					<fx-switch 
						size="mini"
						:before-change="beforeChange"
						:disabled="options.isOpenedAutoAssociation"
						v-model="options.isOpenedAutoAssociation"
					></fx-switch>
				`,
				data() {
					return {
						options: me.options
					}
				},
				methods: {
					beforeChange() {
						const tipsTemp = `
							<p class="sys-email__auto-association__open-tips__header">${$t('确定要开启吗？')}<p>
							<p class="sys-email__auto-association__open-tips__desc">${$t('开启后将创建邮件对象，且此功能不能关闭')}</p>
						`
						return this.$alert(tipsTemp, $t("提示"), {
								type:'warning',
								showClose: false,
								showCancelButton: true,
								dangerouslyUseHTMLString: true,
						})
						.then(me.autoSedimentFun.bind(me))
						.then(me.queryEmailSedimentFun.bind(me))
					},
				},
				beforeCreate() {
					me.options.isOpenedAutoAssociation || (me.options.isOpenedAutoAssociation = false)
				},
			})
		},
    	onManualSet: function(e) {
    		$(e.currentTarget).closest('.flowemail-set-box').toggleClass('all-set');
    	},

		/**
		 * @method 查询上级查看下级的状态
		 */
		getViewStatus:function(){
			var me = this ;
			return new Promise((resolve, reject) => {
				util.FHHApi({
					 url: '/EM1HCRMTemplate/emailAttributesStatusApi/getViewSubordinatesStatus',
					 success: function(res) {
						 if(res.Result.StatusCode == 0) {
							 if(res.Value.code == 0) {
								 const status = +(res.Value.result || 0);
								 me.options.subordinatesStatus = status;
								 resolve()
								//  $("input[name='crm-email-see'][value='"+res.Value.result+"']",me.$el).attr("checked",true)
							 }
							 return;
						 }
						 reject()
						 util.alert(res.Result.FailureMessage);
					 }
				 }, {
					 errorAlertModel: 1
				 });
			})
		},
		
		/**
		 * @method 获取邮件配置
		 */
		loadEmailConfig() {
			return new Promise((resolve, reject) => {
				util.api({
					url: '/FHH/EM1HEMAILPROXY/emailproxy/isBindEmailForceBySsl',
					success: (res) => {
						if (res.Value) {
							Object.assign(res, res.Value)
						}
						this.options.emailConfig.isForceSSL = !!res.data;
						resolve()
					}
				}, 
				{
					errorAlertModel: 1,
					autoPrependPath: false,
				});
			})
		},
		/**
		 * @method 设置可查看下级邮件的范围
		 */
		 setViewStatus:function(value){
			 var me = this ;
			 return new Promise((resolve, reject) => {
				  util.FHHApi({
				  url: '/EM1HCRMTemplate/emailAttributesStatusApi/setViewSubordinatesStatus',
					data:{
						openStatus:Number(value)
					},
					success: function(res) {
						if(res.Result.StatusCode == 0) {
							if(res.Value.code === 0) {
								resolve()
								util.remind(1,res.Value.msg);
								me.$el.find('.crm-see-email-save').addClass('b-g-btn-disabled');
							}
							return;
						}
						reject()
						util.alert(res.Result.FailureMessage);
					}
				}, {
					errorAlertModel: 1
				});
			 })
		 },

		/**
		 * @method 选择是否启用邮件沉淀功能
		 */
		 onClickSediChoice:function(e){
			 var me = this ;
			 if(!$(e.target).hasClass('disabled-selected')){
				 //$(e.target).addClass('sedi-mes__lt_choiced');
				 var confirm = util.confirm($t("开启后将无法关闭确定要开启吗"),$t("提示"),function(){
					 confirm.hide();
					 $(e.target).addClass('disabled-selected');
					 $('.sedi-mes__P1',me.$el).addClass('crm-choiced-iden');
					 //设置保存按钮可点击
					 me.$el.find('.crm-see-email-save').removeClass('b-g-btn-disabled');
					 //设置标识，使其在点击保存按钮的时候请求自动沉淀邮件的接口
					 me.ifRequestEmailSediment = true;
					 //me.autoSedimentFun();
				 });
			 }
		 },

		 /**
		  * @method 保存设置功能
		  */
		  onSaveFun:function(){
			  var me = this ;
			  if(me.changeValue){
				  me.setViewStatus(me.changeValue);
			  }
			  if(me.ifRequestEmailSediment){
				  me.autoSedimentFun();
			  }
		  },
		onAccountSetting() {
			accountBinder.$show({
				account: this.options.account,
				pUsername: this.options.username
			}).then(() => {
				this.render()
			})
		},
		/**
		 * @param 设置邮件自动沉淀接口
		 */
		 autoSedimentFun:function(){
			 var me = this;
			 return new Promise((resolve, reject) => {
					util.FHHApi({
						url: '/EM1HCRMTemplate/emailAttributesStatusApi/setAutoAssociationStatus',
						success: function(res) {
							if(res.Result.StatusCode == 0) {
								if(res.Value.code === 0) {
									resolve()
									util.remind(1, $t("设置成功！"));
									me.ifRequestEmailSediment = false;
									me.$el.find('.crm-see-email-save').addClass('b-g-btn-disabled');
								} else {
									reject()
									util.remind(3, $t("设置失败!"));
									$('.sedi-mes__lt',me.$el).removeClass('disabled-selected');
									$('.sedi-mes__P1',me.$el).removeClass('crm-choiced-iden');
								}
								return;
							}
							util.alert(res.Result.FailureMessage);
						}
					}, {
						errorAlertModel: 1
					});
			 })
		 },
		/**
		 * @param 切换邮箱设置和邮件沉淀
		 */
		 onSetEmailFun:function(e){
			 	var me = this ;
				var width = $(e.target).width();
			 	$('.crm-set-mail-email',me.$el).addClass('hide');
				$('.j-crm-set-email', me.$el).removeClass('cur');
				$(e.target).addClass('cur');
			 if(!$(e.target).hasClass('crm-set-sedi__email')){
				 //  $('.crm-set-active__email',me.$el).css({
				 //     width: width,
				 //     left: $(e.target).offset().left-200
				 //  });
				 $('.crm-p20', me.$el).removeClass('hide');

				 this.exitOfficialSetting();
			 }else{
				 //  $('.crm-set-active__email',me.$el).css({
				 //     width: width,
				 //     left: $(e.target).offset().left-180
				 //  });
				 $('.crm-email-sediment', me.$el).removeClass('hide');
				 me.queryEmailSedimentFun();
			 }
		 },
		 /**
		  * @param 查询自动沉淀的状态
		  */
		queryEmailSedimentFun:function(){
			var me = this ;
			util.FHHApi({
			   url: '/EM1HCRMTemplate/emailAttributesStatusApi/getAutoAssociationStatus',
			   success: function(res) {
				   if(res.Result.StatusCode == 0) {
					   if(res.Value.result){
							 me.options.isOpenedAutoAssociation = !!res.Value.result
						   $('.sedi-mes__lt',me.$el).addClass('disabled-selected');
						   $('.sedi-mes__P1',me.$el).addClass('crm-choiced-iden');
					   }
					   return;
				   }
				   util.alert(res.Result.FailureMessage);
			   }
		   }, {
			   errorAlertModel: 1
		   });
	   },
    	//解绑邮箱
    	onUnbindEmail: function() {
    		var me = this;
    		util.FHHApi({
    			url: '/EM1HCRMTemplate/workflowEmailSet/unBind',
    			success: function(res) {
    				if(res.Result.StatusCode == 0) {
    					if(res.Value) {
    						util.remind(1, $t("解绑成功！"));
    						me.render();
    					}else{
    						util.remind(3, $t("解绑失败")+'！');
    					}
    					return;
    				}
    				util.alert(res.Result.FailureMessage);
    			}
    		}, {
    			errorAlertModel: 1
    		});
    	},

    	//设置
    	onItemSet: function(e) {
    		var $target = $(e.currentTarget);
    		var $setItem = $target.closest('.set-item');
    		var nickname = $setItem.find('.text').html();
    		var signature = $setItem.find('.text').html();
    		$setItem.addClass('set-item-edit');
    		$setItem.find('input').val(nickname==$t("无")?'':nickname);
    		$setItem.find('textarea').val(signature==$t("无")?'':signature);
    	},

    	//确认
    	onItemSure: function(e) {
    		var me = this;
    		var $target = $(e.currentTarget);
    		var $setItem = $target.closest('.set-item');
    		var type = $setItem.data('type');
    		var nickname = null, signature = null;

    		if(type == 'nickname') {
    			nickname = $setItem.find('input').val();
    		}else if(type == 'signature'){
    			signature = $setItem.find('textarea').val();
    		}

    		util.FHHApi({
    			url: '/EM1HCRMTemplate/workflowEmailSet/updateAccountInfo',
    			data: {
    				nickname: nickname,
    				signature: signature
    			},
    			success: function(res) {
    				if(res.Result.StatusCode == 0) {
    					if(res.Value == 1) {
    						$setItem.removeClass('set-item-edit');
    						$setItem.find('.text').html(nickname || signature || $t("无"));
    					}
    					return;
    				}
    				util.alert(res.Result.FailureMessage);
    			}
    		}, {
    			errorAlertModel: 1
    		});
    	},

    	//取消
    	onItemCancel: function(e) {
    		var $target = $(e.currentTarget);
    		$target.closest('.set-item').removeClass('set-item-edit');
    	},
			checkAccount: function() {
    		const $box = $('.flowemail-set-box', this.$el);
				const $account = $('.account', $box);
				if (!$('[name=account]', $account).val()) {
					$('.error-msg', $account).show();
					$('.error-msg span', $account).text($t('请输入邮箱账号'));
					$account.addClass('has-error');
				} else {
					$('.error-msg', $account).hide();
					$account.removeClass('has-error');
				}
			},
			checkPassword: function() {
    		const $box = $('.flowemail-set-box', this.$el);
				const $password = $('.password', $box);
				if (!$('[name=password]', $password).val()) {
					$('.error-msg', $password).show();
					$('.error-msg span', $password).text($t('请输入邮箱密码'));
					$password.addClass('has-error');
				} else {
					$('.error-msg', $password).hide();
					$password.removeClass('has-error');
				}
			},
			checkUsername: function() {
    		const $box = $('.flowemail-set-box', this.$el);
				const $username = $('.username', $box);
				if (this.options.useUsername && !$('[name=username]', $username).val()) {
					$('.error-msg', $username).show();
					$('.error-msg span', $username).text($t('请输入用户名称'));
					$username.addClass('has-error');
				} else {
					$('.error-msg', $username).hide();
					$username.removeClass('has-error');
				}
			},
			checkManualSet: function() {
    		const $box = $('.flowemail-set-box', this.$el);
				const $manualSet = $('.manual-set', $box);
    		const isNeedManaul = $('.flowemail-set-box', this.$el).hasClass('all-set');
				const host = $('[name=host]', $box).val();
    		const port = $('[name=port]', $box).val();
				if (isNeedManaul && (!host || !port)) {
					$('.error-msg', $manualSet).show();
					$('.error-msg span', $manualSet).text($t('请输入完整SMTP服务器信息'));
					$manualSet.addClass('has-error');
				} else {
					$('.error-msg', $manualSet).hide();
					$manualSet.removeClass('has-error');
				}
			},
			checkAllowSubmit: function() {
				this.checkAccount();
				this.checkPassword();
				this.checkUsername();
				this.checkManualSet();
				const $error = $('.flowemail-set-box .has-error');
				return !$error || $error.length === 0;
			},
    	onSubmit: function() {
				if (!this.checkAllowSubmit()) {
					return;
				}
    		var me = this;
    		var url = '';
    		var postData = {};
    		var $box = $('.flowemail-set-box', this.$el);
    		var account = $('[name=account]', $box).val();
    		var password = $('[name=password]', $box).val();
    		var isSsl = $('.is-ssl', $box).hasClass('mn-selected');
    		var username = this.options.useUsername ? $('[name=username]', $box).val() : '';
    		var host = $('[name=host]', $box).val();
    		var port = $('[name=port]', $box).val();
    		var isNeedManaul = $('.flowemail-set-box', this.$el).hasClass('all-set');
    		
				if(isNeedManaul) {
    			// if(account == '' || password == '' || isSsl == '' || host == '' || port == '') {

    			// 	return;
    			// }
    			url = '/EM1HCRMTemplate/workflowEmailSet/emailManualBind';
    			postData.account = account;
    			postData.password = password;
					postData.userName = username;
    			postData.isSendSsl = isSsl ? 1 : 0;
    			postData.smtpHost = host;
    			postData.smtpPort = +port;
    		}else{
    			url = '/EM1HCRMTemplate/workflowEmailSet/emailBind';
    			postData.account = account;
    			postData.password = password;
					postData.userName = username;
    		}
    		util.FHHApi({
    			url: url,
    			data: postData,
    			success: function(res) {
    				if(res.Result.StatusCode == 0) {
    					// if(isNeedManaul) {
    					// 	util.alert(['','认证成功!','密码错误!','未绑定!','认证失败!'][res.Value]);
    					// }else{
    					// 	util.alert(['','认证成功!','认证失败，请手动绑定!'][res.Value]);
    					// }
    					if(res.Value == 2) {
								$('.j-set').click();
								return;
    					}
							me.render();
    					return;
    				}
    				util.alert(res.Result.FailureMessage);
    			}
    		}, {
    			errorAlertModel: 1,
    			submitSelector: isNeedManaul ? $('.j-set', this.$el) : $('.j-validate', this.$el)
    		});
    	},

    	//获取邮箱配置
    	_getEmailSet: function(callback) {
    		var me = this;
    		return new Promise((resolve, reject) => {
					util.FHHApi({
						url: '/EM1HCRMTemplate/workflowEmailSet/getAccountInfo',
						success: function(res) {
							if(res.Result.StatusCode == 0) {
								resolve(_.extend(me.options, res.Value, res.Value.data || {}))
								callback && callback(_.extend(res.Value, res.Value.data || {})); // 兼容server 接口
							}
							reject()
						}
					});
				})
    	},

        
			/**
			 *@desc 销毁视图页面
				*/
			destroy: function() {
				for (let key in this.comps) {
					const comp = this.comps[key]
					comp.$destroy && comp.$destroy();
					comp.destroy && comp.destroy();
				}
				this.comps = null;
			}
    });

    module.exports = ObjMap;
});
