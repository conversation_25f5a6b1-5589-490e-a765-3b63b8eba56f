.crm-s-sysemail {
	.flowemail-show-box,
	.flowemail-set-box {
		padding: 38px 20px 0;
		color: #333;
		h3 {
			font-size: 15px;
			margin-bottom: 32px;
		}
		.set-item.flex {
			display: flex;
			align-items: center;
		}
		.set-item {
			position: relative;
			margin-bottom: 24px;
			&.hide {
				display: none;
			}
			.check-box {
				display: flex;
				align-items: center;
				cursor: pointer;
				.checkbox-input {
					margin-right: 8px;
				}
			}
			label {
				display: inline-block;
				font-size: 12px;
				line-height: 2.67;
				height: 32px;
				width: 106px;
			}
			input {
				padding: 0 8px;
				border: 1px solid #c3ced9;
				#css3 .radius(3px);
			}
			.error-msg {
				position: absolute;
				left: 106px;
				color: red;
				font-size: 14px;
				.el-icon-warning {
					margin-right: 4px;
				}
			}
		}
		.msg-btn-area {
			margin-left: 106px;
			.unbind-btn {
				padding: 0px 38px;
			}
		}
	}
	.flowemail-show-box {
		display: none;
		label {
			color: #919eab;
		}
		.text {
			width: 250px;
			margin: 6px 10px 0 0;
			vertical-align: top;
		}
		a,
		.text {
			display: inline-block;
		}
		.edit-item {
			display: none;
		}
		.set-item-edit {
			.text {
				display: none;
			}
			a {
				display: none;
			}
			.edit-item {
				display: inline-block;
				vertical-align: top;
			}
			.set-item-btns {
				margin-top: 16px;
				.crm-btn {
					margin-right: 10px;
				}
			}
		}
		input {
			width: 394px;
			height: 32px;
		}
		textarea {
			border: 1px solid #c3ced9;
			#css3 .radius(3px);
			padding: 3px 8px;
			width: 394px;
			height: 107px;
		}
	}
	.flowemail-set-box {
		// display: none;
		position: absolute;
		top: 90px;
		bottom: 0;
		left: 0;
		right: 0;
		.left {
			float: left;
			width: 639px;
			height: 100%;
			padding-left: 20px;
			border-right: 1px solid #eee;
		}
		.right {
			float: left;
			padding-left: 30px;
			.r-item {
				margin-top: 16px;
			}
		}
		.btns-box {
			padding-left: 106px;
			span {
				margin-right: 16px;
			}
			.manual-set-btn {
				padding: 0 11px;
			}
		}
	}
	.base-set {
		input {
			width: 394px;
			height: 32px;
		}
	}
	.all-set {
		.manual-set {
			display: inline-block;
		}
	}
	.manual-set {
		display: none;
		input {
			width: 216px;
			height: 32px;
		}
		.set-item-item {
			display: inline-block;
			margin-right: 24px;
			&.port {
				input {
					width: 29px;
				}
				label {
					width: 36px;
				}
			}
		}
	}

	.crm-scroll {
		top: 110px;
	}
	.crm-email-set-tab {
		height: 54px;
		line-height: 54px;
		background-color: var(--color-neutrals01);
		box-shadow: inset 0 -1px 0 0 #eeeeee;
		font-size: 14px;
		color: #333333;
		position: relative;
		.set-span(@p-left:20px) {
			padding-left: @p-left;
			&:hover {
				cursor: pointer;
			}
			display: inline-block;
			height: 53px;
			color: #999;
		}
		.crm-set-system__email {
			.set-span(20px);
		}
		.crm-set-sedi__email {
			.set-span(40px);
		}
		.crm-set-email-active {
			color: #333;
		}
		.crm-set-active__email {
			position: absolute;
			bottom: 1px;
			height: 3px;
			background: #ff8837;
			display: inline-block;
			left: 0px;
			width: 120px;
			transition: 0.15s left linear;
		}
	}
	.crm-email-sediment {
		margin-top: 20px;
		.crm-intro {
			margin: 0 16px;
			padding-bottom: 13px;
		}
		.crm-intro h3 {
			padding-bottom: 4px;
			line-height: 24px;
		}
		.crm-intro p {
			line-height: 18px;
		}
	}
	.crm-obj-sedi {
		margin: 20px 16px 0;
		padding-bottom: 20px;
		border-bottom: 1px dashed #dee1e6;
		.crm-obj-sedi__title {
			display: flex;
			align-items: center;
			font-size: 16px;
			line-height: 24px;
			color: #333;
			.crm-obj-sedi__title__switch {
				margin-left: 12px;
			}
		}
		.crm-sedi-mes {
			margin-top: 8px;
			color: var(--color-neutrals11);
			.sedi-mes__lt {
				display: inline-block;
				float: left;
				width: 16px;
				height: 16px;
				background: url("@{imgUrl}/checkbox.png") no-repeat;
				background-position: -6% 50%;
				position: relative;
				top: 1px;
				margin: 0 10px 0 20px;
				&:hover {
					cursor: pointer;
				}
			}
			.disabled-selected {
				background: url("@{imgUrl}/ico-check.png") no-repeat;
				background-position: -8% 76%;
			}
			.sedi-mes__rg {
				float: left;
				p:nth-child(1) {
					font-size: 12px;
					color: #333;
					margin-bottom: 10px;
				}
				p:nth-child(2) {
					font-size: 12px;
					color: #999;
				}
				.crm-choiced-iden {
					color: #999 !important;
				}
			}
		}
	}
	.crm-see-sub-email {
		padding: 0 16px;
		&.setting-official {
			background: #f4f6f9;
			.crm-see-sub-email-content {
				.crm-see-change-official {
					.change-official__link-btn {
						display: none;
					}
				}
				.crm-see-sub__setting-result {
					.setting-result__value {
						display: none;
					}
					.setting-result__comp {
						display: inline-block;
					}
				}
				.setting-btn-area {
					display: block;
				}
			}
		}
		.crm-see-sub-email-content {
			padding: 20px 0 8px;
			border-bottom: 1px dashed #dee1e6;
			.crm-see-sub__title {
				margin-bottom: 8px;
				font-size: 16px;
				line-height: 24px;
				color: #333;
			}
			.crm-see-change-official {
				display: flex;
				align-items: center;
				margin-bottom: 8px;
				font-size: 12px;
				line-height: 18px;
				color: #999;
				.change-official__desc {
					flex: 1;
				}
				.change-official__link-btn {
					color: var(--color-info06);
					cursor: pointer;
				}
			}
			.crm-see-sub__setting-result {
				margin-bottom: 12px;
				font-size: 12px;
				line-height: 18px;
				color: var(--color-neutrals15);
				.setting-result__label {
					margin-right: 13px;
				}
				.setting-result__comp {
					display: none;
					.el-radio__label {
						padding-left: 2px;
						font-size: 12px;
					}
				}
			}
			.setting-btn-area {
				display: none;
				margin-bottom: 12px;
				.crm-btn + .crm-btn {
					margin-left: 10px;
				}
			}
			.crm-see-email-save {
				width: 88px;
				height: 36px;
				border-radius: 3px;
				background-color: #407fff;
				color: var(--color-neutrals01);
				font-size: 14px;
				line-height: 36px;
				text-align: center;
				margin-left: 19px;
				padding: 0;
				border: none !important;
				&:hover {
					background-color: #2f6dcf;
					cursor: pointer;
				}
			}
			.crm-under-email {
				margin: 20px 0 40px 20px;
				label {
					display: block;
					margin-bottom: 16px;
					&:hover {
						cursor: pointer;
					}

					input {
						margin-right: 8px;
					}
				}
			}
		}
	}
}
.sys-email__auto-association__open-tips__header {
	line-height: 18px;
}
.sys-email__auto-association__open-tips__desc {
	line-height: 18px;
	color: var(--color-neutrals11);
}
