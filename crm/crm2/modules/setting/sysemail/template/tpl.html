<div class="crm-tit">
	<h2><span class="tit-txt">{{$t("邮件管理")}}</span></h2>
</div>
<div class="crm-tab">
	<span class="crm-set-system__email j-crm-set-email cur"
		>{{$t("系统邮箱设置")}}</span
	>
	<span class="crm-set-sedi__email j-crm-set-email"
		>{{$t("邮件沉淀设置")}}</span
	>
	<!-- <label class="crm-set-active__email"></label> -->
</div>
<div class="crm-module-con crm-scroll">
	<!-- <div class="crm-tab">
			<span data-render="databoard-box" class="cur">流程邮箱设置</span>
	</div> -->
	<div class="crm-email-sediment crm-set-mail-email hide">
		<div class="crm-intro">
			<h3>{{$t("说明:")}}</h3>
			<p>1.{{$t("支持邮件沉淀的CRM对象销售线索客户联系人商机2.0。")}}</p>
			<p>2.{{$t("crm.默认沉淀规则")}}</p>
			<p class="hide">
				3.{{$t("上级可见下级邮件数据范围默认为当前员工仅可见自己与客户的往来邮件。")}}
			</p>
		</div>
		<div class="crm-obj-sedi">
			<div class="crm-obj-sedi__title">
				{{$t("CRM对象邮件沉淀")}}
				<div class="crm-obj-sedi__title__switch fx-switch"></div>
			</div>
			<div class="crm-sedi-mes clearfixed">
				<p>
					{{$t("开启后，在对象（销售线索，客户，联系人，商机2.0）下将沉淀纷享邮箱中员工与客户的往来邮件。")}}
				</p>
				<!-- <span class="sedi-mes__lt"></span>
							<div class="sedi-mes__rg">
									<p class="sedi-mes__P1">{{$t("自动沉淀所有往来邮件")}}</p>
									<p>{{$t("勾选后在对象（销售线索客户联系人）下将沉淀纷享邮箱中所有员工与客户的往来邮件。")}}</p>
							</div> -->
			</div>
		</div>
		<!-- <div class="crm-see-sub-email">
				<div class="crm-see-sub-email-content">
					<div class="crm-see-sub__title">{{$t("上级可见下级邮件数据范围")}}</div>
					<div class="crm-see-change-official">
						<p class="change-official__desc">{{$t("用于设置销售线索客户联系人对象下的邮件沉淀数据权限。当设置上级可查看下级邮件时系统将自动发送CRM通知给相应员工。")}}</p>
						<p class="change-official__link-btn">{{$t('设置')}}</p>
					</div>
					<div class="crm-see-sub__setting-result">
						<span class="setting-result__label">{{$t("上级可见的邮件数据范围：")}}</span>
						<span class="setting-result__value"></span>
						<span class="setting-result__comp fx-radio-group"></span>
					</div>
					<div class="setting-btn-area">
						<span class="crm-btn crm-btn-primary crm-see-sub-email__submit-setting">{{$t("确定")}}</span>
						<span class="crm-btn manual-set-btn crm-see-sub-email__cancel-setting">{{$t("取消")}}</span>
					</div>
				</div>
			</div> -->
	</div>
	<div class="crm-p20 crm-set-mail-email">
		<div class="crm-intro">
			<h3>{{$t("说明:")}}</h3>
			<p>
				{{$t("系统邮箱是用于流程中（工作流审批流业务流程）后动作发送邮件")}}
			</p>
		</div>
		<div class="flowemail-box">
			<div class="flowemail-show-box">
				<h3>{{$t("验证邮箱帐号")}}</h3>
				<div class="set-item">
					<label>{{$t("邮箱账号")}}</label>
					<div class="text">{{account}}</div>
					<input type="text" name="account" style="display: none" />
					<a href="javascript:;" class="j-account-setting"
						## if (!passwordErr) { ## style="display: none" ## } ##>
						{{$t("设置")}}
					</a>
					<p class="error-msg" 
						## if (!passwordErr) { ## 
							style="margin-top: -4px; display: none;" 
						## } else { ## 
							style="margin-top: -4px;" 
						## } ##
					>
						<i class="el-icon-warning"></i>
						{{ $t("密码或用户名称错误，邮箱无法正常发送邮件，请更新设置") }}
					</p>
				</div>
				<div class="set-item" data-type="nickname">
					<label>{{$t("发信昵称")}}</label>
					<div class="text">{{{{-nickname||$t("无")}}}}</div>
					<a href="javascript:;" class="j-setItem">{{$t("设置")}}</a>
					<div class="edit-item">
						<input type="text" name="" />
						<div class="set-item-btns">
							<span class="crm-btn crm-btn-primary j-sureItem"
								>{{$t("确认")}}</span
							>
							<span class="crm-btn j-cancelItem"
								>{{$t("取 消")}}</span
							>
						</div>
					</div>
				</div>
				<div class="set-item" data-type="signature">
					<label>{{$t("个性签名")}}</label>
					<div class="text">{{signature||$t("无")}}</div>
					<a href="javascript:;" class="j-setItem">{{$t("设置")}}</a>
					<div class="edit-item">
						<textarea></textarea>
						<div class="set-item-btns">
							<span class="crm-btn crm-btn-primary j-sureItem"
								>{{$t("确认")}}</span
							>
							<span class="crm-btn j-cancelItem"
								>{{$t("取 消")}}</span
							>
						</div>
					</div>
				</div>
				<div class="msg-btn-area">
					<span class="crm-btn j-unbind unbind-btn">
						{{$t("解除绑定")}}
					</span>
				</div>
			</div>
			<div class="flowemail-set-box">
				<div class="left">
					<h3>{{$t("验证邮箱帐号")}}</h3>
					<div class="base-set">
						<div class="set-item account">
							<label>{{$t("邮箱账号")}}</label>
							<input type="text"
								placeholder="<EMAIL>"
								name="account"
							/>
							<p class="error-msg" style="display: none">
								<i class="el-icon-warning"></i>
								<span class="text"></span>
							</p>
						</div>
						<div class="set-item password">
							<label>{{$t("邮箱密码")}}</label>
							<input type="password"
								placeholder='{{$t("请输入邮箱密码")}}'
								name="password"
							/>
							<p class="error-msg" style="display: none">
								<i class="el-icon-warning"></i>
								<span class="text"></span>
							</p>
						</div>
						<div class="set-item username hide">
							<label>{{$t("用户名称")}}</label>
							<input type="text"
								placeholder='{{$t("用户名称")}}'
								name="username"
							/>
							<p class="error-msg" style="display: none">
								<i class="el-icon-warning"></i>
								<span class="text"></span>
							</p>
						</div>
						<div class="set-item flex" style="margin: -10px 0 14px">
							<label style="margin-right: 3px;"></label>
							<div class="check-box mn-checkbox-box j-use-username">
								<input type="checkbox"
									value="1"
									class="checkbox-input mn-checkbox-item"
								/>
								<span>{{$t("使用用户名验证")}}</span>
							</div>
						</div>
					</div>
					<div class="manual-set">
						<div class="set-item">
							<div class="set-item-item">
								<label>{{$t("SMTP服务器")}}</label>
								<input type="text" name="host" />
							</div>
							<div class="mn-checkbox-box set-item-item">
								<!-- 应向达要求写死不可关闭，如果需要重新开启选择 则去除 input 开放span -->
								<!-- <span class="mn-checkbox-item mn-selected" disabled></span> -->
								<input type="checkbox"
									name="is-outgoing-ssl"
									value="1"
									checked="checked"
									## if(emailConfig.isForceSSL) { ## disabled ## } ##
									class="mn-checkbox-item mn-selected is-ssl"
								/>
								<span>SSL</span>
							</div>
							<div class="set-item-item port">
								<label>{{$t("端口")}}</label>
								<input type="text" name="port" class="port" />
							</div>
							<p class="error-msg" style="display: none">
								<i class="el-icon-warning"></i>
								<span class="text"></span>
							</p>
						</div>
					</div>

					<div class="btns-box">
						<span class="crm-btn crm-btn-primary j-validate"
							>{{$t("验证")}}</span
						>
						<span class="crm-btn manual-set-btn j-set"
							>{{$t("手动设置")}}</span
						>
					</div>
				</div>
				<div class="right">
					<div class="r-item">{{$t("常见问题")}}</div>
					<div class="r-item">
						<a
							href="http://open.fxiaoke.com/support.html?articleId=7#faq11"
							target="_blank"
							>{{$t("可以绑定个人邮箱吗")}}</a
						>
					</div>
					<div class="r-item">
						<a
							href="http://open.fxiaoke.com/support.html?articleId=7#faq6"
							target="_blank"
							>{{$t("为什么密码总是错误")}}</a
						>
					</div>
					<div class="r-item">
						<a
							href="http://open.fxiaoke.com/support.html?articleId=7#artiId=7"
							target="_blank"
							>{{$t("如何填写IMAP")}}/{{$t("SMTP邮箱地址")}}</a
						>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
