/**
 * @desc 客户树关系后台页面
 */
define(function (require, exports, module) {
	var accountTreeConfig = Backbone.View.extend({
		initialize: function (opts) {
			this.setElement(opts.wrapper);
			this.apiName = opts.apiName;
			this.$wrapper = opts.el[0];
			// setTimeout(() => {
			// }, 1000);
			this.render();
		},
		render: function () {
			const me = this;
			require.async('vcrm/sdk',function(sdk){
				sdk.getComponent('accountTreeConfig').then((Comp) => {
					const Constructor = Vue.extend(Comp.default);
					me.widget = new Constructor({
						el: me.$wrapper,
						propsData: {
							apiName: me.apiName,
						}
					})
					// const $options = {
					// 	el: me.$wrapper,
					// 	render: h => h(Comp.default)
					// };
					// me.widget = new Vue($options).$mount(me.$wrapper);
				});
			});
		},
		destroy(){
			this.widget && this.widget.destroy && this.widget.destroy();
		}
	});
	module.exports = accountTreeConfig;
});
