/**
 * @file  预设对象 企业库
 * <AUTHOR>
 */
define(function (require, exports, module) {

	var util = require("crm-modules/common/util"),
		tpl = require('./template/tpl-html'),
		FunctionSdk = require('paas-function/sdk'),
		state = {},
		funcobj = {};


	var EnterpriseLibrary = Backbone.View.extend({
		initialize: function (opts) {
			this.comps = {}
			this.setElement(opts.el);
			this.$el.html(tpl());
			this.getConfig();
		},

		render: function () {
		},

		events: {
			"click .j-save": "saveHandle"
		},

		renderRadioTpl: function (status) {
			let me = this,
				param = {
					wrapper: '.leads-wrapper',
					template: `<fx-radio-group v-model="value" @change="change">
								<fx-radio label="1">${$t("默认规则（线索/客户的公司名称是工商注册）")}</fx-radio>
								<fx-radio label="0">${$t("自定义规则")}
									<div v-if="custom_function" class="custom-function-name" @click="clickCustomFunction">-{{custom_function.func_name}}<i class="el-icon-edit"></i></div>
								</fx-radio>
							</fx-radio-group>`,
					data() {
						return {
							value: status,
							custom_function: null
						}
					},
					methods: {
						change: function (val) {
							me.oppoLeads = val;
							if (val === '0') {
								me.renderCustomFunction();
								this.custom_function = {
									func_name: $t('添加')
								}
							} else {
								this.custom_function = null;
							}
						},
						clickCustomFunction() {
							let func_api_name = this.custom_function ? this.custom_function.func_api_name : '';
							me.renderCustomFunction(func_api_name);
						}
					},
					created() {
						if (this.value == '0') {
							this.custom_function = state;
							this.custom_function.func_name = funcobj && funcobj.function ? state.func_name : $t('该函数已删除')
						}
					}
				}
			me.comps['radiobox'] = window.FxUI.create(param);
		},
		renderCustomFunction(checked_api_name) {
			var me = this;
			FunctionSdk && FunctionSdk.getEnterpriseDirectoryFunction({
				object_api_name:'EnterpriseInfoObj',
				checked_api_name: checked_api_name
			},
			res => {
				if (res.status) {
					console.log('add=>',res)
					let data = res.data && res.data.function;
					me.comps['radiobox'].custom_function = {
						func_api_name: data.api_name,
						binding_object_api_name: data.binding_object_api_name,
						func_name: data.function_name
					}
				}
			});
		},
		// 是否删除已有函数方法
		getFunc: function () {
			let me = this,
				binding_object_api_name = state && state.binding_object_api_name ? state.binding_object_api_name : 'NONE';
			me._getAjax && (me._getAjax.abort(), (me._getAjax = null));
			me._getAjax = util.FHHApi(
				{
					url:
						"/EM1HNCRM/API/v1/object/function/service/find",
					data: {
						"api_name": state && state.func_api_name,
						"binding_object_api_name": binding_object_api_name
					},
					success: function (res) {
						if (res.Result.StatusCode === 0) {
							if (res.Value) {
								funcobj = res.Value;
								me.renderRadioTpl(me.oppoLeads);
							}
							return;
						}
						util.alert(res.Result.FailureMessage);
					},
				}, {
				errorAlertModel: 1
			}
			);
		},
		// 获取配置方法
		getConfig: function () {
			var me = this;
			me._getAjax && (me._getAjax.abort(), (me._getAjax = null));
			me._getAjax = util.FHHApi(
				{
					url:
						"/EM1HNCRM/API/v1/object/biz_config/service/get_config_value_by_key",
					data: {
						"key": "account_leads_to_enterprise"
					},
					success: function (res) {
						if (res.Result.StatusCode === 0) {
							if (res.Value && res.Value.value) {
								let data = res.Value.value, status = null;
								if (typeof data == 'string') {
									state = data = JSON.parse(data)
								}
								status = data && data.use_default_rule;
								me.oppoLeads = status;
								me.getFunc()

							}
							return;
						}
						util.alert(res.Result.FailureMessage);
					},
				}, {
				errorAlertModel: 1
			}
			);
		},
		// 保存设置
		saveHandle: function () {
			let radiobox = this.comps['radiobox'],
				custom_function = radiobox.custom_function,
				fun_obj = {
					"use_default_rule": this.oppoLeads,
					"func_api_name": this.oppoLeads == '0' ? custom_function && custom_function.func_api_name : '',
					"func_name": this.oppoLeads == '0' ? custom_function && custom_function.func_name : '',
					'binding_object_api_name': this.oppoLeads == '0' ? custom_function && custom_function.binding_object_api_name : ''
				};
			if (radiobox.custom_function && !radiobox.custom_function.func_api_name) {
				CRM.util.alert($t('请选择一个自定义函数'));
				return
			}
			this.setConfig(JSON.stringify(fun_obj))
		},
		// 保存设置接口
		setConfig: function (val) {
			let me = this;
			me._getAjax && (me._getAjax.abort(), (me._getAjax = null));
			me._getAjax = CRM.util.FHHApi({
				url:
					"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",
				data: {
					"key": "account_leads_to_enterprise",
					"value": val
				},
				success: function (res) {
					if (res.Result.StatusCode === 0) {
						CRM.util.remind(1, $t("设置成功"));
						return;
					}
					CRM.util.alert(res.Result.FailureMessage);
				},
			}, {
				errorAlertModel: 1
			})
		},
		destroy: function () {
			let me = this;
			me.undelegateEvents();
		}
	});

	module.exports = EnterpriseLibrary;
});
