/**
 * @file  预设对象 唯一性设置
 * <AUTHOR>
 */
define(function (require, exports, module) {

    var util  = require('base-modules/utils'),
		tpl = require('./template/tpl-html'),
		data = require('../../data/data').data,
		Select = require('crm-widget/select/select');
    var Model = Backbone.Model.extend({
		defaults:{
			IsImportEnabled:'',
			IsOpenApiEnabled:'',
			checkList:[],
			defaultValue:'',
			RuleSettings:[],
			ownerType:''
		}
	});

    var OnlyOne = Backbone.View.extend({
		choiceNum : 1,
		ruleSelects:[],
        options: {
            ownerType: 2,
			checklist:[{
				value: '',
				name: $t("请选择")
			},
			{
				value: 'key1',
				name: $t("条件1")
			},
			{
				value: 'key2',
				name: $t("条件2")
			}]
        },

        events:{
        	'click .check-btn': 'offHandle',
			'click .j-add-item': 'addItemHandle',
			'click .j-del-item': 'delItemHandle',
			'click .j-ok': 'saveHandle'
		},

        initialize: function(opts) {
			var me = this;
			this.$el.appendTo(opts.$el);
			this.model= new Model();
			me.collection=[];
			me.$el.append('<div class="crm-loading"></div>');
			this.getCheckList(function(){
				me.getUniquenessRule(function(data){
					data = data || {};
					me.$('.crm-loading').remove();
					var rules = (data.pending_rules || {}).rules || [];
					var firstFieldName = rules.length && rules[0].conditions && rules[0].conditions[0] && rules[0].conditions[0].field_name;
					var defaultValue = firstFieldName || me.model.get('defaultValue');
					me.model.set('defaultValue', defaultValue);
					me.$el.html(tpl({
						IsImportEnabled: !!data.use_when_import_excel,
						IsOpenApiEnabled: !!data.use_when_call_open_api,
						defaultValue: defaultValue
					}));
					if(rules.length){
						_.each(rules[0].conditions || [],function(rule,index){
							if(index >= 1){
								me.$el.find('.j-add-item').trigger('click', rule.field_name);
								me.ruleSelects[index+1].trigger('change', rule.field_name, {
									value:rule.field_name,
									name:''
								}, me.ruleSelects[index+1]);
							}
							else {
								me.model.set('defaultValue', rule.field_name);
							}
						})
					}
					me.render();
					me.model.set('IsImportEnabled', !!data.use_when_import_excel);
					me.model.set('IsOpenApiEnabled', !!data.use_when_call_open_api);
				});
			});

			this.listenTo(this.model, 'change:IsImportEnabled',this._changBtn);
			this.listenTo(this.model, 'change:IsOpenApiEnabled',this._changBtn);
        },

        render: function () {
        	var me = this;
			me.ruleSelects && me.ruleSelects[1] && me.ruleSelects[1].destroy();
			me.ruleSelects[1] = new Select({
				$wrap: $('.rule1', me.$el),   // 容器
				width: 290,
				zIndex:1000,
				options: me.getOptions(),
				defaultValue: me.model.get('defaultValue'),
				appendBody: false
			});
			me.ruleSelects[1].on('change', function (v, item) {
                var that = this;
				var oldkey =_.filter(me.getRules(),function(item1){
					return item1.FieldName != that.$el.closest('.list-item').attr('data-value');
				});
				if(oldkey.find(function(key){
					return key.FieldName == item.value
					})){
					util.remind(3,$t("请勿设置重复字段"));
					that.setValue('');
					return;
				}
				that.$el.closest('.list-item').attr('data-value', item.value);
			});

		},

		getOptions: function () {
			var list = _.clone(this.model.get('checkList'));
			var data = this.resValue.unique_rule || {};
			var rules = (data.pending_rules || {}).rules || [];
			var hasVField = false;
			
			if (rules.length) {
				_.map(rules[0].conditions, function (rule, index) {
					_.map(list, function (item, key) {
						if (item.value === rule.field_name && item.virtualField) {
							hasVField = true;
						}
					});
				});
			}

			if (!hasVField) {
				list = _.filter(list, function (value, key) {
					return !value.virtualField;
				});
			}

			return list;
		},

        getUniquenessRule: function (cb) {
        	var me = this;

			CRM.util.FHHApi({
				url:'/EM1HNCRM/API/v1/object/unique_rule/service/get_setting',
				data:{
					api_name:this.options.apiName
				},
				success:function (data) {
					if(data.Result &&　data.Result.StatusCode == 0) {
						me.resValue = data.Value;
						cb && cb(data.Value.unique_rule);
					}else{
						cb && cb({
							IsImportEnabled:true,
							IsOpenApiEnabled:false,
							Rules:[]
						});
					}
				}
			},{
				errorAlertModel:1
			})
		},

        getCheckList: function (cb) {
			var me = this;
			CRM.util.FHHApi({
				url:'/EM1HNCRM/API/v1/object/unique_rule/service/field_describe_list',
				data:{
					api_name:me.options.apiName
				},
				success:function (data) {
					if(data.Result &&　data.Result.StatusCode == 0){
						var arr = [{
							value: 0,
							name: $t("请选择")
						}];
						_.map(data.Value.field_describes, function (item) {
							arr.push({
								value: item.api_name,
								name: item.label,
								virtualField: item.virtualField,
								isTitle: item.virtualField
							});
						});
						me.model.set('defaultValue',arr[1].value);
						me.model.set('checkList',arr);
						cb && cb();
					}else{
						util.alert(data.Result.FailureMessage);
					}
				}
			},{
				errorAlertModel:1
			});
		},

        _changBtn: function () {
        	if(!this.model.get('IsImportEnabled') && !this.model.get('IsOpenApiEnabled')){
				$('.rule-box',this.$el).hide();
			}
			if(this.model.get('IsImportEnabled')||this.model.get('IsOpenApiEnabled')){
				$('.rule-box',this.$el).show();
			}
		},

        addItemHandle: function (e,defaultValue) {
			var $target = $(e.target),
				$parent = $target.closest('.list-item'),
			    $Wrapper = $target.closest('.choices'),
				me= this;
			this.choiceNum ++;
			if($Wrapper.find('.rule-item').length == 4){
				$('<li class="list-item" ><span class="line" style="display:none;" ></span><span class="rule'+this.choiceNum+' rule-item" ></span><span class="del-icon j-del-item"></span></span></li>').insertAfter($Wrapper.find('.list-item').eq($Wrapper.find('.list-item').length-2));
				me.$('.tip').removeClass('hide');
			}else{
				$('<li class="list-item" ><span class="line" ></span><span class="rule'+this.choiceNum+' rule-item" ></span><span class="del-icon j-del-item"></span></span></li>').insertAfter($Wrapper.find('.list-item').eq($Wrapper.find('.list-item').length-2));
				me.$('.tip').addClass('hide');
			}
			var $wrapper =  $Wrapper.find('.rule'+this.choiceNum);

			this.ruleSelects[this.choiceNum] = new Select({
				$wrap: $wrapper,   // 容器
				width: 290,
				zIndex:1000,
				options: me.getOptions(),
				defaultValue: defaultValue||'',
				appendBody: false
			});
			this.ruleSelects[this.choiceNum].on('change', function(v, item){
				var that = this;
				var oldkey =_.filter(me.getRules(),function(item1){
					return item1.FieldName!= that.$el.closest('.list-item').attr('data-value');
				});
				if(oldkey.find(function(key){
					return key.FieldName == item.value
					})){
					util.remind(3,$t("请勿设置重复字段"));
					this.setValue('');
					return;
				}

				this.$el.closest('.list-item').attr('data-value',item.value);
				me.collection.splice(me.collection.indexOf(item.value),1,item.value);
				if($(this.el).parent().closest('.list-item').find('.error-tip') && item.value != ''){
					$(this.el).parent().closest('.list-item').find('.error-tip').remove();
					$(this.el).parent().closest('.list-item').find('.g-select-title-wrapper').removeClass('error-item');
					$(this.el).parent().closest('.list-item').find('.line').css('padding-bottom','0');
				}
			});
			if($parent.parent().find('.rule-item').length == 5){
				$Wrapper.find('li').eq($Wrapper.find('li').length-1).remove();

			}

			e.stopPropagation();
		},

		delItemHandle: function (e) {
			var $target = $(e.target),
				$Wrapper = $target.closest('.choices'),
				temp = [];
			if($Wrapper.find('.rule-item').length == 5){
				$target.closest('.rule-box').find('.tip').addClass('hide');
				$Wrapper.find('.line').css('display','block');
				$Wrapper.append('<li class="list-item"><span class="line" style="display:none;"></span><span class="add j-add-item"></span></li>')
			}
			$target.closest('.list-item').remove();


		},

        offHandle: function (e) {
			var $target = $(e.currentTarget),
				type = $target.attr('data-con');

			if($target.hasClass('on')){
				$target.addClass('off').removeClass('on');
				$target.find('.core').addClass('off-core');
				this.model.set(type,false);
			}else{
				$target.removeClass('off').addClass('on');
				$target.find('.core').removeClass('off-core');
				this.model.set(type,true);
			}

			e.stopPropagation();
		},

        saveHandle: function (e) {
			var items = $('.list-item',this.$el),
				arr = [],
				tag = true,
				me = this;
			this.$el.find('.error-tip') &&this.$el.find('.error-tip').remove();
			_.each(items, function(item){
				var value = $(item).attr('data-value');
				if (!value || value === '0') {
					if(!$(item).find('.add').length){
						$(item).find('.g-select-title-wrapper').addClass('error-item');
						$(item).append('<div class="error-tip">'+ $t("请选择字段")+'</div>');
						$(item).find('.line').css('padding-bottom','34px');
						tag = false;
					}
				}else{
					$(item).find('.g-select-title-wrapper').removeClass('error-item');
					arr.push(value);
				}
			});
			if(tag){
				me.judgeRuleBox(function(){
					me.setRuleSettings()
				});
			}
			console.log(arr.join());
		},

        setRuleSettings:function () {
			var me = this;
			CRM.util.FHHApi({
				url:'/EM1HNCRM/API/v1/object/unique_rule/service/save',
				data: {
					unique_rule: {
						describe_api_name: me.options.apiName,
						use_when_import_excel: me.model.get('IsImportEnabled'),
						use_when_call_open_api: me.model.get('IsOpenApiEnabled'),
						pending_rules: {
							version: ((me.resValue.unique_rule || {}).pending_rules || {}).version || 0,
							rules: me.model.get('RuleSettings')
						},
						version: (me.resValue.unique_rule || {}).version || 0,
					}
				},
				success:function (data) {
					if(data.Result &&　data.Result.StatusCode == 0){
						me.resValue = data.Value;
						util.remind(1, $t("设置成功！"));
						return;
					}

					util.remind(3, data.Result.FailureMessage);
				}
			},{
				errorAlertModel:1
			})
		},

        judgeRuleBox: function (cb) {
			var btns = this.$el.find('.check-con .check-btn');

			this.model.set('IsImportEnabled',$(btns[0]).hasClass('on')?true:false);
			this.model.set('IsOpenApiEnabled',$(btns[1]).hasClass('on')?true:false);

			var rules = this.formatRules(this.getRules());

			this.model.set('RuleSettings',rules);
			rules && cb && cb(rules);
		},

        getRules:function(){
			var Lis = this.$el.find('.choices .list-item'),
				arr = [];
			_.each(Lis,function(li){
				if($(li).attr('data-value')){
					arr.push({
						FieldName:$(li).attr('data-value')
					})
				}
			});
			return arr;
		},

		formatRules: function (data) {
			var rules = [];

			_.map(data, function (item) {
				rules.push({
					connector: 'AND',
					field_name: item.FieldName,
					field_value: 'PRECISE',
					mapping_field: ''
	            });
			});

			return [{
				connector: 'OR',
				conditions: rules
			}];
		},

        /**
         * @desc 页面引用对象销毁
         */
        destroy: function() {
            var me = this;
            me.undelegateEvents();
            _.each(me.ruleSelects, function(ruleSelect){
            	ruleSelect && ruleSelect.destory && ruleSelect.destory();
			})
        }
    });

    module.exports = OnlyOne;
});
