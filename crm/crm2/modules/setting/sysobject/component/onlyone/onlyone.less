.onlyone-box {
    position: relative;
    width: 100%;
    height: 100%;
	.content{
        position: absolute;
            left:0; top: 0; bottom: 40px; right: 0;
		padding: 20px 40px;
		.box-intro{
			font-size: 13px;
			line-height: 1.54;
			color: #999999;
		}
		.check-box{
			margin-top:30px;
			.check-btn{
				display:inline-block;
				width:44px;
				height:24px;
				#css3 >.radius(50px);
				background: #72ce56;
				position:relative;
				vertical-align: middle;
				.core{
					display:inline-block;
					position:absolute;
					right:2px;
					top:2px;
					width:20px;
					height:20px;
					background-color:var(--color-neutrals01);
					#css3>.radius(50px);
				}

			}
			.off{
				background:#ccc;
			}
			.off-core{
				left:2px;
			}
			.check-tip{
				margin-left:16px;
				vertical-align: middle;
				//line-height:;
			}

		}
		.check-title{
			font-size:16px;
			padding-bottom:12px;
			border-bottom:1px solid #eeeeee;
		}
		.check-con{
			margin-top:24px;
			font-size:13px;
			color: #333333;
			.open-check{
				margin-top:18px;
			}
		}
		.rule-box{
			margin-top:34px;
			.rule-title{
				padding-bottom:12px;
				border-bottom:1px solid #eeeeee;
			}
			.rule-con{
				padding-top:20px;
				span{
					font-size: 16px;
					color: #333333;
					display:inline-block;
					vertical-align:middle;
				}
				.choices{

					margin-left:100px;
				}

			}
			.tip{
				margin-left:152px;
				font-size: 13px;
				color: #999999;
			}
			.rule-items{
				position:relative;
				.and{
					position:absolute;
					left:0;
					top:0;
					bottom:0;
					font-size: 13px;
					color: #999999;
					//padding-left:30px;
					width:100px;
					margin:0 auto;
					.text{
						position:absolute;
						right:15px;
						top:50%;
						height:20px;
						transform:translateY(-50%);
						margin-top:-8px;
					}
				}
				.choices{
					vertical-align:middle;
					.error-item{
						border:solid 1px #ff765d;
					}
					.list-item{
						position:relative;
					}
					.del-icon{
						margin-left:18px;
						margin-top:6px;
						display:inline-block;
						vertical-align: top;
						width:16px;
						height:18px;
						.img-retina("@{imgUrl}/del.png", "@{imgUrl}/del_2x.png", 80px, 40px);
					}
					.rule-item{
						width:290px;
						margin-bottom:8px;
						font-size:13px;
						.g-select-title-wrapper{
							height:28px;
							line-height:28px;

						}


					}
					.line{
						position:absolute;
						left:0;
						top:-3px;
						width:20px;
						height:39px;
						//height:100%;
						margin-top:15px;
						#css3>.radius(3px);
						//#css3>.transform(translateY(-50%));
						border-left:solid 1px #dddddd;
						border-top:solid 1px #dddddd;
						border-bottom:solid 1px #dddddd;
						//padding-bottom:1px;
					}
					.rule-item{
						padding-left:32px;
					}
					.add{
						margin-left:32px;
						margin-bottom:15px;
						margin-top:5px;
						width:18px;
						height:18px;
						#css3>.radius(50px);
						border:solid 1px #dddddd;
						position:relative;
						&:before{
							position:absolute;
							left:8px;
							top:4px;
							content:'';
							width:2px;
							height:10px;
							background-color: #3487e2;
						}
						&:after{
							position:absolute;
							//margin-left:10px;
							left:4px;
							top:8px;
							content:'';
							width:10px;
							height:2px;
							background-color: #3487e2;
						}


					}


				}
				.error-tip{
					margin-left:33px;
					margin-top:-3px;
					margin-bottom:17px;
					color: rgb(245, 113, 95);
					font-size:12px;
					&:before{
						content:' ';
						width:18px;
						height:18px;
						margin: 0 8px 0 0;
						background-position: -271px -19px;
						display:inline-block;
						vertical-align:middle;
						.img-retina("@{imgUrl}/icos-crm.png", "@{imgUrl}/icos-crm_2x.png", 80px, 40px);
					}
				}
			}
		}
	}

	.btns{
		position:absolute;
		bottom:0;
		right:0;
		height:40px;
		width:100%;
		background-color: var(--color-neutrals01);
		border-top:1px solid #eeeeee;
		//#css3>.shadow(0 1px 0 0 #eeeeee);
		//bottom:32px;
		//left:40px;
		//margin-top:160px;
		.btn-ok{
			margin-left:40px;
			margin-top:3px;

		}
	}

	.crm-w-select {
		.item-tit {
			padding: 6px 10px 6px 32px;
		}
	}
}
