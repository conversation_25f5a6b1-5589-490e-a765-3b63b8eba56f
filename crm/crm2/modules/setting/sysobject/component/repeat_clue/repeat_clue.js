/**
 * @file  预设对象 唯一性设置
 * <AUTHOR>
 */
define(function (require, exports, module) {

	var util = require('base-modules/utils'),
		tpl = require('./template/tpl-html')

	var RepeatClue = Backbone.View.extend({
		choiceNum: 1,
		ruleSelects: [],
		options: {
			pages: [{
				id: 'repeat_data_process_rule',
				className: 'page-repeat-data-process-rule'
			}, {
				id: 'repeat_rule',
				className: 'page-repeat-rule'
			}]
		},

		events: {
			'click .menu-tabs li': 'tabsHandle',
		},

		async initialize (opts) {
			var me = this;
			const isGrayBehaviorActiveRecords = await me.getGrayByServerForLeads('graySfaImportBehaviorActiveRecords');
			this.cachePages = []
			this.firstLoaded = false;
			this.$el = opts.el;
			this.$el.html(tpl({
				isGrayBehaviorActiveRecords:isGrayBehaviorActiveRecords || false
			}));
			this.renderPage(0)	//首次加载完?
		},
		 getGrayByServerForLeads(type='',apiname='LeadsPoolObj'){
			return new Promise((resolve,reject) => {
				if(!type){
					reject()
				}
				try {
					CRM.util.FHHApi({
						url: '/EM1HNCRM/API/v1/object/pool_service/service/isGrayTimeClaimLimit',
						data: {
							api_name: apiname,
						},
						success: function (res) {
							if (res.Result.StatusCode === 0) {
								resolve(res.Value?.[type]);
								return;
							}
							resolve(false);
						},
						
					}, {
						errorAlertModel: 1
					})
				} catch (error) {
					reject()
				}
			})
		},

		tabsHandle(e) {
			if (!this.firstLoaded) return;
			let index = Math.floor($(e.target).index() / 2);
			this.$el.find('.menu-tabs li').removeClass('active').eq(index).addClass('active');
			this.$el.find('.page-list li').removeClass('active').eq(index).addClass('active');
			this.renderPage(index)
		},

		renderPage(index) {
			let me = this;
			let page = this.options.pages[index];
			if (me.cachePages[index]) {
				me.cachePages[index].show();
			}

			var path = ['.', page.id, page.id].join('/');
			require.async(path, function (Page) {
				me.firstLoaded = true;
				var myPage = new Page({
					wrapper: '.' + page.className
				});
				myPage.show();
				me.cachePages[index] = myPage;
			});
		},
		destroy () {
			var me = this;
			me.undelegateEvents();
		}
	});

	module.exports = RepeatClue;
});
