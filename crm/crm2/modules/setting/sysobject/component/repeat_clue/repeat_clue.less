.repeat-clue-manage {
	overflow: auto;
    height: calc(~"100% - 20px");
    padding: 20px;
	.header-info {
		padding: 20px;
		line-height: 1.62;
		border-radius: 4px;
		background-color: #f0f2f5;
		.title {
			color: var(--color-neutrals19);
		}
		.content {
			color: var(--color-neutrals15);
		}
	}
	.container {
		.menu-tabs {
			display: flex;
			margin: 16px 0;
			li {
				color: var(--color-neutrals11);
				cursor: pointer;
			}
			li.active {
				color: var(--color-neutrals19);
			}
			li:hover {
				color: var(--color-neutrals19);
			}
			.line {
				width: 2px;
				height: 16px;
				background-color: #e9edf5;
				margin: 0 24px;
			}
		}
		.page-list {

			li {
				display: none;
			}
			li.active {
				display: block;
			}
		}
	}
}
