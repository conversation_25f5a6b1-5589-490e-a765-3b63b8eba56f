/*
 * @Descripttion: 
 * @Author: LiAng
 * @Date: 2021-07-14 11:43:22
 * @LastEditors: LiAng
 * @LastEditTime: 2021-07-28 11:52:47
 */
define(function (require, exports, module) {
	let FieldBase = require('crm-modules/action/field/field').C.Base
	let FilterGroup = require('crm-modules/common/filtergroup/filtergroup');
	var helper = require('./helper');
	let util = require('crm-modules/common/util');

	module.exports = FieldBase.extend({
		render() {
			this.comps = {}
			this.renderFilters()
		},
		renderFilters() {
			let defaultValue = this.get('data').filters;

			this.comps['filters'] = new FilterGroup({
				$wrapper: this.$el,
				title: $t("且(AND)"),
				width: 750,
				AND_MAX: 10,
				OR_MIN: 0,
				addBtnName: $t('新增或关系'),
				apiname: 'LeadsObj',
				OR_KEY: 'conditions',
				selectone_multiple: true,	//单选变多选
				datetimeOprateMore: true,	//时间字段支持更多比较符号
				defaultValue: defaultValue || [],
				filterType: [
					'group',
					'url',
					'time',
					'image',
					'file_attachment',
					'percentile',
					'signature',
					'true_or_false',
					'employee_many',
					'department_many',
					'html_rich_text',
					'object_reference_many'
				],
				filterApiname: [
					'leads_status',
					'resale_count',
					'transform_time',
					'assigner_id',
					'assigned_time',
					'expire_time',
					'remaining_time',
					'out_tenant_id',
					'out_owner',
					'last_modified_by',
					'owner',
					'is_remind_recycling',
					'new_opportunity_id',
					'opportunity_id',
					'contact_id',
					'account_id',
					'returned_time',
					'remind_days'
				],
				helper: helper
			});

		},
		getValue() {
			this.hideError();
			if (!this.comps['filters'].valid()) {
				this.showError();
				return null;
			}
			return JSON.parse(this.comps['filters'].getValue());
		},

		showError: function () {
			util.showErrmsg(this.$el, $t('请填写筛选值!'));
		},

		hideError: function () {
			util.hideErrmsg(this.$el);
		},

		destroy() {
			FieldBase.prototype.destroy.apply(this);
			for (let key in this.comps) {
				this.comps[key].destroy && this.comps[key].destroy();
			}
			this.comps = null;
		}
	})

})
