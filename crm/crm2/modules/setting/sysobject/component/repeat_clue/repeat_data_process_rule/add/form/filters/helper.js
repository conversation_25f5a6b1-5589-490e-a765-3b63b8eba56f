define(function (require, exports, module) {
    let helper = require("crm-modules/common/filtergroup/filtergroup").helper;
    return _.extend({}, helper, {
        compare: [{
			value: 1,
			name: $t("等于"),
			value1: 'EQ'
		}, {
			value: 2,
			name: $t("不等于"),
			value1: 'N'
		}, {
			value: 3,
			name: $t("大于"),
			value1: 'GT'
		}, {
			value: 4,
			name: $t("大于等于"),
			value1: 'GTE'
		}, {
			value: 5,
			name: $t("小于"),
			value1: 'LT'
		}, {
			value: 6,
			name: $t("小于等于"),
			value1: 'LTE'
		}, {
			value: 7,
			name: $t("包含"),
			value1: 'LIKE'
		}, {
			value: 8,
			name: $t("不包含"),
			value1: 'NLIKE'
		}, {
			value: 9,
			name: $t("为空"),
			value1: 'IS'
		}, {
			value: 10,
			name: $t("不为空"),
			value1: 'ISN'
		}, {
			value: 11,
			name: $t("早于"),
			value1: 'LT'
		}, {
			value: 12,
			name: $t("晚于"),
			value1: 'GT'
		}, {
			value: 13,
			name: $t("属于"),
			value1: 'IN'
		}, {
			value: 14,
			name: $t("不属于"),
			value1: 'NIN'
		}, {
			value: 17,
			name: $t('时间段'),
			value1: 'BETWEEN'
		},  {
			// value: 18,
			// name: $t('介于'),
			// value1: 'BETWEEN'
		}, {
			value: 19,
			name: $t("自定义"),
			value1: 'CUSTOM'
		}, {
			value: 21,
			name: $t("相同"),
			value1: 'SAME'
		}, {
			value: 22,
			name: $t("不相同"),
			value1: 'NSAME'
		}, {
			value: 23,
			name: $t("开始于"),
			value1: 'STARTWITH'
		}, {
			value: 24,
			name: $t("结束于"),
			value1: 'ENDWITH'
		},{
			value: 25,
			name: $t("属于"),
			value1: 'HASANYOF'
		}, {
			value: 26,
			name: $t("不属于"),
			value1: 'NHASANYOF'
		},{
			value: 27,
			name: $t("过去N天内(不含当天)"),
			value1: 'LTE',
			optionTip: $t('前N天(不含当天)，比如当前是5月8号，前3天是指5.7、5.6、5.5')
		},{
			value: 28,
			name: $t("未来N天内(不含当天)"),
			value1: 'GTE',
			optionTip: $t('后N天(不含当天)，比如当前是5月8号，后3天是指5.9、5.10、5.11')
		},{
			value: 29,
			name: $t("过去N月内(不含当月)"),
			value1: 'LTE',
			optionTip: $t('前N月：指从当天所在月的前一个月开始，向前N个月；前1月=上月；前2个月=上月+上上月')
		},{
			value: 30,
			name: $t("未来N月内(不含当月)'"),
			value1: 'GTE',
			optionTip: $t('后N月：指从当天所在月的下个月开始，向后N个月；后1月=下月；后2月=下月+下下月')
		},{
			value: 31,
			name: $t("N天前"),
			value1: 'LT',
			optionTip: $t('N天前(当天算1天)，比如当前日期是0508，3天前，是指0505的23:59:59之前')
		},{
			value: 32,
			name: $t("N天后"),
			value1: 'GT',
			optionTip: $t('N天后(当天算1天)，比如当前日期是0508，3天后，是指0511的00:00:00之后')
		},{
			value: 33,
			name: $t("N周前"),
			value1: 'LT',
			optionTip: $t('N周前(当周算1周)，比如当前是2020年第7周，3周前，是指2020年第4周周日23:59:59之前')
		},{
			value: 34,
			name: $t("N周后"),
			value1: 'GT',
			optionTip: $t('N周后(当周算1周)，比如当前是2020年第7周，3周后，是指2020年第10周周一00:00:00之后')
		}, {
            value: 35,
            name: $t('过去N周内(不含当周)'),
            value1: 'LTE',
            optionTip: $t('crm_filter_tips13')
        }, {
            value: 36,
            name: $t('未来N周内(不含当周)'),
            value1: 'GTE',
            optionTip: $t('crm_filter_tips14')
        }, 
        , {
            value: 37,
            value1: 'LTEO',
            name: $t('过去N天内(含当天)'),
            optionTip: $t('crm_filter_tips15')
        }, {
            value: 38,
            name: $t('未来N天内(含当天)'),
            value1: 'GTEO',
            optionTip: $t('crm_filter_tips16')
        }, {
            value: 39,
            name: $t('过去N周内(含当周)'),
            value1: 'LTEO',
            optionTip: $t('crm_filter_tips17')
        }, {
            value: 40,
            name: $t('未来N周内(含当周)'),
            value1: 'GTEO',
            optionTip: $t('crm_filter_tips18')
        }, {
            value: 41,
            name: $t('过去N月内(含当月)'),
            value1: 'LTEO',
            optionTip: $t('crm_filter_tips19')
        }, {
            value: 42,
            name: $t('未来N月内(含当月)'),
            value1: 'GTEO',
            optionTip: $t('crm_filter_tips20')
        },
    ],
        getCompareNums: function (type) {
            let menus = helper.getCompareNums(type); // 调用原始的 helper 方法
            switch (type) {
                case 'date_time':
                    menus = [1, 2, 11, 12, 9, 10, 17, 23, 24, 27, 28, 29,
                        30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42
                    ];
				case 'date':
					menus = [1, 2, 11, 12, 9, 10, 17, 23, 24, 27, 28, 29,
                        30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42
                    ];
                    break;
            }
            return menus;
        },
        getTypeByCompare: function (compare, type, ftype) {
            let result = helper.getTypeByCompare(compare, type, ftype);
            if([27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42].indexOf(compare.value) != -1){
                return 'datetimeleadstext'
            }
            return result;
        },
        moreTimeOpratorGet(){
            return [35, 36, 37, 38, 39, 40, 41, 42]
        },
        moreTimeOpratorGetValue(operatorNum,value){
            switch(operatorNum) {
                case 37:
                case 38:
                    return ['day', value];
                    break;
                case 41:
                case 42:
                    return ['month', value];
                    break;
                case 35:
                case 36:
                case 39:
                case 40:
                    return ['week', value];
                    break;
            }

        },
    });
});
