define(function (require, exports, module) {

	var helper = require('crm-modules/common/filtergroup/setting_helper');

	module.exports = $.extend(true, {}, helper, {
		compare: [{
			value: 1,
			name: $t("等于"),
			value1: 'EQ'
		}, {
			value: 2,
			name: $t("不等于"),
			value1: 'N'
		}, {
			value: 3,
			name: $t("大于"),
			value1: 'GT'
		}, {
			value: 4,
			name: $t("大于等于"),
			value1: 'GTE'
		}, {
			value: 5,
			name: $t("小于"),
			value1: 'LT'
		}, {
			value: 6,
			name: $t("小于等于"),
			value1: 'LTE'
		}, {
			value: 7,
			name: $t("包含"),
			value1: 'LIKE'
		}, {
			value: 8,
			name: $t("不包含"),
			value1: 'NLIKE'
		}, {
			value: 9,
			name: $t("为空"),
			value1: 'IS'
		}, {
			value: 10,
			name: $t("不为空"),
			value1: 'ISN'
		}, {
			value: 11,
			name: $t("早于"),
			value1: 'LT'
		}, {
			value: 12,
			name: $t("晚于"),
			value1: 'GT'
		}, {
			value: 13,
			name: $t("属于"),
			value1: 'IN'
		}, {
			value: 14,
			name: $t("不属于"),
			value1: 'NIN'
		}, {
			value: 17,
			name: $t('时间段'),
			value1: 'BETWEEN'
		}, {
			value: 19,
			name: $t("自定义"),
			value1: 'CUSTOM'
		}],
		getCompare(type) {
			var me = this;
			var enums;
			switch (type) {
				case 'text':
				case 'long_text':
				case 'phone_number':
				case 'department':
				case 'email':
				case 'url':
				case 'auto_number':      // 自增编号
				case 'object_reference': // 查找关联
					enums = [1, 2, 7, 8, 9, 10];
					break;

				case 'date':
				case 'date_time':
				case 'time':
					enums = [1, 11, 12, 17];
					break;

				case 'select_one':
				case 'select_many':
				case 'employee':
				case 'record_type':
				case 'country':
				case 'province':
				case 'city':
				case 'district':
					enums = [1, 2, 13, 14, 9, 10];
					break;

				case 'count':
				case 'number':
				case 'currency':
				case 'percentile':
					enums = [1, 2, 3, 4, 5, 6];
					break;

				case 'master_detail':
					enums = [1, 2, 7, 8, 9, 10];
					break;

				case 'location':
					enums = [1, 2, 7, 8];
					break;

				default:
					enums = [1];
			}

			return _.map(enums, function (item) {
				return _.find(me.compare, function (obj) {
					return obj.value === item
				})
			});
		}
	})
})
