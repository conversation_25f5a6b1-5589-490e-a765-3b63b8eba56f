define(function (require, exports, module) {
	let util = require('crm-modules/common/util');
	let Dialog = require('crm-assets/widget/dialog/dialog');
	let tpl = require('./template/tpl-html');
	let collectTpl = require('./template/collect-html');
	let FilterGroup = require('crm-modules/common/filtergroup/filtergroup');
	let helper = require('./helper');


	let AddDialog = Dialog.extend({
		attrs: {
			width: 840,
			className: 'repeat-process-type-dialog',
			title: $t("新增处理规则"),
			showBtns: true,
			showScroll:  true,
			content: tpl()
		},
		events: {
			'click .b-g-btn': 'onSave',
			'click .b-g-btn-cancel': 'onCancel'
		},
		render() {
			Dialog.prototype.render.apply(this, arguments);
			this.$el = this.element;
			this.leadsObj_fields = this.defaults.leadsObj_fields;
			this.comps = {}
			this.data = this.defaults.data || {};
			this.mode_rule = this.data.mode_rule || {}
			this.trigger_actions = this.defaults.trigger_actions || []
			this.set_repeat_rule = this.defaults.set_repeat_rule;
			this.isRender = _.some(this.trigger_actions || [], function(item){
				return item == 'ADD' || item == 'SMART_FORM' || item == 'OPEN_API' || item == 'MARKETING_ADD'
			});
			this.isRender = this.set_repeat_rule && this.isRender; 
			this.renderScope();
			this.renderRadioBox();
			this.data.mode_action == "ADD_INTERACTIVE_RECORDS" && this.set_repeat_rule && this.isRender && this.renderCheckBox();
			if (this.mode_rule.extend_rules && this.mode_rule.extend_rules.auto_update && this.isRender){
				this.renderMulRadioBox()
				this.renderMulChoiceRadioBox()
				this.renderNonMulRadioBox()
			}  
		},
		renderScope() {

			let defaultValue = this.data.filters;

			this.comps['scope'] = new FilterGroup({
				$wrapper: this.$el.find('.scope'),
				title: $t("且(AND)"),
				width: 750,
				AND_MAX: 20,
				OR_MIN: 0,
				addBtnName: $t('新增或关系'),
				apiname: 'LeadsObj',
				OR_KEY: 'conditions',
				selectone_multiple: true,	//单选变多选
				defaultValue: defaultValue || [],
				filterType: [
					'date',
					'time',
					'date_time',
					'true_or_false',
					'employee',
					'location',
					'area',
					'image',
					'auto_number',
					'master_detail',
					'payment',
					'sign_in',
					'quote',
					'percentile',
					'formula',
					'employee_many',
					'department_many',
					'html_rich_text',
					'object_reference_many'
				],
				filterApiname: [
					'biz_status',
					'life_status',
					'leads_status',
					'new_opportunity_id',
					'opportunity_id',
					'account_id',
					'contact_id',
					'lock_status',
					'remind_days',
					'resale_count',
					'out_tenant_id'
				],
				helper: helper
			})
		},
		renderRadioBox() {
			let me = this;
			let defaultValue = this.data.mode_action || 'COLLECT'
			this.comps['radiobox'] = FxUI.create({
				wrapper: this.$el.find('.actions')[0],
				template: `<div>
							<fx-radio-group v-model="value" @change="change">
								<fx-radio label="COLLECT">${$t('归集到已有线索')}
									<fx-tooltip effect="dark" content="${$t("如果只存在重复的客户或联系人，则只打重复标记。")}" placement="top">
										<i class="help-btn"></i>
									</fx-tooltip>
								</fx-radio>
								<fx-radio v-if="isRender" label="ADD_INTERACTIVE_RECORDS">${$t('已有线索下生成行为记录')}
									<fx-tooltip v-if="graySfaImportBehaviorActiveRecords" effect="dark" placement="top">
										<div slot="content">${$t("sfa.repeat_process_type.add_interactive_recordsOne")}</br>${$t('sfa.repeat_process_type.add_interactive_recordsTwo')}</div>
										<i class="help-btn"></i>
									</fx-tooltip>
									<fx-tooltip effect="dark" v-else content="${$t("该功能仅对智能表单或Openapi触发动作生效")}" placement="top">
										<i class="help-btn"></i>
									</fx-tooltip>
								</fx-radio>
								<fx-radio label="CUSTOM_FUNCTION">${$t("执行自定义函数")}
									<div v-if="custom_function" class="custom-function-name" @click="clickCustomFunction">-{{custom_function.function_name}}<i class="el-icon-edit"></i></div>
								</fx-radio>
							</fx-radio-group>
							<div class="tips" v-if="showTip">
							<span v-if="graySfaImportBehaviorActiveRecords">${$t("sfa.repeat_process_type.radiobox.bottom")}</span>
							<span v-else>${$t("仅支持新建、智能表单、Openapi和营销通新建")}</span>
							</div>
						</div>`,
				data() {
					return {
						value: defaultValue,
						custom_function: null,
						isRender: me.isRender,
						showTip: false,
						graySfaImportBehaviorActiveRecords:false
					}
				},
				mounted() {
					const me = this;
				    me.getGrayByServerForLeads('graySfaImportBehaviorActiveRecords').then((value) => {
						me.graySfaImportBehaviorActiveRecords = value;
					})
				},
				methods: {
					getGrayByServerForLeads(type='',apiname='LeadsPoolObj'){
						return new Promise((resolve,reject) => {
							if(!type){
								reject()
							}
							try {
								CRM.util.FHHApi({
									url: '/EM1HNCRM/API/v1/object/pool_service/service/isGrayTimeClaimLimit',
									data: {
										api_name: apiname,
									},
									success: function (res) {
										if (res.Result.StatusCode === 0) {
											resolve(res.Value?.[type]);
											return;
										}
										resolve(false);
									},
									
								}, {
									errorAlertModel: 1
								})
							} catch (error) {
								reject()
							}
						})
					},
					change() {
						if (this.value === 'COLLECT') {
							me.renderCollect(this.value);
							this.showTip = false;
							this.custom_function = null;
						}else if (this.value === 'ADD_INTERACTIVE_RECORDS') {
							me.renderCollect(this.value);
							me.renderCheckBox();
							if (me.mode_rule.extend_rules && me.mode_rule.extend_rules.auto_update){
								me.renderMulRadioBox()
								me.renderMulChoiceRadioBox()
								me.renderNonMulRadioBox()
							}  
							this.custom_function = null;
							this.showTip = true;
						}else if (this.value === 'CUSTOM_FUNCTION') {
							this.showTip = false;
							me.renderCustomFunction();
							this.custom_function = {
								function_name: $t('添加')
							}
						}
					},
					clickCustomFunction() {
						me.renderCustomFunction(this.custom_function.function_api_name);
					}
				},
				created() {
					this.showTip = false;
					if (this.value === 'COLLECT' || this.value === 'ADD_INTERACTIVE_RECORDS') {
						me.renderCollect();
						this.showTip = this.value === 'ADD_INTERACTIVE_RECORDS' && me.isRender && me.set_repeat_rule;
					}else if (this.value === 'CUSTOM_FUNCTION'){
						this.showTip = false;
						this.custom_function = me.mode_rule.custom_function;
					}
				}
			})
		},
		renderMulRadioBox() {
			let me = this;
			this.comps['collect.multext'] = FxUI.create({
				wrapper: this.$el.find('.actions-container')[0],
				template: `<div class="multext">
							<h3 class="field-title">${$t("多行文本字段类型更新方式")}</h3>
							<fx-radio-group v-model="value" @change="change">
								<fx-radio label="SKIP">${$t('跳过')}
								</fx-radio>
								<fx-radio label="COVER">${$t('sysobject.repeat_case_setting_add_new_rules_cover_all')}
								</fx-radio>
								<fx-radio label="NULL_VALUE_COVER">${$t('sysobject.repeat_case_setting_add_new_rules_cover_empty')}
								</fx-radio>
								<fx-radio label="SPLICING">${$t("拼接")}
								</fx-radio>
							</fx-radio-group>
						</div>`,
				data() {
					return {
						value: this.getDefaultValue()
					}
				},
				methods: {
					change() {},
					getDefaultValue() {
						let defaultValue = me.mode_rule.extend_rules || {}
						return defaultValue.multi_line_update_mode;
					}
				},
				created() {}
			})
		},
		renderMulChoiceRadioBox() {
			let me = this;
			this.comps['collect.mulchoice'] = FxUI.create({
				wrapper: this.$el.find('.actions-container')[0],
				template: `<div class="multext">
							<h3 class="field-title">${$t("多选类型字段更新方式")}</h3>
							<fx-radio-group v-model="value" @change="change">
								<fx-radio label="SKIP">${$t('跳过')}
								</fx-radio>
								<fx-radio label="COVER">${$t('sysobject.repeat_case_setting_add_new_rules_cover_all')}
								</fx-radio>
								<fx-radio label="NULL_VALUE_COVER">${$t('sysobject.repeat_case_setting_add_new_rules_cover_empty')}
								</fx-radio>
								<fx-radio label="SPLICING">${$t("拼接")}
								</fx-radio>
							</fx-radio-group>
						</div>`,
				data() {
					return {
						value: this.getDefaultValue()
					}
				},
				methods: {
					change() {},
					getDefaultValue() {
						let defaultValue = me.mode_rule.extend_rules || {}
						return defaultValue.many_select_update_mode;
					}
				},
			})
		},
		renderNonMulRadioBox() {
			let me = this;
			this.comps['collect.non-multext'] = FxUI.create({
				wrapper: this.$el.find('.actions-container')[0],
				template: `<div class="multext">
							<h3 class="field-title">${$t("其他类型字段更新方式")}</h3>
							<fx-radio-group v-model="value" @change="change">
								<fx-radio label="SKIP">${$t('跳过')}
								</fx-radio>
								<fx-radio label="COVER">${$t('sysobject.repeat_case_setting_add_new_rules_cover_all')}
								</fx-radio>
								<fx-radio label="NULL_VALUE_COVER">${$t('sysobject.repeat_case_setting_add_new_rules_cover_empty')}
								</fx-radio>
							</fx-radio-group>
						</div>`,
				data() {
					return {
						value: this.getDefaultValue()
					}
				},
				methods: {
					change() {},
					getDefaultValue() {
						let defaultValue = me.mode_rule.extend_rules || {}
						return defaultValue.other_update_mode;
					}
				},
				created() {}
			})
		},
		renderCheckBox() {
			let me = this;
			this.comps['collect.record'] = FxUI.create({
				wrapper: me.$el.find('.actions-container')[0],
				template: `<div class="chk-wrapper">
								<fx-checkbox v-model="checked" @change="change"><span style="color:#606266">${$t("自动更新已有线索")}
										<fx-tooltip effect="dark" placement="top">
											<div slot="content">${$t("1.更新方式说明")}<br/>${$t("sysobject.repeat_case_setting_add_new_rules_cover_all_tips")}<br/>${$t("sysobject.repeat_case_setting_add_new_rules_cover_empty_tips")}<br/>${$t("拼接:新数据拼接到原数据后")}<br/>
											${$t("跳过:不更新")}<br/>${$t("2.字段类型说明")}<br/>${$t("多选类型字段类型：包括多选、查找关联（多选）、人员（多选）、部门（多选）字段类型")}<br/>${$t("拼接:新数据拼接到原数据后")}<br/>
											${$t("图片、附件字段类型默认走拼接")}</div>	
											<i class="help-btn"></i>
										</fx-tooltip>
									</span>
								</fx-checkbox>
						   </div>`,
				data() {
					return {
						checked: this.getDefaultValue()
					};
				},
				methods: {
					change() {
						if(this.checked){
							me.renderMulRadioBox()
							me.renderMulChoiceRadioBox()
							me.renderNonMulRadioBox()
						}else{
							me.$el.find('.multext').html('')
							me.comps['collect.multext'] = null;
							me.comps['collect.non-multext'] = null;
						}
					},
					getDefaultValue() {
						let defaultValue = me.mode_rule.extend_rules || {}
						return defaultValue.auto_update || false;
					},
				},
				created() {}
			});
		},
		renderCollect(val) {
			let me = this;
			this.$el.find('.actions-container').html(collectTpl({
				mode_action: val || this.data.mode_action
			}));
			this.comps['collect.field'] = FxUI.create({
				wrapper: this.$el.find('.actions-container .field')[0],
				template: `<fx-select
								v-model="value"
								:options="options"
								size="mini"
							></fx-select>`,
				data() {
					return {
						value: this.getDefaultValue(),
						options: this.getOptions()
					}
				},
				methods: {
					getDefaultValue() {
						let defaultValue = me.mode_rule.selection_strategy || {}
						return defaultValue.field_name || 'create_time';
					},
					getOptions() {
						return _.values(me.leadsObj_fields).filter((field) => {
							return ['date', 'time', 'date_time'].indexOf(field.type) != -1 && ['assigned_time', 'expire_time', 'owner_change_time','returned_time'].indexOf(field.api_name) == -1
						}).map((field) => {
							return {
								label: field.label,
								value: field.api_name
							}
						})
					}
				}
			})

			this.comps['collect.date'] = FxUI.create({
				wrapper: this.$el.find('.actions-container .date')[0],
				template: `<fx-select
								v-model="value"
								:options="options"
								size="mini"
							></fx-select>`,
				data() {
					return {
						value: this.getDefaultValue(),
						options: [{
							label: $t('最早'),
							value: 'ASC'
						}, {
							label: $t('最晚'),
							value: 'DESC'
						}]
					}
				},
				methods: {
					getDefaultValue() {
						let defaultValue = me.mode_rule.selection_strategy || {}
						return defaultValue.sort_direction || 'ASC';
					}
				}
			})
			// TODO, 本期需求暂不支持
			// this.comps['collect.update.field'] = FxUI.create({
			// 	wrapper: this.$el.find('.actions-container .update-field-checkbox')[0],
			// 	template: `<fx-checkbox v-model="value" size="micro">更新已有数据中对应字段为空值</fx-checkbox>`,
			// 	data() {
			// 		return {
			// 			value: this.getDefaultValue(),
			// 		}
			// 	},
			// 	methods: {
			// 		getDefaultValue() {
			// 			let defaultValue = me.mode_rule.extend_rules || {}
			// 			return defaultValue.update_null_field_value || '';
			// 		}
			// 	}
			// })
			// this.comps['collect.overwrite.field'] = FxUI.create({
			// 	wrapper: this.$el.find('.actions-container .overwrite-field-checkbox')[0],
			// 	template: `<fx-checkbox v-model="value" class="overwrite-field">覆盖已有数据中对应字段为不空的值</fx-checkbox>`,
			// 	data() {
			// 		return {
			// 			value: this.getDefaultValue(),
			// 		}
			// 	},
			// 	methods: {
			// 		getDefaultValue() {
			// 			let defaultValue = me.mode_rule.extend_rules || {}
			// 			return defaultValue.update_non_null_field_value || '';
			// 		}
			// 	}
			// })

		},
		renderCustomFunction(checked_api_name) {
			let me = this;
			this.$el.find('.actions-container').html('');
			require.async('paas-function/sdk.js', function (SDK) {
				SDK.getFunction({
					name_space: 'button',
					return_type: 'void',
					object_api_name: 'LeadsObj',
					disable_binding: false,
					disable_namespace: true,
					disable_returntype: false,
					checked_api_name: checked_api_name
				}).then((data) => {
					me.comps['radiobox'].custom_function = {
						function_api_name: data.function.api_name,
						binding_object_api_name: data.function.binding_object_api_name,
						function_name: data.function.function_name
					}
				})
			})
		},
		isAlertCreateTimeInterval(data) {
			if (data.indexOf('create_time_interval') == -1) {
				return false;
			}
			data = JSON.parse(data);
			for (let i = 0; i < data.length; i++) {
				let conditions = data[i].conditions;
				for (let j = 0; j < conditions.length; j++) {
					let filter = conditions[j];
					if (filter.field_name === 'create_time_interval') {
						if (filter.field_values[0] > 10000) {
							return true;
						}
					}
				}
			}
			return false;
		},
		onSave() {
			let scope = this.comps.scope.getValue();
			let radiobox = this.comps['radiobox'];
			let checkbox = this.comps['collect.record'];
			let field = this.comps['collect.field'];
			let date = this.comps['collect.date'];
			let nonmultext = this.comps['collect.non-multext'];
			let multext = this.comps['collect.multext'];
			let mulchoice = this.comps['collect.mulchoice'];
			let overwrite = this.comps['collect.overwrite.field'];

			this.hideError();
			if (!this.comps['scope'].valid()) {
				this.showError();
				return null;
			} else if (this.isAlertCreateTimeInterval(scope)) {
				util.showErrmsg(this.$el.find('.scope'), $t('时间间隔最大值不能超过 {{n}}',{n:10000}));
				return null;
			}

			let filters = JSON.parse(this.comps['scope'].getValue());

			let data = {
				filters: filters,
				mode_action: radiobox.value,
				mode_rule: {}
			}

			if (radiobox.value === 'COLLECT') {
				if (field.value === '') {
					util.alert($t('请选择字段名称!'))
					return;
				} else if (date.value === ''){
					util.alert($t('请选择最早或最晚!'))
					return;
				}
				data.mode_rule.selection_strategy = {
					field_name: field.value,
					sort_direction: date.value,
				}
				// data.mode_rule.extend_rules = {
				// 	update_null_field_value: update.value,
				// 	update_non_null_field_value: overwrite.value
				// }
			} else if (radiobox.value === 'ADD_INTERACTIVE_RECORDS') {
				if (field.value === '') {
					util.alert($t('请选择字段名称!'))
					return;
				} else if (date.value === ''){
					util.alert($t('请选择最早或最晚!'))
					return;
				}
				if(multext && multext.value == null || mulchoice && mulchoice.value == null || nonmultext && nonmultext.value == null){
					util.alert($t('请设置自动更新已有线索规则!'))
					return;
				}
				data.mode_rule.selection_strategy = {
					field_name: field.value,
					sort_direction: date.value,
				}
				data.mode_rule.extend_rules = {
					auto_update: checkbox.checked,
					multi_line_update_mode: checkbox.checked ? multext.value : null,
					many_select_update_mode: checkbox.checked ? mulchoice.value : null,
					other_update_mode: checkbox.checked ? nonmultext.value : null
				}
			} else if (radiobox.value === 'CUSTOM_FUNCTION') {
				if (radiobox.custom_function && radiobox.custom_function.function_api_name) {
					data.mode_rule.custom_function = {
						function_api_name: radiobox.custom_function.function_api_name,
						binding_object_api_name: radiobox.custom_function.binding_object_api_name,
						function_name: radiobox.custom_function.function_name
					}
				} else {
					util.alert($t('请选择一个自定义函数!'))
					return;
				}
			}

			this.trigger('success', data);

			Dialog.prototype.hide.apply(this, arguments);
		},
		onCancel() {
			Dialog.prototype.hide.apply(this, arguments);
		},
		showError: function () {
			util.showErrmsg(this.$el.find('.scope'), `$t('请填写筛选值')!`);
		},

		hideError: function () {
			util.hideErrmsg(this.$el.find('.scope'));
		},
		destroy() {
			Dialog.prototype.destroy.apply(this);
			for (let key in this.comps) {
				this.comps[key].destroy && this.comps[key].destroy();
			}
			this.comps = null;
		}
	})

	module.exports = AddDialog;
});
