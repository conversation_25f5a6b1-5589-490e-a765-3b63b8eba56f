.repeat-process-type-dialog {
	.repeat-process-type-add-container {
		height: 560px;
		.title {
			margin-bottom: 10px;
			color: var(--color-neutrals19);
		}
		.filter-container {
			margin-bottom: 10px;
			.scope {

			}
		}
		.type-container {
			margin-bottom: 10px;
			font-size: 12px;
			.actions {
				margin-bottom: 10px;
				.custom-function-name {
					display: inline-block;
					color: var(--color-neutrals11);
					.el-icon-edit {
						margin-left: 3px;
					}
				}
				.record-content{
					position: relative;
				}
				.tips{
					color: var(--color-neutrals11);
					font-size: 14px;
					margin-top: 9px;
				}
				.help-btn {
					position: relative;
					top: 3px;
					display: inline-block;
					width: 16px;
					height: 16px;
					background-image: url("../images/icon-qus.png?6.1 0");
					cursor: pointer;
				}
			}
			.actions-container {
				.actions-collect{
					display: flex;
					align-items: center;
					margin-bottom: 10px;
					font-size: 14px;
					.field {
						margin: 0 6px;
					}
					.date {
						margin: 0 6px;
					}
				}
				.field-title{
					color: #606266;
					font-size: 14px;
					margin: 10px 0;
				}
				.actions-custom-function {

				}
				.help-btn {
					position: relative;
					top: 3px;
					display: inline-block;
					width: 16px;
					height: 16px;
					background-image: url("../images/icon-qus.png?6.1 0");
					cursor: pointer;
				}
			}
		}
	}
}

