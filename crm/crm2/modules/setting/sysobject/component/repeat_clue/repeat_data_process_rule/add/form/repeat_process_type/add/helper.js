define(function (require, exports, module) {

	var helper = require('crm-modules/common/filtergroup/setting_helper');

	module.exports = $.extend(true, {}, helper, {
		compare: [{
			value: 1,
			name: $t("等于"),
			value1: 'EQ'
		}, {
			value: 2,
			name: $t("不等于"),
			value1: 'N'
		}, {
			value: 3,
			name: $t("大于"),
			value1: 'GT'
		}, {
			value: 4,
			name: $t("大于等于"),
			value1: 'GTE'
		}, {
			value: 5,
			name: $t("小于"),
			value1: 'LT'
		}, {
			value: 6,
			name: $t("小于等于"),
			value1: 'LTE'
		}, {
			value: 7,
			name: $t("包含"),
			value1: 'LIKE'
		}, {
			value: 8,
			name: $t("不包含"),
			value1: 'NLIKE'
		}, {
			value: 9,
			name: $t("为空"),
			value1: 'IS'
		}, {
			value: 10,
			name: $t("不为空"),
			value1: 'ISN'
		}, {
			value: 11,
			name: $t("早于"),
			value1: 'LT'
		}, {
			value: 12,
			name: $t("晚于"),
			value1: 'GT'
		}, {
			value: 13,
			name: $t("属于"),
			value1: 'IN'
		}, {
			value: 14,
			name: $t("不属于"),
			value1: 'NIN'
		}, {
			value: 17,
			name: $t('时间段'),
			value1: 'BETWEEN'
		}, {
			value: 19,
			name: $t("自定义"),
			value1: 'CUSTOM'
		}, {
			value: 21,
			name: $t("相同"),
			value1: 'SAME'
		}, {
			value: 22,
			name: $t("不相同"),
			value1: 'NSAME'
		}],
		getCompare: function (type) {
			var me = this;
			var enums;
			switch (type) {
				case 'number':
				case 'currency':
					enums = [3,4,5,6];
					break;
				case 'object_reference':
				case 'text':
				case 'long_text':
				case 'select_one':
				case 'select_many':
				case 'phone_number':
				case 'email':
				case 'url':
					enums = [21,22];
					break;
				default:
					enums = [21,22];
			}

			return _.map(enums, function (item) {
				return _.find(me.compare, function (obj) {
					return obj.value === item
				})
			});
		},
		getNameByValue(value1, type) {
			let item = _.findWhere(this.compare, { value1 });
			return item && item.name;
		},
		getTypeByCompare(compare, type, ftype) {
			if ([21, 22].indexOf(compare.value) != -1) {
				return 'diasbled'
			}
		},
		formatFields(fields, opts) {
			fields['create_time_interval'] = {
				api_name: 'create_time_interval',
				label: $t('创建时间间隔（小时）'),
				type: 'number'
			}

			return fields;
		}
	})
})
