define(function (require, exports, module) {
	let FieldBase = require('crm-modules/action/field/field').C.Base
	let util = require('crm-modules/common/util');
	let RulesSortable = require('crm-components/rules_sortable/rules_sortable');
	let tpl = require('./template/tpl-html');
	let process_type_tpl = require('./template/process_type_tpl-html');
	let AddDialog = require('./add/add');
	let helper = require('./add/helper');

	module.exports = FieldBase.extend({
		events: {
			'click .j-add-btn': 'clickAddHandle'
		},
		render() {
			const me = this
			this.comps = {}
			this.duplicated_processing_modes_list = this.model.get('data').duplicated_processing_modes || [];
			this.trigger_actions = this.get('data').trigger_actions || [];
			this.$el.html(tpl());
			this.getGrayByServerForLeads('graySfaImportBehaviorActiveRecords').then((value) => {
				me.graySfaImportBehaviorActiveRecords = value;
				this.renderProcessType();
			}).catch(() => {
				me.graySfaImportBehaviorActiveRecords = false;
			    this.renderProcessType();
			})
		},
		getGrayByServerForLeads(type='',apiname='LeadsPoolObj'){
			return new Promise((resolve,reject) => {
				if(!type){
					reject()
				}
				try {
					CRM.util.FHHApi({
						url: '/EM1HNCRM/API/v1/object/pool_service/service/isGrayTimeClaimLimit',
						data: {
							api_name: apiname,
						},
						success: function (res) {
							if (res.Result.StatusCode === 0) {
								resolve(res.Value?.[type]);
								return;
							}
							resolve(false);
						},
						
					}, {
						errorAlertModel: 1
					})
				} catch (error) {
					reject()
				}
			})
		},
		renderProcessType() {
			let me = this;
			
			this.comps['rulesSortable'] = new RulesSortable({
				$el: this.$el.find('.repeat-process-type-list'),
				labelName: $t('优先级'),
				data: me.duplicated_processing_modes_list,
				renderRule(item, index) {
					return process_type_tpl({
						scope_content: me.getFilterText(item.filters),
						type_content: item.mode_action == 'ADD_INTERACTIVE_RECORDS' ? 
						me.graySfaImportBehaviorActiveRecords ? me.getProcessType(item)  + `(${$t('sfa.repeat_process_type.radiobox.bottom')})`:	me.getProcessType(item) + `(${$t('仅支持新建、智能表单、Openapi和营销通新建')})`
						: me.getProcessType(item)
					})
				},
				actions: [{
					action: 'edit',
					name: $t('编辑')
				}, {
					action: 'delete',
					name: $t('删除')
				}]
			})

			this.comps['rulesSortable'].on('action', function (action, list, item, index) {
				if (action === 'delete') {
					list.splice(index, 1);
					this.renderLabel();
					this.renderRules();
					me.updateAddBtnStatus();
				} else if (action === 'edit') {
					let set_repeat_rule = me.getRepeatRules()
					this.comps['dialog'] = new AddDialog({
						leadsObj_fields: me.model.get('leadsObj_fields'),
						data: item,
						trigger_actions: me.get('forms').trigger_actions.getValue(),
						set_repeat_rule: set_repeat_rule
					});
					this.comps['dialog'].show();

					this.comps['dialog'].on('success', (data) => {
						me.duplicated_processing_modes_list.splice(index, 1, data);
						this.renderLabel();
						this.renderRules();
					})
					
				}
			})

		},
		getRepeatRules: function(){
			let LeadsObj = this.get('forms').set_repeat_rule.comps['LeadsObj'].value || '';
			return LeadsObj;  
		},
		clickAddHandle() {
			let me = this;
			let set_repeat_rule = me.getRepeatRules()
			this.comps['dialog'] = new AddDialog({
				leadsObj_fields: this.model.get('leadsObj_fields'),
				trigger_actions: me.get('forms').trigger_actions.getValue(),
				set_repeat_rule: set_repeat_rule
			});
			this.comps['dialog'].show();

			this.comps['dialog'].on('success', function (data) {
				me.duplicated_processing_modes_list.push(data);
				me.comps['rulesSortable'].renderLabel();
				me.comps['rulesSortable'].renderRules();
				me.updateAddBtnStatus();
			});
		},

		updateAddBtnStatus() {
			if (this.duplicated_processing_modes_list.length >= 5) {
				this.$el.find('.j-add-btn').hide();
			} else {
				this.$el.find('.j-add-btn').show();
			}
		},
		getFilterText(data) {
			let rows = [];
			if (!data.length) return $t('全部线索');
			_.each(data, (condition, i) => {
				let row = []
				let filters = condition.conditions;
				_.each(filters, (filter, j) => {
					let field = CRM.get(`fields.LeadsObj`)[filter.field_name]
					if (field) {
						let text = this.formatOperator(field, filter.operator);
						if (['SAME', 'NSAME'].indexOf(filter.operator) != -1) {
							row.push(`“${field.label}” ${filter.operator_name}`)
						} else {
							row.push(`“${field.label}” ${filter.operator_name} ${filter.field_values || '--'}`)
						}
					} else if (filter.field_name === 'create_time_interval') {
						row.push(`“${$t("创建时间间隔（小时）")}” ${filter.operator_name} ${filter.field_values || '--'}`)
					}
				})
				if (i == 0) {
					rows.push(`<p>${row.join(` ${$t("且")} `)};</p>`)
				} else {
					rows.push(`<p>${$t("或")} ${row.join(` ${$t("且")} `)};</p>`)
				}
			})
			
			return rows.join(``);
		},
		formatOperator(field, operator) {
			let item = _.findWhere(helper.compare, { value1: operator });
			return item && item.name;
		},
		getProcessType(data) {
			switch (data.mode_action) {
				case 'COLLECT':
					return $t('归集到已有线索')
				case 'ADD_INTERACTIVE_RECORDS':
					return $t('已有线索下生成行为记录')
				case 'CUSTOM_FUNCTION':
					return `${$t("执行自定义函数")}-${data.mode_rule.custom_function.function_name}`
			}
		},
		getValue() {
			this.hideError();
			return this.duplicated_processing_modes_list;
		},

		destroy() {
			FieldBase.prototype.destroy.apply(this);
			for (let key in this.comps) {
				this.comps[key].destroy && this.comps[key].destroy();
			}
			this.comps = null;
		}
	})

})
