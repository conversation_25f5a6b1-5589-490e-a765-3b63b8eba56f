define(function (require, exports, module) {
	let FieldBase = require('crm-modules/action/field/field').C.Base
	let util = require('crm-modules/common/util');
	let tpl = require('./template/tpl-html')
	let field = require('crm-modules/action/field/field');
	let view = require('crm-modules/setting/sysobject/component/repeat_clue/repeat_rule/add/view');
	let model = require('crm-modules/setting/sysobject/component/repeat_clue/repeat_rule/add/model');
	let dialogTpl = require('./template/dialog_tpl-html')

	module.exports = FieldBase.extend({
		options: {
			list: [{
				api_name: 'LeadsObj',
				obj_name: $t('线索'),
			}, {
				api_name: 'AccountObj',
				obj_name: $t('客户'),
			}, {
				api_name: 'ContactObj',
				obj_name: $t('联系人'),
			}]
		},
		events: {
			'click .view_rule': 'viewRuleHandle',
			'click .clear_rule': 'clearRuleHandle',
			'click .add_clue': 'addRuleHandle',
		},
		render() {
			this.comps = {}
			this.data = {}

			this.$el.html(tpl({
				list: this.options.list
			}));
			
			
			this.fetchRulesList(() => {
				this.renderSelect()
			})
			
		},
		fetchRulesList(callback) {
			let me = this;
			util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/leads_duplicated_processing/service/get_duplicated_search_rule_list',
				data: {
					search_template: {
						limit: 50,
						offset: 0,
						table_name: 'biz_leads_duplicated_search',
						where_params: [{
							operator: 'LIKE',
							field_name: 'name',
							values: []
						}, {
							operator: 'EQ',
							field_name: 'status',
							values: [1]
						}]
					}
				},
				success(res) {
					if (res.Result.StatusCode == 0) {
						_.each(me.options.list, (item, i) => {
							me.data[item.api_name] = [];
							_.each(res.Value.duplicated_search_rule_list, (rule, j) => {
								if (item.api_name === rule.object_api_name) {
									me.data[item.api_name].push(rule)
								}
							})
						})
						callback && callback();
					}
				}
			})
		},
		renderSelect() {
			let me = this;
			_.each(this.options.list, (item) => {
				me.comps[item.api_name] && me.comps[item.api_name].destroy();
				me.comps[item.api_name] = FxUI.create({
					wrapper: this.$el.find(`.leadsobj_${item.api_name}_rule`)[0],
					template: `<fx-select
								v-model="value"
								:options="options"
								@change="change"
								size="mini"
							></fx-select>`,
					data() {
						return {
							value: this.getDefaultValue(),
							options: this.getOptions()
						}
					},
					methods: {
						getDefaultValue() {
							let rules = me.get('data').duplicated_search_rules
							let rule = _.findWhere(rules, { object_api_name: item.api_name })
							if (rule) {
								if (_.findWhere(me.data[item.api_name], { id: rule.id })) {
									return rule.id
								}
							}
							return '';
						},
						getOptions() {
							return me.data[item.api_name].map((item, index) => {
								return {
									label: item.name,
									value: item.id,
									map_full_fields: item.map_full_fields
								}
							})
						},
						change() {
							me.updateMapRule();
							me.updateBtnColor();
						}
					}
				})
			})

			setTimeout(() => {
				me.updateMapRule();
				me.updateBtnColor();
			})
		},
		updateBtnColor() {
			let me = this;
			_.each(this.options.list, function (item) {
				let $view_dom = me.$el.find(`.view_rule[data-api_name="${item.api_name}"]`);
				let $clear_dom = me.$el.find(`.clear_rule[data-api_name="${item.api_name}"]`);
				let value = me.comps[item.api_name].value;
				if (value) {
					$view_dom.addClass('active');
					$clear_dom.addClass('active');
				} else {
					$view_dom.removeClass('active');
					$clear_dom.removeClass('active');
				}
			})
		},
		updateMapRule() {
			let me = this;
			_.each(this.options.list, function (item) {
				let $dom = me.$el.find(`.field_map_rule[data-api_name="${item.api_name}"]`);
				let value = me.comps[item.api_name].value;

				if (value) {
					let obj = _.findWhere(me.data[item.api_name], { id: value });
					if (obj.map_full_fields) {
						$dom.hide()
					} else {
						$dom.show()
					}
				} else {
					$dom.hide()
				}
				
			})
			
		},
		viewRuleHandle(e) {
			let me = this;
			let api_name = $(e.target).data('api_name');
			let id = this.comps[api_name].value;

			if (!id) {
				util.alert($t('sfa.settings.repeat_clue.pleaseWriteRule'))
				return false;
			}

			util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/leads_duplicated_processing/service/get_duplicated_search_rule',
				data: {
					duplicated_search_rule_id: id
				},
				success(res) {
					if (res.Result.StatusCode == 0) { 
						let data = res.Value.duplicated_search_rule;
						let api_name = data.object_api_name;
						util.getFieldsByApiName(api_name).done(() => {
							me.viewRuleDialog(data);
						})
					}
				}
			})
		},
		viewRuleDialog(data) {
			FxUI.MessageBox.alert(dialogTpl({
				description: data.description,
				object_name: _.findWhere(this.options.list, { api_name: data.object_api_name }).obj_name,
				rule_detail: this.format_rule_detail(data)
			}), $t('客户重复规则'), {
				customClass: 'view-repeat-rule-continer',
				dangerouslyUseHTMLString: true,
				confirmButtonText: $t('知道了')
			})
		},
		format_rule_detail(data) {
			let rows = []
			let rules = data.usable_rules;
			let api_name = data.object_api_name;
			for (let i = 0; i < rules.length; i++) {
				let conditions = rules[i].conditions;
				let row = []
				for (let j = 0; j < conditions.length; j++) {
					let field = CRM.get(`fields.${api_name}`)[conditions[j].field_name];
					if (field) {
						row.push(`“${field.label}”`)
					}
				}
				if (i == 0) {
					rows.push(`${$t('当')} ${row.join(` ${$t('sfa.settings.repeat_clue.sfa.settings.repeat_clue.and')}`)}`)
				}else if (i == rules.length - 1) {
					rows.push(`;${$t('或')} ${row.join(` ${$t('sfa.settings.repeat_clue.sfa.settings.repeat_clue.and')}`)}`)
				} else {
					rows.push(`;${$t('或')} ${row.join(` ${$t('sfa.settings.repeat_clue.sfa.settings.repeat_clue.and')}`)}`)
				}
			}
			return rows.join('') + `${$t('的值完全相同时, 业务记录出现重复。')}`
		},
		clearRuleHandle(e) {
			// 暂时不处理
			// let duplicated_processing_modes_list = this.model.get('data').duplicated_processing_modes || [], mode_actions;
			// if(duplicated_processing_modes_list.length > 0){
			// 	mode_actions = _.filter(duplicated_processing_modes_list, function(item, index){
			// 		return item.mode_action == 'ADD_INTERACTIVE_RECORDS'
			// 	})
			// }
			// if(mode_actions.length > 0){
			// 	util.alert($t('规则不能清空，原因如下：当前规则已被【已有线索下生成行为记录】重复线索处理规则引用!'))
			// 	return;
			// }
			let api_name = $(e.target).data('api_name');
			this.comps[api_name].value = '';
			this.updateMapRule();
			this.updateBtnColor();
		},
		addRuleHandle() {
			let me = this;
			field.add({
				Model: field.Model.extend(model),
				View: field.View.extend(view),
				record_type: 'default__c',
				show_type: 'full',
				action_type: 'add',
				className: 'repeat_rule_dialog',
				data: {
					object_api_name: 'LeadsObj'
				},
				success: function () {
					me.fetchRulesList(() => {
						me.renderSelect()
					})
				}
			})
		},
		getValue() {
			this.hideError();
			let data = []
			for (let i = 0; i < this.options.list.length; i++) {
				let item = this.options.list[i];
				let value = this.comps[item.api_name].value;
				if (value) {
					data.push({
						id: value
					})
				}
			}
			if (!data.length) {
				this.showError();
				return false;
			}
			return data;
		},

		showError: function () {
			util.showErrmsg(this.$el, $t("请设置查重规则!"));
		},

		hideError: function () {
			util.hideErrmsg(this.$el);
		},

		destroy() {
			FieldBase.prototype.destroy.apply(this);
			for (let key in this.comps) {
				this.comps[key].destroy && this.comps[key].destroy();
			}
			this.comps = null;
			this.list = null;
			this.data = null;
		}
	})

})
