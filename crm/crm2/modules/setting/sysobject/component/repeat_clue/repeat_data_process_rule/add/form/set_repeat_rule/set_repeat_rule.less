.set_repeat_rule-container {
	position: relative;
	font-size: 12px;
	.rule_list {
		padding: 20px;
		border: 1px dashed var(--color-neutrals07);
		border-radius: 3px;
		.rule_item {
			display: flex;
			align-items: center;
			height: 32px;
			.rule_label {
				display: inline-block;
				width: 206px;
				color: var(--color-neutrals19);
			}
			.rule_select {
				
			}
			.view_rule,.clear_rule {
				color: var(--color-neutrals11);
				margin-left: 16px;
				text-decoration:none;
				&.active {
					color: #407fff;
				}
			}
			.field_map_rule {
				display: none;
				color: #ff3f3f;
				margin-left: 16px;
			}
		}
	}
	.add_clue {
		position: absolute;
		top: -25px;
		right: 0;
		color: var(--color-info06);
		text-decoration:none;
	}
}
.view-repeat-rule-continer {
	.repeat-rule-row {
		display: flex;
		label {
			width: 60px;
			font-size: 12px;
		}
		span {
			flex: 1;
			font-size: 12px;
			color: var(--color-neutrals19);
		}
	}
}
