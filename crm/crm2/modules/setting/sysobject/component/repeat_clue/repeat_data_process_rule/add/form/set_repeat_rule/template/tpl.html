<div class="set_repeat_rule-container">
	<ul class="rule_list">
		## for(var i = 0; i < obj.list.length; i++){ ##
			<li class="rule_item">
				## var name = $t('将线索与{{name}}查重',{name: obj.list[i].obj_name}) ##
				<label class="rule_label">{{name}}</label>
				<div class="rule_select leadsobj_{{obj.list[i].api_name}}_rule"></div>
				<a href="javascript:;" class="view_rule" data-api_name="{{obj.list[i].api_name}}">{{$t('查看规则')}}</a>
				<a href="javascript:;" class="clear_rule" data-api_name="{{obj.list[i].api_name}}">{{$t('清空')}}</a>
				<a href="https://{{window.location.host}}/XV/Home/Index#crmmanage/=/module-objmap" target="_blank" class="field_map_rule" data-api_name="{{obj.list[i].api_name}}">{{$t('字段映射规则未匹配')}}</a>
			</li>
		## } ##
	</ul>
	<a href="javascript:;" class="add_clue">{{$t('新建查重规则')}} >></a>
</div>
