define(function (require, exports, module) { 
	let FieldBase = require('crm-modules/action/field/field').C.Base
	let util = require('crm-modules/common/util');

	module.exports = FieldBase.extend({
		render() {
			this.comps = {}

			this.renderRuleAcion();
		},
		renderRuleAcion() {
			let me = this;

			this.comps['checkbox'] = FxUI.create({
				wrapper: this.$el[0],
				template: `<fx-checkbox-group v-model="value">
							<fx-checkbox label="ADD">${$t('新建')}</fx-checkbox>
							<fx-checkbox label="IMPORT">${$t('导入')}</fx-checkbox>
							<fx-checkbox label="SMART_FORM">${$t('智能表单')}</fx-checkbox>
							<fx-checkbox label="OPEN_API">Openapi</fx-checkbox>
							<fx-checkbox label="EDIT">${$t('编辑')}</fx-checkbox>
							<fx-checkbox label="RECOVER">${$t('恢复')}</fx-checkbox>
							<fx-checkbox label="MARKETING_ADD">${$t('营销通新建')}</fx-checkbox>
							<fx-checkbox label="MERGE">${$t('合并')}</fx-checkbox>
							<fx-checkbox label="CHANGE_OWNER">${$t('变更负责人')}</fx-checkbox>
						</fx-checkbox-group>`,
				data() {
					return {
						value: this.getDefaultValue()
					}
				},
				methods: {
					getDefaultValue() {
						return me.get('data').trigger_actions || []
					}
				}
			})
		},
		getValue() {
			this.hideError();
			let value = this.comps['checkbox'].value;
			if (!value.length) {
				this.showError();
				return;
			}
			return value;
		},

		showError: function () {
			util.showErrmsg(this.$el, `${$t("请填写触发规则操作")}!`);
		},

		hideError: function () {
			util.hideErrmsg(this.$el);
		},

		destroy() {
			FieldBase.prototype.destroy.apply(this);
			for (let key in this.comps) {
				this.comps[key].destroy && this.comps[key].destroy();
			}
			this.comps = null;
		}

	})

})
