define(function (require, exports, module) {

	var util = require('crm-modules/common/util');

	module.exports = {
		fetch() {
			this.parse();
			this.set({
				leadsObj_fields: CRM.get('fields.LeadsObj')
			})
		},
		parse() {
			var fields = this.getFields();
			this.set({
				layout: [{
					api_name: 'basic',
					label: $t('基本信息'),
					columns: 2,
					components: [fields.name, fields.description]
				}, {
					api_name: 'trigger_actions',
					label: $t('触发规则的操作'),
					columns: 1,
						components: [fields.trigger_actions]
				}, {
					api_name: 'filters',
					label: $t('触发规则的线索条件'),
					columns: 1,
					components: [fields.filters]
				}, {
					api_name: 'set_repeat_rule',
					label: $t('设置查重规则'),
					columns: 1,
						components: [fields.set_repeat_rule]
				}, {
					api_name: 'repeat_process_type',
					label: $t('线索重复处理方式'),
					columns: 1,
						components: [fields.repeat_process_type]
				}],
				fields: fields
			})
		},
		getFields() {
			return {
				name: {
					api_name: 'name',
					type: 'text',
					label: $t('规则名称'),
					placeholder: $t('sfa.setting.sysobject.repeat_clue.maxWrite'),
					is_required: true,
					max_length: 50
				},
				description: {
					api_name: 'description',
					type: 'long_text',
					label: $t('描述')
				},
				trigger_actions: {
					api_name: 'trigger_actions',
					is_required: true,
					label: $t('触发操作')
				},
				filters: {
					api_name: 'filters',
					label: $t('条件范围')
				},
				set_repeat_rule: {
					api_name: 'set_repeat_rule',
					is_required: true,
					label: $t('查重规则 (至少设置一个对象)')
				},
				repeat_process_type: {
					api_name: 'repeat_process_type',
					label: $t('处理规则：所有线索均默认打重复标记(可拖拽调整顺序)')
				}
			}
		}
		
	}
});
