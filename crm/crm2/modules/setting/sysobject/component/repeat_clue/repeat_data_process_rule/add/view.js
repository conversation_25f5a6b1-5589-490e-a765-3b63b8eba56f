define(function (require, exports, module) {
	let util = require('crm-modules/common/util');
	let field = require('crm-modules/action/field/field');
	let trigger_actions = require('./form/trigger_actions/trigger_actions')
	let filters = require('./form/filters/filters')
	let set_repeat_rule = require('./form/set_repeat_rule/set_repeat_rule')
	let repeat_process_type = require('./form/repeat_process_type/repeat_process_type')


	module.exports = {
		mycomponents: {
			trigger_actions: trigger_actions,
			filters: filters,
			set_repeat_rule: set_repeat_rule,
			repeat_process_type: repeat_process_type
		},

		collect() {
			let data = {
				duplicated_processing: {
					name: this.forms.name.getValue(),
					description: this.forms.description.getValue(),
					trigger_actions: this.forms.trigger_actions.getValue(),
					filters: this.forms.filters.getValue(),
					duplicated_processing_modes: this.forms.repeat_process_type.getValue(),
					duplicated_search_rules: this.forms.set_repeat_rule.getValue(),
					priority: this.model.get('data').priority
				}
			}

			if (this.model.get('action_type') === 'edit') {
				data.duplicated_processing.id = this.model.get('data').id;
			}

			return data;
		},
		featchSave(data) {
			let me = this;
			CRM.util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/leads_duplicated_processing/service/save',
				data: data,
				success(res) {
					if (res.Result.StatusCode == 0) {
						me.trigger('success');
						return;
					}
					CRM.util.alert(res.Result.FailureMessage);
				}
			}, {
				submitSelector: $('span[data-action="submit"]', me.$el.closest('.crm-c-dialog'))
			})
		},
		submit() {
			let me = this;
			let data = this.collect();
			if (!this.validate()) return;

			// 当线索处理配置了【已有线索下生成行为记录时（ADD_INTERACTIVE_RECORDS）】需要校验前置调整件
			let processData = data.duplicated_processing.duplicated_processing_modes;
			let noValidRecord = true;
			$.each(processData, (key, item) => {
				if (item.mode_action == 'ADD_INTERACTIVE_RECORDS') {
					noValidRecord = false;
					return false;
				}
			})
			if (!noValidRecord) {
				let leadsVal = this.forms.set_repeat_rule.comps["LeadsObj"].value;
				let trigger_actions = data.duplicated_processing.trigger_actions || [];
				let matchArr = ['ADD','SMART_FORM','OPEN_API','MARKETING_ADD'];
				let isMatchArr = _.filter(matchArr,function(item){
					return _.contains(trigger_actions, item);
				})

				if(!leadsVal || isMatchArr.length == 0){
					util.alert($t('提交失败，当前规则不满足“已有线索下生成行为记录”的前置条件'));
					return;
				}
			}
			
			// if (!this.validate()) return;

			if (this.model.get('action_type') === 'edit') {
				let confirm = util.confirm($t(`此操作将造成系统在今晚 ${this.model.get('data').task_execute_time || '--'} 更新已有线索的重复标签,是否继续？`), $t('确认'), () => {
					confirm.destroy();
					me.featchSave(data);
				});
			} else {
				me.featchSave(data);
			}

			
		}
	};
});
