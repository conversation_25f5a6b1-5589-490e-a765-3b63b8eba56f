define(function (require, exports, module) {
	let util = require('crm-modules/common/util');
	let Table = require('crm-widget/table/table');
	let tpl = require('./template/tpl-html')
	let field = require('crm-modules/action/field/field');
	let view = require('./add/view');
	let model = require('./add/model');
	var RulePriorityDialog = require('./ruleprioritydialog/ruleprioritydialog');
	let FGutil = require('crm-modules/common/filtergroup/util');


	var RepeatDataProcessRule = Backbone.View.extend({
		options: {
			MAX_RULE_COUNT: 5
		},
		initialize(opts) {
			this.comps = {};
			this.setElement(opts.wrapper);
			this.$el.html(tpl());

			util.getFieldsByApiName('LeadsObj').done(() => {
				this.initTable();
			})
			
		},
		initTable(keyword = '') {
			let me = this;
			this.comps['table'] && this.comps['table'].destroy();
			this.comps['table'] = new Table({
				$el: this.$el.find('.repeat_data_process_rule-table'),
				tableName: 'tableName',
				requestType: 'FHHApi',
				url: '/EM1HNCRM/API/v1/object/leads_duplicated_processing/service/get_duplicated_processing_list',
				showFilerBtn: false,
				showMultiple: false,
				showMoreBtn: false,
				showTermBatch: true,
				noAllowedWrap: false,
				autoHeight: true,
				showPage: false,
				postData: {
					search_template: {
						table_name: 'biz_leads_duplicated_processing',
						where_params: [{
							operator: 'LIKE',
							field_name: 'name',
							values: keyword === '' ? [] : [keyword]
						}],
						orders: [{
							field_name: 'create_time',
							is_asc: true
						}]
					}
				},
				columns: [{
					data: 'name',
					title: $t('规则名称')
				}, {
					data: 'description',
					title: $t('规则描述'),
					dataType: 2
				}, {
					data: 'trigger_actions',
					title: $t('触发动作'),
					dataType: 2,
					render(data) { 
						return data.map((action) => {
							switch (action) {
								case 'ADD':
									return $t('新建')
								case 'IMPORT':
									return $t('导入')
								case 'SMART_FORM':
									return $t('智能表单')
								case 'OPEN_API':
									return 'Openapi'
								case 'EDIT':
									return $t('编辑')
								case 'RECOVER':
									return $t('恢复')
								case 'MARKETING_ADD':
									return $t('营销通新建')
								case 'MERGE':
									return $t('合并')
								case 'CHANGE_OWNER':
									return $t('变更负责人')
							}
							return '--'
						}).join(',')
					}
				}, {
					data: 'last_modified_by',
					title: $t('最后修改人'),
					dataType: 2,
					render(data) {
						let employee = FS.contacts.getEmployeeById(data)
						if (employee) {
							return employee.name
						}
						return data;
					}
				}, {
					data: 'last_modified_time',
					title: $t('最后修改时间'),
					dataType:4,
				}, {
					data: 'status',
					title: $t('状态'),
					dataType: 2,
					render(data) {
						switch (data) {
							case 1:
								return $t('已启用')
							case 0:
								return $t('已禁用')
						}
					}
				}, {
					data: '',
					title: $t('操作'),
					render(value, name, row) {
						let data = [];
						let arr = [`<a data-operate="edit">${$t("编辑")}</a>`,
							`<a data-operate="start">${$t("启用")}</a>`,
							`<a data-operate="disable">${$t("禁用")}</a>`,
							`<a data-operate="copy">${$t("复制")}</a>`,
							`<a data-operate="delete">${$t("删除")}</a>`]

						switch (row.status) {
							case 1:
								data = [arr[0], arr[2], arr[3]];
								break;
							case 0:
								data = [arr[0], arr[1], arr[3], arr[4]]
								break;
							default:
								data = []
						}
						return `<div class="ops-btns">${data.join('\n')}</div>`
					}
				}],
				formatData(data) {
					return {
						data: data.duplicated_processing_list
					};
				}
			}) 

			this.comps['table'].on('trclick', (row, $tr, $target) => {
				let operate = $target.data('operate');
				if (operate == 'edit') {
					this.editHandle(row)
				} else if (operate == 'start') {
					this.statusHandle(row, 1)
				} else if (operate == 'disable') {
					FxUI.MessageBox.confirm($t('确定禁用此条规则？'), $t('提示'), {
						confirmButtonText: $t('确定'),
						cancelButtonText: $t('取消'),
						type: 'warning'
					}).then(() => {
						this.statusHandle(row, 0)
					});
				} else if (operate == 'copy') {
					let count = this.comps['table'].getRowData().length
					if (count >= me.options.MAX_RULE_COUNT) {
						util.alert($t("最多创建{{MaxRule}}条规则", {MaxRule: this.options.MAX_RULE_COUNT}));
					} else {
						this.copyHandle(row);
					}
				} else if (operate == 'delete') {
					FxUI.MessageBox.confirm($t('确定删除此条规则？'), $t('提示'), {
						confirmButtonText: $t('确定'),
						cancelButtonText: $t('取消'),
						type: 'warning'
					}).then(() => {
						this.statusHandle(row, -1)
					});
				}
			});

			this.comps['table'].on('renderListComplete', function () {
				let MAX_RULE_COUNT = me.options.MAX_RULE_COUNT;
				let count = this.getRowData().length;
				me.searchBtn();
				// me.sortBtn();
				me.createBtn();
				me.setTitleCount(MAX_RULE_COUNT, count);

				// me.comps['sort'].disabled = (count < 2 ? true : false)
				// me.comps['sort'].hidden = (count < 2 ? false : true)

				me.comps['create'].disabled = (count >= MAX_RULE_COUNT ? true : false)
				me.comps['create'].hidden = (count >= MAX_RULE_COUNT ? false : true)

			})


		},

		refreshTable() {
			this.comps['table'].setParam({
				pageNumber: 50
			}, true)
		},

		editHandle(row) {
			let me = this;
			field.edit({
				Model: field.Model.extend(model),
				View: field.View.extend(view),
				record_type: 'default__c',
				show_type: 'full',
				action_type: 'edit',
				className: 'repeat_rule_dialog',
				data: row,
				success: function () {
					me.refreshTable();
				}
			})
		},

		disableHandle(row) {
			FxUI.MessageBox.confirm($t('确定禁用此条规则？'), $t('提示'), {
				confirmButtonText: $t('确定'),
				cancelButtonText: $t('取消'),
				type: 'warning'
			}).then(() => {
				//todo
			});
		},

		copyHandle(row) {
			let me = this;
			row = util.deepClone(row);
			row.name = row.name + '-' + $t('副本');
			row.priority = null;
			field.add({
				Model: field.Model.extend(model),
				View: field.View.extend(view),
				record_type: 'default__c',
				show_type: 'full',
				action_type: 'copy',
				className: 'repeat_rule_dialog',
				data: row,
				success: function () {
					me.refreshTable();
				}
			})
		},

		statusHandle(row, status) {
			let me = this;
			util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/leads_duplicated_processing/service/update_status',
				data: {
					table_name: 'biz_leads_duplicated_processing',
					data_id: row.id,
					status: status
				},
				success(res) {
					if (res.Result.StatusCode == 0) {
						util.remindSuccess(res.Value.message);
						me.refreshTable();
					}
				}
			})
		},

		searchBtn() {
			let me = this;
			if (this.comps['search']) return;
			this.comps['search'] = FxUI.create({
				wrapper: '.repeat_data_process_rule-title .j-srarch-rule',
				// 嵌套一层span 是为了解决 button组件 disabled 时, tooltip 不起作用的问题
				template: `<fx-input placeholder="${$t('搜索规则')}" v-model="keyword" size="mini" @change="clickSearch" >
								<fx-button slot="append" icon="el-icon-search" @click="clickSearch"></fx-button>
							</fx-input>`,
				data() {
					return {
						keyword: ''
					}
				},
				methods: {
					clickSearch() {
						me.initTable(this.keyword);
					}
				}
				
			})
		},
		sortBtn() {
			if (this.comps['sort']) return;
			let txt = $t("多条规则时可排序");
			this.comps['sort'] = FxUI.create({
				wrapper: '.repeat_data_process_rule-title .j-sort-rule',
				// 嵌套一层span 是为了解决 button组件 disabled 时, tooltip 不起作用的问题
				template: `<fx-tooltip effect="dark" content="${txt}" :disabled="hidden" placement="top">
							<span><fx-button plain size="mini" :disabled="disabled">${$t("重新排序")}</fx-button></span>
						</fx-tooltip>`,
				data() {
					return {
						disabled: false,
						hidden: true
					}
				},
			})
		},
		createBtn() {
			let me = this;
			if (this.comps['create']) return;
			let txt = $t("最多创建{{MaxRule}}条规则", {MaxRule: this.options.MAX_RULE_COUNT});
			this.comps['create'] = FxUI.create({
				wrapper: '.repeat_data_process_rule-title .j-create-rule',
				// 嵌套一层span 是为了解决 button组件 disabled 时, tooltip 不起作用的问题
				template: `<div><fx-button plain size="mini" :disabled="false" @click="clickCreatDialogHandle">${$t("规则优先级设置")}</fx-button>
							<fx-tooltip effect="dark" content="${txt}" :disabled="hidden" placement="top"><span>
							<fx-button plain size="mini" :disabled="disabled" @click="clickCreateHandle">${$t("新建处理规则")}</fx-button></span>
						</fx-tooltip></div>`,
				data() {
					return {
						disabled: false,
						hidden: true
					}
				},
				methods: {
					clickCreateHandle() {
						field.add({
							Model: field.Model.extend(model),
							View: field.View.extend(view),
							record_type: 'default__c',
							action_type: 'add',
							show_type: 'full',
							className: 'repeat_rule_dialog',
							success: function () {
								me.refreshTable();
							}
						})
					},
					clickCreatDialogHandle() {
						 me.rulePriorityDialog = new RulePriorityDialog({
							title: $t('规则优先级'),
							success: me.refreshTable.bind(me)
						 });
						 me.rulePriorityDialog.show(me.comps.table.getCurData())
					},
				},
				created(){
					this.tableData = []
				}
			})
		},

		setTitleCount(max, n) {
			$('.repeat_data_process_rule-count', this.$el).html(`(${n}/${max})`);
		},
		show: function () {
			this.$el.show();
		},
		hide: function () {
			var result = FollowBehaviorDialog.superclass.hide.call(this);
			this.destroy();
			return result;
		},
		destroy() {
			for (let key in this.comps) {
				this.comps[key].destroy && this.comps[key].destroy();
			}
			this.comps = null;

		}
	})

	module.exports = RepeatDataProcessRule;
})
