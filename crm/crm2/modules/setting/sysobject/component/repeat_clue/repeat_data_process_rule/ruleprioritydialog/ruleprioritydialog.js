/** 保有量上限 */
define(function (require, exports, module) {
	let Dialog = require('crm-widget/dialog/dialog');
	let Table = require('crm-widget/table/table');

	let RulePriorityDialog = Backbone.View.extend({

		options(){
			return {
				cols: [{
					data: 'name',
					title: $t('规则名称')
				}, {
					data: 'description',
					title: $t('规则描述'),
					dataType: 2,
				}, {
					data: 'trigger_actions',
					title: $t('触发动作'),
				},{
					data: 'status',
					title: $t('状态'),
				}], //暂时写死后期可通过传参方式传进来
				tableData: [],
				title: $t('规则优先级'),
				configObj: {
					url: '/EM1HNCRM/API/v1/object/leads_duplicated_processing/service/batch_update_lead_duplicate_priority',
					data: {}
				}
			}
		},

		events: {
			'click .b-g-btn-cancel': 'hide',
			'click .b-g-btn': 'submitBtn'  	// 确定按钮事件
		},

		sortRulePriority() {
			let me = this;
			CRM.util.FHHApi({
				url: me.options.configObj.url,
				data: me.options.configObj.data,
				success(res) {
					if (res.Result.StatusCode === 0) {
						me.dialog.dialogVisible = false;
						me.options.success && me.options.success() 
					}
				}
			})
		},

		viewRulePriority() {
			let me = this;
			me.dialog = FxUI.create({
				template: `<fx-dialog
							:visible.sync="dialogVisible"
							:append-to-body="true"
							size="medium"
							width="70%"
							class="pvui-sort-scene-dialog"
							:title="getTitle"
							@closed="handleClose">
							<fx-scrollbar class="pvui-sort-scene-dialog-scroll">
							<div class="pvui-sort-scene-dialog-content">
								<fx-alert
								class="sort-tip"
								:title="$t('您可以通过拖动来改变顺序')"
								type="info"
								show-icon
								></fx-alert>
								<div class="pvui-sort-scene-list">
								<ul class="list-head">
									<li v-for="item in cols">
										<span>{{ item.title }}</span>
									</li>
								</ul>
								<fx-draggable
									class="table-list"
									v-model="rulePriorityList"
									tag="ul"
									:animation="150"
									v-loading="loading"
									ghost-class="draggable-item"
								>
									<li v-for="item in rulePriorityList">
										<span><i class="drag-target om-icon-drag"
											:title="$t('按住拖拽排序')"
											></i>{{ item.name }}
										</span>
										<span>{{ item.description || '--' }}</span>
										<span>{{ handleTranslate(item.trigger_actions) }}</span>
										<span>{{ item.status == 1 ? $t('已启用') : $t('已禁用')}}</span>
									</li>
								</fx-draggable>
								</div>
							</div>
							</fx-scrollbar>
							<span slot="footer" class="dialog-footer">
							<fx-button type="primary" :loading="saveLoading" @click="handleSave">{{
								$t("保 存")
							}}</fx-button>
							<fx-button @click="handleClose">{{ $t("取 消") }}</fx-button>
							</span>
						</fx-dialog>`,
				data() {
					return {
						dialogVisible: true,
						loading: false,
						saveLoading: false,
						rulePriorityList: [], //列表,
						cols: me.options.cols
					}
				},
				computed: {
					getTitle() {
						return $t("{{objectName}}", {objectName: me.options.title});
					}
				},
				methods: {
					handleSave(){
						let rulePriorityObj = {}
						_.each(this.rulePriorityList, function(item, index){
							rulePriorityObj[item.id] = index + 1
						})
						me.options.configObj.data.data_id_and_priority_map = rulePriorityObj;
						me.sortRulePriority()

					},
					handleClose() {
						this.destroy && this.destroy();
					},
					handleTranslate(trigger_actions){
						return trigger_actions.map((action) => {
							switch (action) {
								case 'ADD':
									return $t('新建')
								case 'IMPORT':
									return $t('导入')
								case 'SMART_FORM':
									return $t('智能表单')
								case 'OPEN_API':
									return 'Openapi'
								case 'EDIT':
									return $t('编辑')
								case 'RECOVER':
									return $t('恢复')
							}
							return '--'
						}).join(',')
					}
				},
				mounted(){
				},
				created() {
					this.rulePriorityList = me.data
				}
			})
		},
		show: function (data) {
			this.data = data
			this.viewRulePriority()
		},
		destroy() {
			this.$el.remove();
			this.options = null;
			this.ruleTable && this.ruleTable.destroy();
		}

	})
	module.exports = RulePriorityDialog;
});
