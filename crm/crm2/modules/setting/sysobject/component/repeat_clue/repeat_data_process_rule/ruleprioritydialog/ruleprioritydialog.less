.pvui-sort-scene-dialog {
	.el-dialog__body {
	  padding: 0px;
	  height: 400px;
	}
	.pvui-sort-scene-dialog-scroll {
	  height: 400px;
	}
	.pvui-sort-scene-dialog-content {
	  padding: 30px 20px;
	}
	.sort-tip {
	  margin-bottom: 16px;
	}
	.pvui-sort-scene-list {
	  border: 1px solid #dee1e6;
	  font-size: 12px;
	  .table-list {
		// min-height: 170px;
	  }
	  .draggable-item {
		background: #f4f6f8;
	  }
	  ul{
		li {
		  display: flex;
		  border-top: 1px solid #dee1e6;
		  height: 40px;
		  line-height: 40px;
		  &:hover {
			background: #f4f6f8;
			cursor: move;
			i.drag-target {
			  display: inline-block;
			}
		  }
		  i.drag-target {
			display: none;
			margin-left: -20px;
			margin-right: 6px;
		  }
		  span {
			flex: 1;
			display: inline-block;
			padding: 0 25px;
			box-sizing: border-box;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		  }
		}
	  }
	  .list-head {
		display: flex;
		li {
		  flex: 1;
		  cursor: initial;
		  border-top: 0px;
		  background: #f4f6f8;
		  border-right: 1px solid #dee1e6;
		  &:hover {
			cursor: initial;
		  }
		}
		li:last-child {
			border-right: 0px;
		  }
	  }
	}
}
