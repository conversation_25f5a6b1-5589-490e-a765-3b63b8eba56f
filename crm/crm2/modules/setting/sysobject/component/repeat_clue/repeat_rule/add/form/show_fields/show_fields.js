define(function (require, exports, module) { 
	let FieldBase = require('crm-modules/action/field/field').C.Base
	let util = require('crm-modules/common/util');
	let FGUtil = require('crm-modules/common/filtergroup/util')
	let filterUtil = require('./../util')

	module.exports = FieldBase.extend({
		dataEvents: {
			'change.object_api_name': 'change_object_handle'
		},
		render() {
			this.comps = {}
			this.api_name = this.model.get('data').object_api_name;
			this.renderShowFields();
		},
		change_object_handle(item) {
			this.comps['show_fields'].api_name = item.value;
			this.comps['show_fields'].value = ['name'];
		},
		renderShowFields(api_name) {
			let me = this;
			this.comps['show_fields'] && this.comps['show_fields'].$destroy();
			this.comps['show_fields'] = FxUI.create({
				wrapper: this.$el[0],
				template: `<fx-transfer v-model="value" :data="data" target-order="push" filterable filter-placeholder="${$t('搜索字段')}" :titles="['${$t('全部字段')}', '${$t('显示字段')}']"></fx-transfer>`,
				data() {
					return {
						data: [],
						value: me.get('data').show_fields || ['name'],
						api_name: ''
					}
				},
				methods: {
					generateData() {
						if (!this.api_name) this.data = [];
						util.getFieldsByApiName(this.api_name).done(() => {
							let fields = CRM.get(`fields.${this.api_name}`)
							this.data = Object.values(fields).filter((item, index) => {
								if (item.is_active === false) {
									return false;
								}
								if (filterUtil.global.indexOf(item.api_name) != -1) {
									return false
								}
								if (['signature', 'image', 'file_attachment', 'group'].indexOf(item.type) != -1) {
									return false;
								}
								return true;
							}).map((item, index) => {
								return {
									key: item.api_name,
									label: item.label
								}
							})
						})
					}
				},
				watch: {
					'api_name': function () {
						this.generateData();
					}
				},
				created() {
					
					this.api_name = me.api_name;
					this.generateData();
				}
			})
		},
		getValue() {
			this.hideError();
			let data = this.comps['show_fields'].value;
			if (data.length < 2) {
				this.showError();
				return false;
			}
			return this.comps['show_fields'].value;
		},

		showError: function () {
			util.showErrmsg(this.$el, $t("显示字段不得少于 2 个!"));
		},

		hideError: function () {
			util.hideErrmsg(this.$el);
		},

		destroy() {
			FieldBase.prototype.destroy.apply(this);
			for (let key in this.comps) {
				this.comps[key].destroy && this.comps[key].destroy();
			}
			this.comps = null;
		}

	})

})
