define(function (require, exports, module) {
	let FieldBase = require('crm-modules/action/field/field').C.Base
	let FilterGroup = require('crm-modules/common/filtergroup/filtergroup');
	var helper = require('./../helper');
	let util = require('crm-modules/common/util');
	let filterUtil = require('./../util')
	let tpl = require('./template/tpl-html')

	module.exports = FieldBase.extend({
		dataEvents: {
			'change.object_api_name': 'change_object_handle'
		},
		render() {
			this.comps = {}
			this.$el.html(tpl());
			this.api_name = this.model.get('data').object_api_name || 'LeadsObj';
			let defaultValue = this.get('data').usable_rules;
			this.renderScope(defaultValue)
		},
		change_object_handle(item) {
			this.api_name = item.value;
			this.renderScope();
		},
		renderScope(defaultValue) {
			this.comps['usable_rules'] && this.comps['usable_rules'].destroy();
			this.comps['usable_rules'] = new FilterGroup({
				$wrapper: this.$el.find('.usable-rules-continer'),
				title: $t("且(AND)"),
				width: 750,
				AND_MAX: 20,
				OR_MAX: 5,
				OR_KEY: 'conditions',
				level: 1,
				addBtnName: $t('新增或关系'),
				apiname: this.api_name,
				selectone_multiple: true,	//单选变多选
				defaultValue: defaultValue,
				filterType: filterUtil.filter[this.api_name].type,
				filterApiname: filterUtil.filter[this.api_name].api_name,
				helper: helper
			});
		},

		getValue: function () {
			this.hideError();
			let result = [];
			let data = this.comps['usable_rules'].getValue();
			data = JSON.parse(data);

			if (!this.comps['usable_rules'].valid()) {
				this.showError();
				return null;
			}

			return data;
		},

		showError: function () {
			util.showErrmsg(this.$el, $t("请填写筛选值"));
		},

		hideError: function () {
			util.hideErrmsg(this.$el);
		},

		destroy() {
			FieldBase.prototype.destroy.apply(this);
			for (let key in this.comps) {
				this.comps[key].destroy && this.comps[key].destroy();
			}
			this.comps = null;
		}
	})

})
