define(function (require, exports, module) {

	module.exports = {
		global: [
			'relevant_team',
			'lock_rule',
			'life_status_before_invalid',
			'lock_user',
			'extend_obj_data_id',
			'out_owner',
			'out_tenant_id',
			'owner_department',
			'_id',
			'version',
			'is_deleted',
			'object_describe_api_name',
			'object_describe_id',
			'tenant_id',
			'order_by',
			'package',
			'resale_count',
			'enterprise_wechat_user_id',
			'refresh_duplicated_version',
			'confirm_time',
			'completed_field_quantity',
			'filling_checker_id',
			'is_remind_recycling',
			'last_deal_closed_amount',
			'pin_yin',
			'remind_days',
			'total_refund_amount',
			'day_of_birth',
			'date_of_birth',
			'year_of_birth',
			'month_of_birth',
			'owner_changed_time',
			'name_order',
			'order_by',
			'industry_ext',
			'extend_days',
		],
		filter: {
			LeadsObj: {
				type: [
					'formula',
					'file_attachment',
					'image',
					// 'time',
					// 'date_time',
					// 'date',
					'count',
					'auto_number',
					'quote',
					// 'select_many',
					// 'true_or_false',
					// 'object_reference',
					'employee_many',
					'department_many',
					'html_rich_text',
					'object_reference_many'
				],
				api_name: [
					'life_status',
					'biz_status',
					'conversion_probability',
					'resale_count',
					'account_id',
					'account_id__r',
					'opportunity_id',
					'opportunity_id__r',
					'contact_id',
					'contact_id__r',
					'partner_id',
					// 'out_resources',
					'close_reason',
					'assigner_id',
					// 'source',
					'completed_result',
					'is_overtime',
					'last_follow_time',
					'leads_status',
					'remaining_time',
					'last_follower',
					'remark',
					// 'owner',
					'lock_status',
					'created_by',
					'last_modified_by',
					'data_own_department',
					'back_reason'
				]
			},
			AccountObj: {
				type: [
					'formula',
					'file_attachment',
					'image',
					// 'time',
					// 'date_time',
					// 'date',
					'count',
					'auto_number',
					'quote',
					// 'select_many',
					// 'true_or_false',
					// 'object_reference',
					'employee_many',
					'department_many',
					'html_rich_text',
					'object_reference_many'
				],
				api_name: [
					'account_status',
					'last_follower',
					'created_by',
					'life_status',
					'deal_status',
					'biz_status',
					'resale_count',
					'last_deal_closed_time',
					'total_refund_amount',
					'partner_id',
					'out_resources',
					'close_reason',
					'account_no',
					'area_location',
					'back_reason',
					'industry_level1',
					'industry_level2',
					'remark',
					'last_modified_by',
					'data_own_department',
					'completion_rate',
					'transfer_count',
					'recycled_reason',
					'leads_id',
					'owner',
					'lock_status',
					'remaining_time'
				]
			},
			ContactObj: {
				type: [
					'formula',
					'file_attachment',
					'image',
					// 'time',
					// 'date_time',
					// 'date',
					'count',
					'auto_number',
					'quote',
					// 'select_many',
					// 'true_or_false',
					// 'object_reference',
					'employee_many',
					'department_many',
					'html_rich_text',
					'object_reference_many'
				],
				api_name: [
					'life_status',
					'resale_count',
					'leads_id',
					'partner_id',
					'out_resources',
					'close_reason',
					'tel1',
					'tel2',
					'tel3',
					'tel4',
					'tel5',
					'mobile1',
					'mobile2',
					'mobile3',
					'mobile4',
					'mobile5',
					'created_by',
					'data_own_department',
					'lock_status',
					'owner',
					'contact_status',
					'last_modified_by',
					'name_order'
				]
			}
		}
	}
});
