define(function (require, exports, module) {

	var util = require('crm-modules/common/util');

	module.exports = {
		fetch() {
			this.parse();
			this.set({
				leadsObj_fields: CRM.get('fields.LeadsObj')
			})
		},
		parse() {
			var fields = this.getFields();
			this.set({
				layout: [{
					api_name: 'basic',
					label: $t('基本信息'),
					columns: 2,
					components: [fields.object_api_name, fields.name, fields.description]
				}, {
					api_name: 'rule_detail',
					label: $t('规则明细'),
					columns: 1,
						components: [fields.usable_rules]
				}, {
					api_name: 'show_fields',
					label: $t('设置显示字段'),
					columns: 1,
					components: [fields.show_fields]
				}],
				fields: fields
			})
		},
		getFields() {
			return {
				object_api_name: {
					api_name: 'object_api_name',
					type: 'select_one',
					label: $t('所属对象'),
					is_required: true,
					options: [{
						label: $t('线索'),
						value: 'LeadsObj'
					}, {
						label: $t('客户'),
						value: 'AccountObj',
					}, {
						label: $t('联系人'),
						value: 'ContactObj'
					}]
				},
				name: {
					api_name: 'name',
					type: 'text',
					label: $t('规则名称'),
					placeholder: $t('请输入，最多50字'),
					is_required: true,
					max_length: 50
				},
				description: {
					api_name: 'description',
					type: 'long_text',
					label: $t('描述')
				},
				usable_rules: {
					api_name: 'usable_rules'
				},
				show_fields: {
					api_name: 'show_fields',
					is_required: true,
					label: $t('显示字段')
				}
			}
		}
		
	}
});
