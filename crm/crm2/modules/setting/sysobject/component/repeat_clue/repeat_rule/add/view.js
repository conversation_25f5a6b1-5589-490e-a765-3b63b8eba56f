define(function (require, exports, module) {
	let util = require('crm-modules/common/util');
	let field = require('crm-modules/action/field/field');
	let usable_rules = require('./form/usable_rules/usable_rules')
	let show_fields = require('./form/show_fields/show_fields')


	module.exports = {
		mycomponents: {
			usable_rules: usable_rules,
			show_fields: show_fields,
		},

		initView() {
			let me = this;
			field.View.prototype.initView.apply(this, arguments);

			if (this.model.get('action_type') === 'edit') {
				this.forms.object_api_name.disable()
			}
		},

		collect() {
			let data = {
				object_api_name: this.forms.object_api_name.getValue(),
				name: this.forms.name.getValue(),
				description: this.forms.description.getValue(),
				usable_rules: this.forms.usable_rules.getValue(),
				show_fields: this.forms.show_fields.getValue(),
				map_full_fields: false
			}

			if (this.model.get('action_type') === 'edit') {
				data.id = this.model.get('data').id;
			}

			return data;
		},
		featchSave(data) {
			let me = this;
			CRM.util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/leads_duplicated_processing/service/save_search_rule',
				data: data,
				success(res) {
					if (res.Result.StatusCode == 0) {
						me.trigger('success');
					}
				}
			}, {
				submitSelector: $('span[data-action="submit"]', this.$el.closest('.crm-c-dialog'))
			})
		},
		submit() {
			let me = this;
			let data = this.collect();

			if (!this.validate()) return;

			if (this.model.get('action_type') === 'edit') { 
				let confirm = util.confirm($t("此操作将造成系统在今晚 {{time}} 更新已有线索的重复标签,是否继续？",{time: this.model.get('data').task_execute_time || '--'}), $t('确认'), () => {
					confirm.destroy();
					me.featchSave(data);
				});
			} else {
				me.featchSave(data);
			}
		},
	};
});
