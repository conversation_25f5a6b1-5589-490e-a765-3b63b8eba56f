define(function (require, exports, module) {
	let util = require('crm-modules/common/util');
	let Table = require('crm-widget/table/table');
	let tpl = require('./template/tpl-html')
	let field = require('crm-modules/action/field/field');
	let view = require('./add/view');
	let model = require('./add/model');



	var RepeatDataProcessRule = Backbone.View.extend({
		options: {
			MAX_RULE_COUNT: 50
		},
		events: {

		},
		initialize(opts) {
			this.comps = {};
			this.setElement(opts.wrapper);
			this.$el.html(tpl());

			$.when(
				util.getFieldsByApiName('LeadsObj'),
				util.getFieldsByApiName('AccountObj'),
				util.getFieldsByApiName('ContactObj')
			).done(() => {
				this.initTable();
			})

		},
		initTable(keyword = '') {
			let me = this;
			this.comps['table'] && this.comps['table'].destroy();
			this.comps['table'] = new Table({
				$el: this.$el.find('.repeat_rule-table'),
				tableName: 'tableName',
				requestType: 'FHHApi',
				url: '/EM1HNCRM/API/v1/object/leads_duplicated_processing/service/get_duplicated_search_rule_list',
				showFilerBtn: false,
				showMultiple: false,
				showMoreBtn: false,
				showTermBatch: true,
				noAllowedWrap: false,
				autoHeight: true,
				showPage: false,
				postData: {
					search_template: {
						table_name: 'biz_leads_duplicated_search',
						where_params: [{
							operator: 'LIKE',
							field_name: 'name',
							values: keyword === '' ? [] : [keyword]
						}]
					}
				},
				columns: [{
					data: 'name',
					title: $t('规则名称')
				}, {
					data: 'description',
					title: $t('规则描述'),
					dataType: 2
				}, {
					data: 'object_name',
					title: $t('所属对象'),
					dataType: 2
				}, {
					data: 'usable_rules',
					title: $t('匹配规则'),
					dataType: 2,
					render(data, type, fulldata) {
						return me.getUsableRulesHtml(fulldata)
					}
				}, {
					data: 'status',
					title: $t('状态'),
					dataType: 2,
					render(data) {
						switch (data) {
							case 1:
								return $t('已启用')
							case 0:
								return $t('已禁用')
						}
					}
				}, {
					data: '',
					title: $t('操作'),
					render(value, name, row) {
						let data = [];
						let arr = ['<a data-operate="edit">'+$t('编辑')+'</a>',
									'<a data-operate="start">'+$t('启用')+'</a>',
									'<a data-operate="disable">'+$t('禁用')+'</a>',
									'<a data-operate="copy">'+$t('复制')+'</a>',
									'<a data-operate="delete">'+$t('删除')+'</a>']
						
						switch (row.status) {
							case 1:
								data = [arr[0], arr[2], arr[3]];
								break;
							case 0:
								data = [arr[0], arr[1], arr[3], arr[4]]
								break;
							default:
								data = [arr[1]]
						}
						return `<div class="ops-btns">${data.join('\n')}</div>`
					}
				}],
				formatData(data) {
					return {
						data: data.duplicated_search_rule_list
					};
				}
			})

			this.comps['table'].on('trclick', (row, $tr, $target) => {
				let operate = $target.data('operate');
				if (operate == 'edit') {
					this.editHandle(row)
				} else if (operate == 'start') {
					this.statusHandle(row, 1)
				} else if (operate == 'disable') {
					FxUI.MessageBox.confirm($t('确定禁用此条规则？'), $t('提示'), {
						confirmButtonText: $t('确定'),
						cancelButtonText: $t('取消'),
						type: 'warning'
					}).then(() => {
						this.statusHandle(row, 0)
					});
				} else if (operate == 'copy') {
					let count = this.comps['table'].getRowData().length
					if (count >= me.options.MAX_RULE_COUNT) {
						util.alert($t('最多创建{{MaxRule}}条规则', {MaxRule:this.options.MAX_RULE_COUNT}));
					} else {
						this.copyHandle(row);
					}
					
				} else if (operate == 'delete') {
					FxUI.MessageBox.confirm($t('确定删除此条规则？'), $t('提示'), {
						confirmButtonText: $t('确定'),
						cancelButtonText: $t('取消'),
						type: 'warning'
					}).then(() => {
						this.statusHandle(row, -1)
					});
				}
			});

			this.comps['table'].on('renderListComplete', function () {
				let MAX_RULE_COUNT = me.options.MAX_RULE_COUNT;
				let count = this.getRowData().length;
				me.searchBtn();
				me.createBtn();
				me.setTitleCount(MAX_RULE_COUNT, count);

				me.comps['create'].disabled = (count >= MAX_RULE_COUNT ? true : false)
				me.comps['create'].hidden = (count >= MAX_RULE_COUNT ? false : true)

			})

		},

		refreshTable() {
			this.comps['table'].setParam({
				pageNumber: 50
			}, true)
		},

		editHandle(row) {
			let me = this;
			field.edit({
				Model: field.Model.extend(model),
				View: field.View.extend(view),
				record_type: 'default__c',
				show_type: 'full',
				action_type: 'edit',
				className: 'repeat_rule_dialog',
				data: row,
				success: function () {
					me.refreshTable();
				}
			})
		},

		copyHandle(row) {
			let me = this;
			row = util.deepClone(row);
			row.name = row.name + $t('-副本')
			field.add({
				Model: field.Model.extend(model),
				View: field.View.extend(view),
				record_type: 'default__c',
				show_type: 'full',
				action_type: 'copy',
				className: 'repeat_rule_dialog',
				data: row,
				success: function () {
					me.refreshTable();
				}
			})
		},

		statusHandle(row, status) {
			let me = this;
			util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/leads_duplicated_processing/service/update_status',
				data: {
					table_name: 'biz_leads_duplicated_search',
					data_id: row.id,
					status: status
				},
				success(res) {
					if (res.Result.StatusCode == 0) { 
						util.remindSuccess(res.Value.message);
						me.refreshTable();
					}
				}
			})
		},

		searchBtn() {
			let me = this;
			if (this.comps['search']) return;
			this.comps['search'] = FxUI.create({
				wrapper: '.repeat_rule-title .j-srarch-rule',
				// 嵌套一层span 是为了解决 button组件 disabled 时, tooltip 不起作用的问题
				template: `<fx-input placeholder="${$t('搜索规则')}" v-model="keyword" size="mini" @change="clickSearch">
								<fx-button slot="append" icon="el-icon-search" @click="clickSearch"></fx-button>
							</fx-input>`,
				data() {
					return {
						keyword: ''
					}
				},
				methods: {
					clickSearch() {
						me.initTable(this.keyword);
					}
				}
			})
		},
		createBtn() {
			let me = this;
			if (this.comps['create']) return;
			let txt = $t('最多创建{{MaxRule}}条规则', {MaxRule:this.options.MAX_RULE_COUNT})
			this.comps['create'] = FxUI.create({
				wrapper: '.repeat_rule-title .j-create-rule',
				// 嵌套一层span 是为了解决 button组件 disabled 时, tooltip 不起作用的问题
				template: `<fx-tooltip effect="dark" content="${txt}" :disabled="hidden" placement="top">
							<span><fx-button plain size="mini" :disabled="disabled" @click="clickCreateHandle">${$t("新建查重规则")}</fx-button></span>
						</fx-tooltip>`,
				data() {
					return {
						disabled: false,
						hidden: true
					}
				},
				methods: {
					clickCreateHandle() {
						field.add({
							Model: field.Model.extend(model),
							View: field.View.extend(view),
							record_type: 'default__c',
							show_type: 'full',
							action_type: 'add',
							className: 'repeat_rule_dialog',
							data: {
								object_api_name: 'LeadsObj'
							},
							success: function () {
								me.refreshTable();
							}
						})

					}
				}
			})
		},

		getUsableRulesHtml(fulldata) {
			let data = []
			let usable_rules = fulldata.usable_rules;
			for (let i = 0; i < usable_rules.length; i++) {
				let conditions = usable_rules[i];
				let row = []
				for (let j = 0; j < conditions.conditions.length; j++) {
					const filter = conditions.conditions[j];
					let field = CRM.get(`fields.${fulldata.object_api_name}`)[filter.field_name];
					if (field) {
						row.push(`${field.label}`)
					}
				}
				if (i == 0) {
					data.push(`(${row.join(` AND `)})`)
				} else {
					data.push(` OR (${row.join(` AND `)})`)
				}
			}
			let text = data.join('') || $t('sfa.setting.sysobj.feild.disable');
			return `<div class="ruel-container">${text}</div>`;
		},

		setTitleCount(max, n) {
			$('.repeat_rule-count', this.$el).html(`(${n}/${max})`);
		},
		show: function () {
			this.$el.show();
		},
		hide: function () {
			this.$el.hide();
		},
		destroy() {
			for (let key in this.comps) {
				this.comps[key].destroy && this.comps[key].destroy();
			}
			this.comps = null;

		}
	})

	module.exports = RepeatDataProcessRule;
})
