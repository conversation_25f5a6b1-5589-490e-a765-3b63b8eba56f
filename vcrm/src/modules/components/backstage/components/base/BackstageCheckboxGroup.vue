<template>
    <div class="backstage-checkbox-group">
        <div class="crm-module-title" v-if="listData.title">
            <p>{{listData.title}}<span class="warn-tip" v-if="!listData.enabledClose">{{$t('sfa.crm.setting.backstage_checkbox_group.warn_cannot_closed', null, '*勾选后，不可取消')}}</span></p>
            <p class="subtitle" v-if="listData.subTitle">{{listData.subTitle}}</p>
            <p class="line" v-if="listData.showTitleLine" />
        </div>
        <div class="content">
            <div class="checkboxs">
                <fx-checkbox-group :value="listData.value">
                    <fx-checkbox v-for="(op, i) in listData.options" :key="i" :label="op.key" :disabled="getOpDisabled(op, listData.value.includes(op.key))" @change="(value) => handleChange(value, op.key, i)">
                        {{op.label}}
                    </fx-checkbox>
                </fx-checkbox-group>
            </div>
            <slot name="extra"></slot>
        </div>
    </div>
</template>
<script>
export default {
    name: "BackstageCheckboxGroup",
    props: {
        listData: {
            type: Object,
            require: true,
            // default() {
            //     return {
            //         enabledClose: false, // 开启后是否可关闭
            //         key: 'periodic_product_plugin', // key为数组string
            //         value: ['salesOrderObj', 'quoteObj'] // 默认值为string
            //     }
            // }
        },
        index: {
            type: Number
        }
    },
    data() {
        return {};
    },
    mounted() { },
    methods: {
        handleChange(value, key, i) {
            let values = Object.assign([], this.listData.value);
            values.includes(key)
                ? (values = values.filter(item => item !== key)) // 取消
                : values.push(key); // 选中
            this.$emit('change', {
                type: this.listData.type,
                key: this.listData.key,
                value: values,
                values
            });
        },
        getOpDisabled(op, val) {
            return (val && !this.listData.enableClose) ||
                (op.disabled === undefined ? false :
                    (_.isFunction(op.disabled) ? op.disabled() : op.disabled)
                )
        }
    }
}
</script>
<style lang="less" scoped>
.backstage-checkbox-group {
    .crm-module-title {
        p {
            font-size: 14px!important;
            line-height: 20px!important;
        }
        .warn-tip {
            font-size: 14px;
            line-height: 20px;
            color: var(--color-danger06, #ff522a);
            margin-left: 8px;
        }
    }
    .content {
        padding: 0 16px 16px;
        .checkboxs {
            display: flex;
            flex-direction: column;
            align-items: flex-start;

            .el-checkbox {
                font-size: 14px;
                line-height: 18px;
                color: var(--color-neutrals19);
                white-space: normal;
            }
        }
    }
}
</style>