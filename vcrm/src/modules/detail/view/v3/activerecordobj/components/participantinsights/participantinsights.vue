<template>
  <div class="canedit-card-list" ref="mainContainer" v-loading="listLoading">
    <div class="header-container" v-if="(hastheir_sideUsers || hasour_sideUsers) && allUserhasApiname">
      <div class="custom-radio-group">
        <div 
          v-if="hastheir_sideUsers"
          class="custom-radio-button" 
          :class="{ active: currentType === 'their_side' }"
          @click="currentType = 'their_side'"
        >
          {{ $t('sfa.crm.participantinsights.their_side') }}
        </div>
        <div 
          v-if="hasour_sideUsers"
          class="custom-radio-button" 
          :class="{ active: currentType === 'our_side' }"
          @click="currentType = 'our_side'"
        >
          {{ $t('sfa.crm.participantinsights.my_side') }}
        </div>
      </div>
      <div class="right-actions">
        <!-- <fx-button type="text" class="refresh-button" @click="refreshData">
          <span class="fx-icon-refresh"></span>
        </fx-button> -->
        <div v-if="showViewAllButton" class="view-all-button">
          <fx-button type="text" @click="toggleViewAll">
            {{ isViewAll ? $t('sfa.crm.participantinsights.collapse') : $t('sfa.crm.participantinsights.expand') }}
            <i :class="isViewAll ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          </fx-button>
        </div>
      </div>
    </div>
    <template v-if="resultUserDataList.length > 0">
      <!-- 联系人已全部绑定 -->
      <template v-if="allUserhasApiname">
        <timeline-component
          :comments="displayedTimelineData"
          :start-time="meetingStartTime"
          :end-time="meetingEndTime"
          :current-type="currentType"
          @segment-click="handleSegmentClick"
          @user-click="handleUserClick"
        />
      </template>
      <!-- 联系人未全部绑定 -->
      <template v-else>
        <div class="no-data">
          <div class="image-placeholder"></div>
          <div class="text">{{$t('crm.sfa.list.nodata.atip')}}</div>
          <div class="reload" @click="emitBindContact">{{$t('crm.sfa.emiss.bind_contact')}}</div>
        </div>
      </template>
    </template>

    <!-- 有选中联系人且全部绑定 -->
    <template v-if="selectedUser && allUserhasApiname">
      <user-content
        :selected-user="selectedUser"
        :dataId="dataId"
        :now-generate-insights="userLoadingMap[selectedUser && selectedUser.user_id] || false"
        @locate-source="handleLocateSource"
        @generate-insights="handleUserGenerateInsights"
        @generate-insights-done="handleUserGenerateInsightsDone"
      />
    </template>

    <!-- 没有联系人 -->
    <template v-if="!resultUserDataList.length || (hastheir_sideUsers || hasour_sideUsers)">
      <div class="no-data" style="margin-top: 12px;">
        <div class="image-placeholder"></div>
        <div class="text">{{$t('crm.sfa.list.nodata.tip')}}</div>
      </div>
    </template>
  </div>
</template>

<script>
import TimelineComponent from './timelinecomponent.vue'
import UserContent from './usercontent.vue'

export default {
  name: "ParticipantInsights",
  components: {
    TimelineComponent,
    UserContent
  },
  props: ["dataId", "apiName", "compInfo", "pluginService", "extendData"],
  data() {
    return {
      listLoading: false,
      resultUserDataList: [],
      meetingStartTime: '',
      meetingEndTime: '',
      currentType: 'their_side', // 默认显示我方参与人
      isViewAll: false, // 是否展示全部
      defaultDisplayCount: 5, // 默认显示条数
      selectedUser: null,
      allUserhasApiname: false,
      userLoadingMap: {}, // 新增：每个用户的 loading 状态
    }
  },
  computed: {
    filteredTimelineData() {
      // 根据类型过滤并按参与度排序
      const timelineData = this.resultUserDataList
        .filter(user => user.participant_types === this.currentType)
        .sort((a, b) => (b.participation_proportion || 0) - (a.participation_proportion || 0))
        .map(user => ({
          ...user,
          user: user.user_name,
          main: user.corpusEntries.map(entry => ({
            time: `${entry.startTime}-${entry.endTime}`,
            content: entry.content,
            originalData: entry
          }))
        }));
      return timelineData;
    },
    showViewAllButton() {
      return this.filteredTimelineData.length > this.defaultDisplayCount;
    },
    displayedTimelineData() {
      if (!this.showViewAllButton || this.isViewAll) {
        return this.filteredTimelineData;
      }
      return this.filteredTimelineData.slice(0, this.defaultDisplayCount);
    },
    hasour_sideUsers() {
      return this.resultUserDataList.some(user => user.participant_types === 'our_side');
    },
    hastheir_sideUsers() {
      return this.resultUserDataList.some(user => user.participant_types === 'their_side');
    }
  },
  watch: {
    resultUserDataList: {
      immediate: true,
      handler(newList) {
        // 当数据列表变化时，自动选择可用的类型
        if (this.currentType === 'our_side' && !this.hasour_sideUsers) {
          if (this.hastheir_sideUsers) {
            this.currentType = 'their_side';
          }
        } else if (this.currentType === 'their_side' && !this.hastheir_sideUsers) {
          if (this.hasour_sideUsers) {
            this.currentType = 'our_side';
          }
        }
      }
    },
    currentType: {
      handler(newType) {
        // 找到当前类型的第一个用户
        const firstUser = this.resultUserDataList.find(user => user.participant_types === newType);
        if (firstUser) {
          this.selectedUser = firstUser;
        }
      }
    }
  },
  methods: {
    emitBindContact(){
      try {
        this.$context.$emit('corpus.speaker.bind')
      } catch (error) {
          this.$message({
          message: $t("sfa.crm.participantinsights.pleasetocopus"),
          type: 'error',
        });
      }
    },
    toggleViewAll() {
      this.isViewAll = !this.isViewAll;
    },
    handleUserClick(userData) {
      const user = this.resultUserDataList.find(u => u.user_id === userData.user_id);
      this.selectedUser = user;
    },
    handleSegmentClick(userData, item) {
     this.$context.$emit('corpus.list.scroll.to', JSON.stringify([item.originalData.seq]));
    },
    handleLocateSource(a){
        this.$context.$emit('corpus.list.scroll.to', JSON.stringify(a?.quotaSeqList || []));
    },
    // 处理获取到的数据
    processData(userData, corpusData){
        // 处理会议开始和结束时间
        if (corpusData.length > 0) {
            this.meetingStartTime = corpusData[0].startTime.split('.')[0];  // "06:58.601" -> "06:58"
            this.meetingEndTime = corpusData[corpusData.length - 1].endTime.split('.')[0];
        }
        
        const processedUserData = userData.map(user => {
            const matchingCorpusEntries = corpusData.filter(corpusItem => corpusItem.userId === user.user_id)
                .map(item => ({
                    ...item,
                    startTime: item.startTime.split('.')[0],
                    endTime: item.endTime.split('.')[0]
                }));
            return {
                ...user,
                corpusEntries: matchingCorpusEntries
            };
        });
        
        // 检查userData中abc字段是否都有值
        const checkAbcFields = (userData) => {
          if (!userData || !Array.isArray(userData) || !userData.length) return false;
          return userData.every(item => item.user_api_name !== undefined && item.user_api_name !== null);
        };
        this.allUserhasApiname = checkAbcFields(processedUserData);
        console.log( this.allUserhasApiname);
        this.resultUserDataList = processedUserData;
    },
    // 获取参会人列表
    getUserList(){
      const _this = this;
        return new Promise((resolve,reject)=>{
         CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/activity_attendees_insight/service/get_attendees',
                    data: {
                        activeRecordId: _this.dataId,
                    },
                    success: (res) => {
                        const pageData = res.Value?.dataList || [];
                        resolve(pageData);
                    },
                    error: (err) => {
                        _this.listLoading = false;
                        reject(err);
                    }
                }, { errorAlertModel: 1 });
            
        })
    },
    // 获取预料列表
    getCorpusList(){
        const _this = this;
        return new Promise((resolve,reject)=>{
            const PAGE_SIZE = 1000;
            let offset = 0;
            let allData = [];
            const fetchPage = () => {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/activity_text/service/full_text_list',
                    data: {
                        objectId: _this.dataId,
                        tenantId: CRM.ea,
                        offset: offset,
                        limit: PAGE_SIZE
                    },
                    success: (res) => {
                        const pageData = res.Value?.dataList || [];
                        allData = allData.concat(pageData);
                        
                        if (pageData.length === PAGE_SIZE) {
                            // 如果返回数据等于页大小，说明可能还有更多数据
                            offset += PAGE_SIZE;
                            fetchPage();
                        } else {
                            // 数据获取完毕
                            resolve(allData);
                            _this.listLoading = false;
                        }
                    },
                    error: (err) => {
                        _this.listLoading = false;
                        reject(err);
                    }
                }, { errorAlertModel: 1 });
            };
            
            fetchPage();
        });
    },
       /**
     * 动态设置容器高度，适应窗口大小
     */
    setContainerHeight() {
      const container = this.$refs.mainContainer;
      if (!container) return;
      
      const containerTop = container.getBoundingClientRect().top;
      // 设置容器高度为视窗高度减去容器顶部距离
      container.style.maxHeight = `calc(100vh - 56px - ${containerTop}px)`;
      container.style.margin = '8px 0 0 6px';
    },
    /**
     * 初始化或刷新数据
     */
    initData() {
      this.listLoading = true;
      return Promise.all([this.getUserList(), this.getCorpusList()])
        .then(([userData, corpusData]) => {
          this.processData(userData, corpusData);
        })
        .catch(error => {
          this.resultUserDataList = [];
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    refreshData() {
      this.initData();
    },
    handleUserGenerateInsights(userId) {
      this.$set(this.userLoadingMap, userId, true);
    },
     handleUserGenerateInsightsDone(userId) {
      this.$set(this.userLoadingMap, userId, false);
    },
  },
  created() {
    this.initData();
  },
  mounted() {
  // this.$context.$on('corpus.speaker.bind.complete', () => {
  //   this.initData();
  // })
  this.$context.$on('corpus.user.list.updated',() => {
    console.log(new Date().getTime());
    this.initData();
  })
  this.setContainerHeight();
    // 监听窗口大小变化，重新计算高度
    window.addEventListener('resize', this.setContainerHeight);
  },
    beforeDestroy() {
      this.$context.off('corpus.user.list.updated');
      // 清理事件监听
      window.removeEventListener('resize', this.setContainerHeight);
  },
}
</script>

<style scoped lang="less">
.canedit-card-list {
  display: flex;
  padding: 12px;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  border-radius: 8px;
  background: #f7f9fa;
  position: relative;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }

  .header-container {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    gap: 16px;

    .right-actions {
      display: flex;
      align-items: center;
      gap: 8px;

      .refresh-button {
        color: var(--color-info06);
        font-size: 13px;
        padding: 0;
        height: 24px;
        line-height: 24px;

        &:hover {
          opacity: 0.8;
        }
      }
    }
  }

  .custom-radio-group {
    display: flex;
    height: 24px;
    justify-content: center;
    align-items: center;
    background: #E7EAF3;
    border-radius: 6px;
    cursor: pointer;

    .custom-radio-button {
      display: flex;
      color: var(--color-neutrals19);
      padding: 2px 16px;
      justify-content: center;
      align-items: center;
      gap: 8px;
      background: #E7EAF3;
      border-radius: 6px;
      font-size: 13px;
      transition: all 0.2s ease;

      &:hover {
        color: var(--color-primary06);
      }
      
      &.active {
        font-weight: 700;
        background: #fff;
        color: var(--color-primary06);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      
      & + .custom-radio-button {
        margin-left: 2px;
      }
    }
  }

  .view-item {
    display: flex;
    padding: 8px 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    flex: 1 0 0;
    border-radius: 4px;
    background: var(--color-neutrals02);
  .more-content-button{
      color: var(--color-info06);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
  }
    .view-item-title {
      color: var(--color-neutrals19);
      /* Text/13-B */
      font-size: 13px;
      font-style: normal;
      font-weight: 700;
      line-height: 18px; /* 138.462% */
    }

    .view-item-title.speaker_summary {
      color: var(--color-neutrals11);
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 18px;
    }

    p {
      color: var(--color-neutrals19);
      max-height: 90px;
      overflow: hidden;
      transition: max-height 0.3s ease;
      font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: 18px; /* 138.462% */
    }

    // p.overflow-hidden {
    //   overflow: hidden;
    //   text-overflow: ellipsis;
    //   display: -webkit-box;
    //   -webkit-line-clamp: 5; /* 设置行数 */
    //   -webkit-box-orient: vertical;
    // }
  }
  span {
    flex: 1 0 0;
    position: absolute;
    right: 16px;
    color: var(---Light, #c1c5ce);
    text-align: right;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
  }

  .view-item.all_content_summary {
    background-color: white;
    position: relative;
    // p {
    //   max-height: 90px;
    //   overflow: hidden;
    //   transition: max-height 0.3s ease;
    // }
  }

  // .view-item.all_content_summary.expanded {
  //   max-height: none;
  // }

  .view-all-button {
    .fx-button {
      color: var(--color-info06);
      font-size: 13px;
      padding: 0;
      height: 24px;
      line-height: 24px;
      
      i {
        margin-left: 4px;
        font-size: 12px;
      }
    }
  }
}
 .no-data {
      width: 100%;
      border-radius: 8px;
      background: #fff;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 265px;
      text-align: center;
      font-family: Arial, sans-serif;
      .image-placeholder {
            width: 150px;
            height: 120px;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
            background: image-set(url("~@assets/images/nodata2.png") 1x,
                    url("~@assets/images/<EMAIL>") 2x) center center no-repeat;
        }
        .text {
            color: var(--Text-H2, #545861);
            text-align: center;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 142.857% */
        }
        .reload {
            color: var(--Text-Blue, #0C6CFF);
            text-align: center;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 142.857% */
        }
    }

</style>
