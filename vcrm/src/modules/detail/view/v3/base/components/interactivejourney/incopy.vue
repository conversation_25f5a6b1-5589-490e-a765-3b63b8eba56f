<template>
  <div class="view-graph-container" @click.self="resetAllStates" @mouseleave="onContainerMouseLeave">
    <div class="view-graph-header">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M7.5 0.100586C11.6421 0.100586 15 3.45845 15 7.60059C15 8.36536 14.8855 9.1034 14.6728 9.79851L13.249 9.32352C13.4123 8.7778 13.5 8.19944 13.5 7.60059C13.5 4.28688 10.8137 1.60059 7.5 1.60059C4.18629 1.60059 1.5 4.28688 1.5 7.60059C1.5 10.9143 4.18629 13.6006 7.5 13.6006C8.09885 13.6006 8.67722 13.5129 9.22294 13.3495L9.69792 14.7734C9.00282 14.9861 8.26478 15.1006 7.5 15.1006C3.35786 15.1006 0 11.7427 0 7.60059C0 3.45845 3.35786 0.100586 7.5 0.100586ZM10.1487 9.61681L14.2437 10.9818C14.3485 11.0168 14.4051 11.13 14.3702 11.2348C14.3604 11.2643 14.3438 11.291 14.3219 11.313L13.213 12.4218L14.5507 13.7595C14.797 14.0058 14.797 14.405 14.5507 14.6513C14.3045 14.8976 13.9052 14.8976 13.6589 14.6513L12.3213 13.3136L11.2124 14.4225C11.1343 14.5006 11.0077 14.5006 10.9296 14.4225C10.9076 14.4005 10.8911 14.3738 10.8812 14.3443L9.51623 10.2493C9.4289 9.9873 9.57048 9.70414 9.83246 9.61681C9.93509 9.5826 10.0461 9.5826 10.1487 9.61681ZM7.5 3.10059C9.98528 3.10059 12 5.1153 12 7.60059C12 8.03359 11.9388 8.45231 11.8247 8.84858L10.3994 8.37384C10.465 8.12714 10.5 7.86795 10.5 7.60059C10.5 5.94373 9.15685 4.60059 7.5 4.60059C5.84315 4.60059 4.5 5.94373 4.5 7.60059C4.5 9.25744 5.84315 10.6006 7.5 10.6006C7.76772 10.6006 8.02726 10.5655 8.27425 10.4997L8.74899 11.925C8.35243 12.0393 7.93337 12.1006 7.5 12.1006C5.01472 12.1006 3 10.0859 3 7.60059C3 5.1153 5.01472 3.10059 7.5 3.10059Z" fill="#36C2B6"/>
          </svg>
          <span class="view-graph-header-title-text">客户旅程</span>
    </div>
    <!-- 图表容器 -->
    <div class="view-graph" ref="radarGraph" v-loading="loading"></div>
    
    <div class="ai-summary-advice" v-show="showAiSummaryAdvice">
    <!-- AI总结 -->
        <div class="ai-contect" v-if="!profileContext">
            <div class="header">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                  <path d="M7.9999 2.95985C10.6946 1.13205 13.2621 0.539778 14.4114 1.68901C15.5603 2.83837 14.9682 5.40596 13.1405 8.10048C14.9682 10.7951 15.5606 13.3627 14.4114 14.512C13.2622 15.6612 10.6945 15.0688 7.9999 13.2411C5.30538 15.0688 2.73778 15.6609 1.58843 14.512C0.439192 13.3627 1.03146 10.7952 2.85926 8.10048C1.03143 5.40578 0.439185 2.83825 1.58843 1.68901C2.73767 0.539771 5.30519 1.13202 7.9999 2.95985ZM12.5129 8.96637C12.0697 9.54151 11.5736 10.1168 11.0285 10.6786L10.8046 10.9052L10.5624 11.1435C10.0057 11.6825 9.43676 12.1745 8.86709 12.6135C11.0618 14.0301 13.0116 14.4977 13.7043 13.8049C14.3927 13.1166 13.9308 11.1649 12.5129 8.96637ZM3.48687 8.96767C2.071 11.1615 1.60243 13.1119 2.29546 13.8049C2.98402 14.4929 4.93562 14.0314 7.13401 12.6135C6.55894 12.1704 5.98348 11.6741 5.42177 11.1291L5.1952 10.9052L4.95692 10.663C4.41797 10.1064 3.92587 9.53728 3.48687 8.96767ZM7.9999 4.18772C7.45646 4.59018 6.90581 5.04738 6.36317 5.55621L6.13791 5.76975L5.89573 6.00803C5.20799 6.69579 4.60322 7.4037 4.08713 8.10048C4.48955 8.64384 4.94687 9.19465 5.45562 9.7372L5.66916 9.96247L5.90224 10.1981C6.59239 10.8883 7.30252 11.4958 8.0012 12.0133C8.5444 11.6109 9.09423 11.1534 9.63662 10.6448L9.86188 10.4312L10.0976 10.1981C10.7878 9.50793 11.3952 8.79793 11.9127 8.09918C11.5104 7.55605 11.0527 7.00608 10.5442 6.46376L10.3306 6.2385L10.0923 5.99631C9.40467 5.30866 8.6966 4.70376 7.9999 4.18772ZM8.04547 6.38694C8.07078 6.39453 8.09449 6.40861 8.11318 6.4273C8.13153 6.44585 8.14471 6.46873 8.15224 6.49371L8.2525 6.83225C8.32346 7.07152 8.45354 7.29 8.63011 7.46637C8.80669 7.64271 9.02489 7.77203 9.26422 7.84267L9.60016 7.94163C9.63297 7.95137 9.66167 7.97152 9.68219 7.99892C9.70271 8.02636 9.71337 8.05971 9.71344 8.09397C9.71344 8.12836 9.70278 8.16279 9.68219 8.19033C9.66165 8.21757 9.63288 8.23794 9.60016 8.24762L9.25901 8.34918C9.02126 8.42037 8.80421 8.54989 8.6288 8.72548C8.45346 8.90106 8.32474 9.11789 8.2538 9.35569L8.15224 9.69423C8.14237 9.72714 8.12251 9.75706 8.09495 9.77757C8.06749 9.79788 8.03405 9.8088 7.9999 9.80882C7.96576 9.80882 7.93231 9.79783 7.90484 9.77757C7.87728 9.75706 7.85612 9.72714 7.84625 9.69423L7.74599 9.35569C7.67507 9.11794 7.54628 8.90104 7.37099 8.72548C7.19559 8.54989 6.97853 8.42037 6.74078 8.34918L6.39963 8.24762C6.36669 8.23779 6.3368 8.21663 6.3163 8.18902C6.29607 8.16154 6.28495 8.12812 6.28505 8.09397C6.28516 8.05964 6.29698 8.02636 6.3176 7.99892C6.33816 7.97161 6.36686 7.9513 6.39963 7.94163L6.73557 7.84267C6.97483 7.77202 7.19315 7.64266 7.36969 7.46637C7.5462 7.29002 7.67504 7.07147 7.74599 6.83225L7.84625 6.49371C7.85828 6.45357 7.88637 6.42014 7.92307 6.39996C7.96022 6.37975 8.00489 6.37501 8.04547 6.38694ZM7.13271 3.58745C4.93889 2.17155 2.98849 1.70301 2.29546 2.39605C1.6071 3.0844 2.06885 5.03595 3.48687 7.2346C3.93008 6.65945 4.42617 6.08414 4.97125 5.52235L5.1952 5.29579L5.43739 5.05751C5.99409 4.51848 6.56301 4.02651 7.13271 3.58745ZM13.7043 2.39605C13.016 1.70769 11.0644 2.16947 8.86578 3.58745C9.44085 4.03062 10.0163 4.52684 10.578 5.07183L10.8046 5.29579L11.0429 5.53798C11.5818 6.09459 12.0739 6.66369 12.5129 7.23329C13.9295 5.03873 14.3968 3.08905 13.7043 2.39605Z" fill="url(#paint0_radial_4495_53062)"/>
                  <defs>
                    <radialGradient id="paint0_radial_4495_53062" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(3.34496 15.0999) scale(15.3997 15.3993)">
                      <stop stop-color="#0099FF"/>
                      <stop offset="0.6" stop-color="#A033FF"/>
                      <stop offset="0.9" stop-color="#FF5280"/>
                      <stop offset="1" stop-color="#FF7061"/>
                    </radialGradient>
                  </defs>
                </svg>
                <span class="text">AI总结</span>
            </div>
             <div class="contect" v-if="aiSummary" v-html="aiSummary">

             </div>
              <div  class="no-data" v-else>
                  <div class="image-placeholder"></div>
            <div class="text">{{$t('crm.sfa.list.nodata.tip')}}</div>
          </div>

        </div>
    <!-- AI建议 -->
    <div class="ai-contect">
            <div class="header">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                  <path d="M7.9999 2.95985C10.6946 1.13205 13.2621 0.539778 14.4114 1.68901C15.5603 2.83837 14.9682 5.40596 13.1405 8.10048C14.9682 10.7951 15.5606 13.3627 14.4114 14.512C13.2622 15.6612 10.6945 15.0688 7.9999 13.2411C5.30538 15.0688 2.73778 15.6609 1.58843 14.512C0.439192 13.3627 1.03146 10.7952 2.85926 8.10048C1.03143 5.40578 0.439185 2.83825 1.58843 1.68901C2.73767 0.539771 5.30519 1.13202 7.9999 2.95985ZM12.5129 8.96637C12.0697 9.54151 11.5736 10.1168 11.0285 10.6786L10.8046 10.9052L10.5624 11.1435C10.0057 11.6825 9.43676 12.1745 8.86709 12.6135C11.0618 14.0301 13.0116 14.4977 13.7043 13.8049C14.3927 13.1166 13.9308 11.1649 12.5129 8.96637ZM3.48687 8.96767C2.071 11.1615 1.60243 13.1119 2.29546 13.8049C2.98402 14.4929 4.93562 14.0314 7.13401 12.6135C6.55894 12.1704 5.98348 11.6741 5.42177 11.1291L5.1952 10.9052L4.95692 10.663C4.41797 10.1064 3.92587 9.53728 3.48687 8.96767ZM7.9999 4.18772C7.45646 4.59018 6.90581 5.04738 6.36317 5.55621L6.13791 5.76975L5.89573 6.00803C5.20799 6.69579 4.60322 7.4037 4.08713 8.10048C4.48955 8.64384 4.94687 9.19465 5.45562 9.7372L5.66916 9.96247L5.90224 10.1981C6.59239 10.8883 7.30252 11.4958 8.0012 12.0133C8.5444 11.6109 9.09423 11.1534 9.63662 10.6448L9.86188 10.4312L10.0976 10.1981C10.7878 9.50793 11.3952 8.79793 11.9127 8.09918C11.5104 7.55605 11.0527 7.00608 10.5442 6.46376L10.3306 6.2385L10.0923 5.99631C9.40467 5.30866 8.6966 4.70376 7.9999 4.18772ZM8.04547 6.38694C8.07078 6.39453 8.09449 6.40861 8.11318 6.4273C8.13153 6.44585 8.14471 6.46873 8.15224 6.49371L8.2525 6.83225C8.32346 7.07152 8.45354 7.29 8.63011 7.46637C8.80669 7.64271 9.02489 7.77203 9.26422 7.84267L9.60016 7.94163C9.63297 7.95137 9.66167 7.97152 9.68219 7.99892C9.70271 8.02636 9.71337 8.05971 9.71344 8.09397C9.71344 8.12836 9.70278 8.16279 9.68219 8.19033C9.66165 8.21757 9.63288 8.23794 9.60016 8.24762L9.25901 8.34918C9.02126 8.42037 8.80421 8.54989 8.6288 8.72548C8.45346 8.90106 8.32474 9.11789 8.2538 9.35569L8.15224 9.69423C8.14237 9.72714 8.12251 9.75706 8.09495 9.77757C8.06749 9.79788 8.03405 9.8088 7.9999 9.80882C7.96576 9.80882 7.93231 9.79783 7.90484 9.77757C7.87728 9.75706 7.85612 9.72714 7.84625 9.69423L7.74599 9.35569C7.67507 9.11794 7.54628 8.90104 7.37099 8.72548C7.19559 8.54989 6.97853 8.42037 6.74078 8.34918L6.39963 8.24762C6.36669 8.23779 6.3368 8.21663 6.3163 8.18902C6.29607 8.16154 6.28495 8.12812 6.28505 8.09397C6.28516 8.05964 6.29698 8.02636 6.3176 7.99892C6.33816 7.97161 6.36686 7.9513 6.39963 7.94163L6.73557 7.84267C6.97483 7.77202 7.19315 7.64266 7.36969 7.46637C7.5462 7.29002 7.67504 7.07147 7.74599 6.83225L7.84625 6.49371C7.85828 6.45357 7.88637 6.42014 7.92307 6.39996C7.96022 6.37975 8.00489 6.37501 8.04547 6.38694ZM7.13271 3.58745C4.93889 2.17155 2.98849 1.70301 2.29546 2.39605C1.6071 3.0844 2.06885 5.03595 3.48687 7.2346C3.93008 6.65945 4.42617 6.08414 4.97125 5.52235L5.1952 5.29579L5.43739 5.05751C5.99409 4.51848 6.56301 4.02651 7.13271 3.58745ZM13.7043 2.39605C13.016 1.70769 11.0644 2.16947 8.86578 3.58745C9.44085 4.03062 10.0163 4.52684 10.578 5.07183L10.8046 5.29579L11.0429 5.53798C11.5818 6.09459 12.0739 6.66369 12.5129 7.23329C13.9295 5.03873 14.3968 3.08905 13.7043 2.39605Z" fill="url(#paint0_radial_4495_53062)"/>
                  <defs>
                    <radialGradient id="paint0_radial_4495_53062" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(3.34496 15.0999) scale(15.3997 15.3993)">
                      <stop stop-color="#0099FF"/>
                      <stop offset="0.6" stop-color="#A033FF"/>
                      <stop offset="0.9" stop-color="#FF5280"/>
                      <stop offset="1" stop-color="#FF7061"/>
                    </radialGradient>
                  </defs>
                </svg>
                <span class="text">AI建议</span>
            </div>
              <div class="contect" v-if="aiAdvice" v-html="aiAdvice">

             </div>
              <div  class="no-data" v-else>
                  <div class="image-placeholder"></div>
            <div class="text">{{$t('crm.sfa.list.nodata.tip')}}</div>
          </div>
        </div>
    </div>
    
    <!-- 动态渲染的图标 -->
    <template v-if="showInteractiveIcons" v-for="(icon, index) in interactiveIcons">
      <!-- 图标组：当天有多个事件 -->
      <div
        v-if="icon.isGrouped"
        :key="`group-${index}`"
        class="icon-group-container"
        :class="{ 'dimmed': highlightedSeriesIndex !== null && highlightedSeriesIndex !== 2 }"
        :style="{ left: getGroupPosition(icon).left, top: getGroupPosition(icon).top }"
        @mouseenter="highlightInteractiveLine"
        @mouseleave="restoreLineStyles"
      >
        <!-- 展开状态：显示所有图标 -->
        <template v-if="expandedIconGroupIndex === index">
          <span
            v-for="(item, itemIndex) in icon.items"
            :key="itemIndex"
            class="interactive-icon"
            :class="[iconConfigClass[item.interactive_types || 'default' ], item.interactive_types || 'default']"
            @click.stop="onIconClick(item, $event)"
            @mouseenter.stop="onIconMouseOver(item, $event)"
          ></span>
        </template>
        <!-- 默认状态：显示主图标和更多按钮 -->
        <template v-else>
          <span
            class="interactive-icon"
            :class="[iconConfigClass[icon.type] || 'default',icon.type]"
            @click.stop="onIconClick(icon.items[0], $event)"
            @mouseenter.stop="onIconMouseOver(icon.items[0], $event)"
          ></span>
          <span
            class="interactive-icon icon-more"
            @click.stop="toggleIconGroup(index)"
            @mouseenter.stop="toggleIconGroup(index)"
          >
           ...
          </span>
        </template>
      </div>
      <!-- 单个图标 -->
      <span
        v-else
        :key="`single-${index}`"
        class="interactive-icon"
        :class="[iconConfigClass[icon.type || 'default'] ,icon.type, { 'dimmed': highlightedSeriesIndex !== null && highlightedSeriesIndex !== 2 }]"
        :style="{ left: (icon.x - 8) + 'px', top: (icon.y - 8) + 'px' }"
        @click.stop="onIconClick(icon.items[0], $event)"
        @mouseenter.stop="onIconMouseOver(icon.items[0], $event)"
        @mouseenter="highlightInteractiveLine"
        @mouseleave="restoreLineStyles"
      ></span>
    </template>

    <!-- 新的 Popover 组件 -->
    <detail-popover
      ref="detailPopover"
      v-model="showDetailPopover"
      :position="popoverPosition"
      :item="selectedItem"
      @content-ready="repositionPopover"
    />
  </div>
</template>

<script>
import { requireEcharts } from '@common/require';
import DetailPopover from './popover.vue';

export default {
  name: "InteractiveJourney",
  components: { DetailPopover },
  props: ["compInfo", "apiName", "dataId","profileContext","fromCustomerPofile"],
  data() {
    return {
      loadingData: false,
      loading: false,
      myChart: null,
      interactiveScoreList: [],
      profileIntegratedScoreList: [],
      profileItemScoreList: [],
      interactiveIcons: [], // 新增：用于存储HTML图标的数据
      expandedIconGroupIndex: null, // 新增：记录当前展开的图标组索引
      showDetailPopover: false, // 控制Popover显示
      selectedItem: null,       // 存储弹窗所需的数据
      popoverPosition: { // 用于手动定位Popover
        position: 'fixed',
        top: '0px',
        left: '0px',
        transform: 'translateY(-50%)',
        zIndex: 100,
        visibility: 'hidden', // 默认隐藏，通过逻辑控制
      },
      lastPopoverRect: null, // 新增：用于存储上一次popover触发元素的rect
      resizeObserver: null, // 新增：用于监听容器尺寸变化
      resizeTimeout: null,  // 新增：用于防抖
      highlightedSeriesIndex: null, // 新增：用于追踪高亮的系列
      restoreStylesListener: null, // 新增：用于存储mouseleave事件监听器
      showAiSummaryAdvice : true,//是否显示AI总结和AI建议
      aiSummary:[],//AI总结
      aiAdvice:[],//AI建议
      showInteractiveIcons: true, // 新增：控制互动图标的显示
      iconConfigClass:{
         meeting: 'fx-icon-tengxunhuiyi',
         default:'fx-icon-f-obj-app107',
         email:'fx-icon-f-email',
         call:"fx-icon-obj-app405",
         visit:"fx-icon-obj-app367"
      }
    };
  },
  watch: {
    // 新增：监听Popover的显示状态，以添加/移除全局点击监听器
    showDetailPopover(newValue) {
      if (newValue) {
        window.addEventListener('click', this.handleWindowClick, true);
      } else {
        window.removeEventListener('click', this.handleWindowClick, true);
      }
    },
  },
  created() {
    this.getAllData();
  },
  mounted() {
    // 我们不再监听window，而是观察图表容器本身
    this.setupResizeObserver();
  },
  beforeDestroy() {
    this.myChart && this.myChart.dispose();
    // 组件销毁时，确保移除Observer和全局点击监听器
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    window.removeEventListener('click', this.handleWindowClick, true);
  },
  methods: {
    getAllData() {
      const me = this;
      let requireData;
      me.loadingData = true;
      if (me.apiName === 'NewOpportunityObj') {
        requireData = {
          new_opportunity_id: me.dataId,
          show_latest_profile_items: me.showAiSummaryAdvice ? true : void 0
        }
      } else {
        requireData = {
          account_id: me.dataId,
          show_latest_profile_items: me.showAiSummaryAdvice ? true : void 0
        }
      }
      CRM.util.FHHApi({
        url: "/EM1HNCRM/API/v1/object/interactive_journey/service/query",
        data: requireData,
        success: function(res) {
          console.log(res,me.profileContext,me.fromCustomerPofile);
          const { interactive_score_list,profile_integrated_score_list,profile_item_score_list } = res.Value;
          me.interactiveScoreList = interactive_score_list || [];
          me.profileIntegratedScoreList = profile_integrated_score_list || [];
          me.profileItemScoreList = profile_item_score_list || []
          // me.profileItemScoreList = []
          if(me.showAiSummaryAdvice){
            me.aiSummary = res.Value?.latest_profile_item_score?.summary || '';
            me.aiAdvice = res.Value?.latest_profile_advice?.advice || '';
          }
          me.initChartView();
        },
        fail: function(err) {
          console.error("Failed to fetch data:", err);
          me.initChartView(); // Initialize chart even on failure
        },
        complete: function() {
          me.loadingData = false;
        },
      });
    },

    initChartView() {
      const me = this;
      me.loading = true;
      requireEcharts().then((echarts) => {
        me.myChart && me.myChart.dispose();
        if (!me.$refs.radarGraph) return;
        
        const myChart = me.myChart = echarts.init(me.$refs.radarGraph);

        // 1. 生成6周42天的时间轴
        const dates = [];
        for (let i = 41; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            dates.push(date);
        }

        // 2. 处理 profileIntegratedScoreList 数据
        const integratedLineData = [];
        if (me.profileIntegratedScoreList && me.profileIntegratedScoreList.length > 0) {
            const groupedByDay = me.profileIntegratedScoreList.reduce((acc, item) => {
                if (item && item.create_time && item.integrated_score) {
                    const eventDate = new Date(parseInt(item.create_time, 10));
                    const dateString = `${eventDate.getFullYear()}-${eventDate.getMonth()}-${eventDate.getDate()}`;
                    if (!acc[dateString]) {
                        acc[dateString] = [];
                    }
                    acc[dateString].push(item);
                }
                return acc;
            }, {});

            for (const dateString in groupedByDay) {
                const dayData = groupedByDay[dateString];
                if (dayData.length > 0) {
                    const avgScore = dayData.reduce((sum, item) => sum + parseInt(item.integrated_score, 10), 0) / dayData.length / 20;
                    const eventDate = new Date(parseInt(dayData[0].create_time, 10));
                    const dayIndex = dates.findIndex(d =>
                        d.getFullYear() === eventDate.getFullYear() &&
                        d.getMonth() === eventDate.getMonth() &&
                        d.getDate() === eventDate.getDate()
                    );

                    if (dayIndex !== -1) {
                        integratedLineData.push([dayIndex, avgScore]);
                    }
                }
            }
        }

        // 3. 处理 profileItemScoreList 数据
        const itemLineData = [];
        if (me.profileItemScoreList) {
          me.profileItemScoreList.forEach(item => {
            if (item && item.create_time && item.score) {
              const score = parseInt(item.score, 10) / 20;
              
              const eventDate = new Date(parseInt(item.create_time, 10));
              const dayIndex = dates.findIndex(d => 
                  d.getFullYear() === eventDate.getFullYear() &&
                  d.getMonth() === eventDate.getMonth() &&
                  d.getDate() === eventDate.getDate()
              );
              
              if (dayIndex !== -1) {
                  itemLineData.push([dayIndex, score]);
              }
            }
          });
        }
        
        // 4. 处理 interactiveScoreList 数据
        const interactiveLineData = []; // 用于ECharts灰色线
        const iconsData = []; // 用于Vue HTML图标
        if (me.interactiveScoreList) {
          me.interactiveScoreList.forEach(dayData => {
            if (dayData && dayData.length > 0) {
              const firstItem = dayData[0];
              const score = dayData.reduce((sum, item) => sum + (item.interactive_scores ? parseInt(item.interactive_scores, 10) : 0), 0) / dayData.length;
              const type = firstItem.interactive_types || 'default';

              const eventDate = new Date(parseInt(firstItem.create_time, 10));
              const dayIndex = dates.findIndex(d => 
                  d.getFullYear() === eventDate.getFullYear() &&
                  d.getMonth() === eventDate.getMonth() &&
                  d.getDate() === eventDate.getDate()
              );

              if (dayIndex !== -1) {
                interactiveLineData.push([dayIndex, score]);
                iconsData.push({
                  dayIndex,
                  score,
                  type,
                  isGrouped: dayData.length > 1,
                  items: dayData,
                  x: 0, 
                  y: 0, 
                });
              }
            }
          });
        }
        me.interactiveIcons = iconsData;

        const xAxisDates = dates.map((d, index) => {
            if ((index + 4) % 7 === 0) {
              const year = d.getFullYear();
              const month = (d.getMonth() + 1).toString().padStart(2, '0');
              const day = d.getDate().toString().padStart(2, '0');
              return `${year}/${month}/${day}`;
            }
            return '';
        });

        const option = {
          legend: {
            data: [
                { name: '综合分数' },
                { name: '单项分数' },
                { name: '互动分数', itemStyle: { color: '#cccccc' } }
            ],
            top: '5%',
            left: 'center',
          },
          tooltip: {
            trigger: 'item',
            formatter: function(params) {
              if (params.seriesName !== 'background') {
                const originalScore = params.value[1] * 20;
                return `${params.seriesName}: ${originalScore.toFixed(0)}`;
              }
              return null;
            }
          },
          grid: { left: '0', right: '0', top: '20%', bottom: '15%' },
          xAxis: {
            type: 'category',
            data: xAxisDates,
            boundaryGap: false,
            show: true,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              color: '#888',
              interval: 0
            }
          },
          yAxis: {
            type: 'value',
            min: -6,
            max: 6,
            show: false,
          },
          series: [{
            name: '综合分数',
            type: 'line',
            data: integratedLineData, 
            smooth: false,
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: '#28a745'
            },
            lineStyle: {
              color: '#28a745',
              width: 2,
            },
            triggerLineEvent: true,
          }, {
            name: '单项分数',
            type: 'line',
            data: itemLineData, 
            smooth: false,
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: '#007bff'
            },
            lineStyle: {
              color: '#007bff',
              width: 2,
            },
            triggerLineEvent: true,
          }, {
            name: '互动分数',
            type: 'line',
            data: interactiveLineData,
            smooth: false,
            symbol: 'none',
            lineStyle: {
              color: '#cccccc',
              width: 2,
            },
            triggerLineEvent: true,
          }, {
            name: 'background',
            type: 'line',
            data: [],
            showSymbol: false,
            legendHoverLink: false,
            markArea: {
              silent: true,
              data: [
                [
                  {
                    yAxis: 0,
                    itemStyle: {
                      color: {
                        type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
                        colorStops: [{ offset: 0, color: '#FFFFFF' }, { offset: 1, color: '#F1FBFB' }]
                      }
                    }
                  },
                  { yAxis: 6 }
                ],
                [
                  {
                    yAxis: -6,
                    itemStyle: {
                      color: {
                        type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
                        colorStops: [{ offset: 0, color: '#FFF8F4' }, { offset: 1, color: '#FFFFFF' }]
                      }
                    }
                  },
                  { yAxis: 0 }
                ]
              ]
            }
          }]
        };
        
        myChart.setOption(option);
        me.loading = false;

        myChart.on('legendselectchanged', (params) => {
            this.showInteractiveIcons = params.selected['互动分数'];
        });

        myChart.on('mouseover', (params) => {
          this.showDetailPopover = false;
          this.popoverPosition.visibility = 'hidden';
          
          if (params.seriesIndex !== undefined && params.seriesName !== 'background') {
            me.highlightedSeriesIndex = params.seriesIndex;
            myChart.setOption({
              series: [
                { lineStyle: { opacity: params.seriesIndex === 0 ? 1 : 0.2, width: params.seriesIndex === 0 ? 3 : 2 }, itemStyle: { opacity: params.seriesIndex === 0 ? 1 : 0.2 } },
                { lineStyle: { opacity: params.seriesIndex === 1 ? 1 : 0.2, width: params.seriesIndex === 1 ? 3 : 2 }, itemStyle: { opacity: params.seriesIndex === 1 ? 1 : 0.2 } },
                { lineStyle: { opacity: params.seriesIndex === 2 ? 1 : 0.2, width: params.seriesIndex === 2 ? 3 : 2 } }
              ]
            });
          }
        });

        const restoreOriginalStyles = () => {
          if (me.highlightedSeriesIndex !== null) {
            me.highlightedSeriesIndex = null;
            myChart.setOption({
              series: [
                { lineStyle: { opacity: 1, width: 2 }, itemStyle: { opacity: 1 } },
                { lineStyle: { opacity: 1, width: 2 }, itemStyle: { opacity: 1 } },
                { lineStyle: { opacity: 1, width: 2 } }
              ]
            });
          }
        };
        
        myChart.on('mouseout', restoreOriginalStyles);
        
        me.$nextTick(() => {
          me.updateIconPositions();
        });

      }).catch(err => {
        console.error("ECharts loading failed:", err);
        me.loading = false;
      });
      if (!this.resizeObserver) {
          this.setupResizeObserver();
      }
    },

    updateIconPositions() {
      if (!this.myChart || !this.interactiveIcons || !this.$refs.radarGraph) return;

      const chartEl = this.$refs.radarGraph;
      const offsetX = chartEl.offsetLeft;
      const offsetY = chartEl.offsetTop;

      this.interactiveIcons.forEach(icon => {
        const pixelCoord = this.myChart.convertToPixel('grid', [icon.dayIndex, icon.score]);
        if (pixelCoord) {
          icon.x = pixelCoord[0] + offsetX;
          icon.y = pixelCoord[1] + offsetY;
        }
      });
    },

    toggleIconGroup(index) {
      this.showDetailPopover = false;
      if (this.expandedIconGroupIndex === index) {
        this.expandedIconGroupIndex = null;
      } else {
        this.expandedIconGroupIndex = index;
      }
    },

    resetAllStates() {
      this.expandedIconGroupIndex = null;
      this.showDetailPopover = false;
      this.popoverPosition.visibility = 'hidden';
      if(this.myChart) {
        this.highlightedSeriesIndex = null;
        this.myChart.setOption({
          series: [
            { lineStyle: { opacity: 1, width: 2 }, itemStyle: { opacity: 1 } },
            { lineStyle: { opacity: 1, width: 2 }, itemStyle: { opacity: 1 } },
            { lineStyle: { opacity: 1, width: 2 } }
          ]
        });
      }
    },
    
    getGroupPosition(icon) {
      const isExpanded = this.interactiveIcons.indexOf(icon) === this.expandedIconGroupIndex;
      if (isExpanded) {
        const width = (16 + 4 + 4) * icon.items.length + 8;
        return {
          left: (icon.x - width / 2) + 'px',
          top: (icon.y - 14) + 'px',
        };
      } else {
        return {
          left: (icon.x - 28) + 'px',
          top: (icon.y - 14) + 'px',
        };
      }
    },

    onIconClick(item, event) {
      this._resetChartStyles();
      if (this.expandedIconGroupIndex !== null) {
        const parentGroup = event.target.closest('.icon-group-container');
        if (!parentGroup) {
          this.expandedIconGroupIndex = null;
        }
      }
    },

    onIconMouseOver(item, event) {
      this._showPopover(event.target.getBoundingClientRect());
      this.$nextTick(() => {
        this.selectedItem = item;
      });
    },
    
    onContainerMouseLeave() {
      // for debug, 后续请改回来
      this.showDetailPopover = false;
      this.popoverPosition.visibility = 'hidden';
      this.expandedIconGroupIndex = null;
    },

    highlightInteractiveLine() {
      if (!this.myChart) return;
      this.myChart.setOption({
        series: [
          {
            lineStyle: { opacity: 0.2 },
            itemStyle: { opacity: 0.2 }
          },
          {
            lineStyle: { opacity: 0.2 },
            itemStyle: { opacity: 0.2 }
          },
          {
            lineStyle: { width: 3, opacity: 1 }
          }
        ]
      });
    },

    restoreLineStyles() {
      if (!this.myChart) return;
      this.myChart.setOption({
        series: [
          {
            lineStyle: { opacity: 1 },
            itemStyle: { opacity: 1 }
          },
          {
            lineStyle: { opacity: 1 },
            itemStyle: { opacity: 1 }
          },
          {
            lineStyle: { width: 2, opacity: 1 }
          }
        ]
      });
    },

    setupResizeObserver() {
      this.resizeObserver = new ResizeObserver(() => {
        if (this.resizeTimeout) {
          clearTimeout(this.resizeTimeout);
        }
        this.resizeTimeout = setTimeout(() => {
          if (this.myChart) {
            this.myChart.resize();
            this.$nextTick(() => {
              this.updateIconPositions();
            });
          }
        }, 150);
      });
      if (this.$refs.radarGraph) {
        this.resizeObserver.observe(this.$refs.radarGraph);
      }
    },

    handleWindowClick(event) {
      const popoverEl = this.$refs.detailPopover?.$el;
      if (popoverEl && !popoverEl.contains(event.target)) {
          this.showDetailPopover = false;
          this.popoverPosition.visibility = 'hidden';
      }
      
      const iconGroups = this.$el.querySelectorAll('.icon-group-container');
      let isClickOutsideGroups = true;
      iconGroups.forEach(group => {
        if (group.contains(event.target)) {
          isClickOutsideGroups = false;
        }
      });
      if(isClickOutsideGroups) {
        this.expandedIconGroupIndex = null;
      }
    },
    
    _generateDateAxis() {
      const dates = [];
      for (let i = 41; i >= 0; i--) {
          const date = new Date();
          date.setDate(date.getDate() - i);
          dates.push(date);
      }
      return dates;
    },

    _showPopover(rect) {
      this.lastPopoverRect = rect;
      
      this.popoverPosition.visibility = 'hidden';
      this.showDetailPopover = true;
      
      this.$nextTick(() => {
        this.repositionPopover();
      });
    },
    
    repositionPopover() {
      if (!this.showDetailPopover || !this.lastPopoverRect) return;

      const rect = this.lastPopoverRect;
      const popoverWidth = 360; // 弹窗的固定宽度
      const popoverHeight = 460; // 弹窗的固定高度
      const popoverMargin = 8;
      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;

      // --- 水平位置计算 ---
      // 默认策略：将弹窗放在触发元素的右侧
      let left = rect.right + popoverMargin;
      
      // 边界检查（右侧）：如果弹窗右侧会超出屏幕，则切换策略
      if (left + popoverWidth > windowWidth - popoverMargin) {
        // 切换策略：将弹窗放在触发元素的左侧
        left = rect.left - popoverWidth - popoverMargin;
      }

      // 边界检查（左侧）：如果弹窗左侧仍然超出屏幕
      if (left < popoverMargin) {
        left = popoverMargin;
      }
      
      // --- 垂直位置计算 ---
      let top = rect.top + rect.height / 2 - popoverHeight / 2;

      // 检查下方空间是否足够
      if (top + popoverHeight > windowHeight - popoverMargin) {
        top = windowHeight - popoverHeight - popoverMargin;
      }

      // 检查上方空间是否足够
      if (top < popoverMargin) {
        top = popoverMargin;
      }
      
      this.popoverPosition.left = `${left}px`;
      this.popoverPosition.top = `${top}px`;
      this.popoverPosition.transform = 'translate(0, 0)';
      this.popoverPosition.visibility = 'visible';
    },

    _resetChartStyles() {
      if (!this.myChart) return;
      this.myChart.setOption({
        series: [
          { lineStyle: { opacity: 1, width: 2 }, itemStyle: { opacity: 1 } },
          { lineStyle: { opacity: 1, width: 2 }, itemStyle: { opacity: 1 } },
          { lineStyle: { opacity: 1, width: 2 } }
        ]
      });
      this.highlightedSeriesIndex = null;
    }
  },
};
</script>

<style lang="less" scoped>
.view-graph-container {
  position: relative;
  background: white;
  padding: 12px;
  border-radius: 8px;
  .view-graph-header{
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 4px;
    font-weight: 700;
    font-size: 13px;
    line-height: 18px;
    color: #181c25;
    color: var(--color-neutrals19);
    margin: 0;
    .view-graph-header-title-text{
      font-size: 13px;
      font-weight: 700;
      line-height: 18px;
      color: var(--color-neutrals19);
    }
  }
}

.view-graph {
  height: 250px;
  width: 100%;
}
.ai-summary-advice{
  display: flex;
  align-items: flex-start;
  gap: var(---12, 12px);
  .ai-contect{
    display: flex;
    padding: 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: var(---8, 8px);
    flex: 1 0 0;
    align-self: stretch;
    border-radius: var(--8, 8px);
    border: 2px solid var(--color-special01);
    background: var(--color-neutrals01);
    .header{
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
          .text{
            font-size: 13px;
            color: var(--color-neutrals19);
            font-style: normal;
            font-weight: 700;
            line-height: 18px; 
      }
    }
      .contect{
      min-height: 150px;
      font-size: 13px;
      font-weight: 400;
      line-height: 24px;
    }
     .no-data {
      width: 100%;
      border-radius: 8px;
      background: #fff;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 265px;
      text-align: center;
      font-family: Arial, sans-serif;
      .image-placeholder {
            width: 150px;
            height: 120px;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
            background: image-set(url("~@assets/images/nodata2.png") 1x,
                    url("~@assets/images/<EMAIL>") 2x) center center no-repeat;
        }
        .text {
            color: var(--Text-H2, #545861);
            text-align: center;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 142.857% */
        }
        .reload {
            color: var(--Text-Blue, #0C6CFF);
            text-align: center;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 142.857% */
        }
    }
  }
}
.icon-group-container {
  position: absolute;
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: rgba(240, 242, 245, 0.9);
  border-radius: 16px;
  padding: 4px;
  z-index: 10;
  border: 1px solid rgba(220, 222, 225, 0.9);
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);
  transform: translateZ(0);
  transition: width 0.3s ease, left 0.3s ease;
}

.interactive-icon {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  // border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0,0,0,0.3);
  font-size: 10px;
  font-weight: bold;
  color: white;
  cursor: pointer;
}

.view-graph-container > .interactive-icon {
  position: absolute;
}

.interactive-icon.fx-icon-tengxunhuiyi::before{
  color: white;
}
.interactive-icon.fx-icon-f-obj-app107::before{
  color: white;
}
.interactive-icon.fx-icon-f-email::before{
  color: white;
}
.interactive-icon.fx-icon-obj-app405::before{
  color: white;
}
.interactive-icon.fx-icon-obj-app367::before{
  color: white;
}
// /* 根据类型定义不同颜色 */
.interactive-icon.default {
  background-color: #36c2b6;
}
.interactive-icon.visit {
   background-color: #36c2b6;
}
.interactive-icon.meeting {
  background-color: #368DFF;
}
.interactive-icon.call {
  background-color: #FF7383;
}
.interactive-icon.email {
  background-color: #40b6ff;
}
.interactive-icon.icon-more {
  background-color: #FF9B29;
}

// .interactive-icon.dimmed, .icon-group-container.dimmed {
//   opacity: 0.2;
//   transition: opacity 0.3s ease;
// }
</style>
