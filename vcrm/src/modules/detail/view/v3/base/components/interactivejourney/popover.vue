<template>
  <fx-popover
    ref="detailPopover"
    :value="value"
    trigger="manual"
    :style="position"
    popper-class="detail-popover-interactive-journey"
    @input="$emit('input', $event)"
  >
    <div v-if="item" v-loading="popoverLoading" class="popover-content">
      <div class="header-popover-interactive-journey">
        <div class="header-popover-interactive-journey-title">
          <span class="interactive-icon" :class="[iconConfigClass[item.interactive_types || 'default'] , item.interactive_types || 'default' ]"></span>
          <span>{{popoverTitle}}</span>
        </div>
        <div v-if="item.interactive_scores" class="header-popover-interactive-journey-score">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
          >
            <path
              d="M8.35474 4.04881C5.09655 4.04881 2.44201 6.70335 2.44201 9.96153C2.44201 13.2197 5.09655 15.8743 8.35474 15.8743C11.6129 15.8743 14.2675 13.2197 14.2675 9.96153C14.2675 6.70335 11.6129 4.04881 8.35474 4.04881ZM10.0813 12.7274L8.29946 11.7615L6.48928 12.6808C6.48928 12.6808 6.44274 12.6903 6.37801 12.6532C6.31256 12.6161 6.33146 12.5514 6.33146 12.5514L6.70237 10.555L5.26455 9.10771C5.26455 9.10771 5.23619 9.06044 5.24565 8.99571C5.24803 8.97718 5.25537 8.95963 5.26689 8.94491C5.27841 8.93019 5.29368 8.91885 5.3111 8.91208L7.35256 8.64299L8.28055 6.83353C8.28055 6.83353 8.30892 6.80517 8.38237 6.80517C8.45728 6.80517 8.49437 6.83353 8.49437 6.83353L9.3671 8.6619L11.3809 8.98626C11.3809 8.98626 11.418 9.01462 11.4275 9.08808C11.4369 9.15353 11.418 9.20008 11.418 9.20008L9.95183 10.5921L10.2675 12.6161C10.2675 12.6161 10.2675 12.6619 10.1926 12.7084C10.1096 12.7361 10.0813 12.7274 10.0813 12.7274ZM5.9511 1.05026C5.9511 0.930261 5.98819 0.827716 6.06237 0.735352H2.69292C2.55787 0.736492 2.42867 0.790647 2.33317 0.886147C2.23767 0.981647 2.18351 1.11085 2.18237 1.2459V3.87208C2.18237 4.01172 2.24783 4.15062 2.34965 4.25317L3.09219 4.92081C3.15861 4.97871 3.23835 5.01925 3.32427 5.0388C3.41019 5.05834 3.49962 5.05628 3.58455 5.03281C4.2611 4.37574 5.06705 3.86675 5.9511 3.53826V1.05099V1.05026ZM9.70092 1.05026C9.70092 0.930261 9.73801 0.827716 9.81219 0.735352H6.86965C6.94305 0.823771 6.98273 0.935349 6.98165 1.05026V3.23208C7.43338 3.13882 7.89347 3.09204 8.35474 3.09244C8.81946 3.09244 9.27401 3.13899 9.70092 3.22262V1.05026ZM14.0173 0.735352H10.6195C10.6929 0.823771 10.7325 0.935349 10.7315 1.05026V3.51062C11.6677 3.86331 12.5187 4.41035 13.2282 5.11572C13.2905 5.11664 13.3523 5.1048 13.4098 5.0809C13.4674 5.05701 13.5194 5.02159 13.5627 4.97681L14.37 4.25317C14.4229 4.20481 14.4651 4.14593 14.4939 4.0803C14.5227 4.01468 14.5375 3.94375 14.5373 3.87208V1.25462C14.5373 1.18637 14.5238 1.11879 14.4977 1.05574C14.4715 0.992691 14.4332 0.935415 14.3849 0.887186C14.3366 0.838958 14.2793 0.800724 14.2162 0.77467C14.1531 0.748617 14.0855 0.735256 14.0173 0.735352Z"
              fill="#FF8000"
            />
          </svg>
          <span class="text"> {{ item.interactive_scores }}</span>
        </div>
      </div>
      <span class="time-now">{{ formatCreateTime(item.create_time) }}</span>
      <!-- 他方与我方联系人 -->
      <div class="users-list" v-if="popoverDetail && popoverDetail.activity_users && popoverDetail.activity_users.length > 0">
        <div class="our-users-list"  v-show="ourSideUsers.length > 0">
          <span class="user-group-title">{{$t('sfa.vcrm.interactivejourney.MySide')}}</span>
          <div class="our-users user-group">
            <div
              v-for="user in ourSideUsers"
              :key="user._id"
              class="user-avatar-container"
            >
              <sfaAiAvatar :data="{
              dataId: user.user_id,
              userName: user.user,
              useAvatarImg: !!(user.profile_image && user.profile_image.length),
              backgroundColor: user.avatar_bg_color,
              avatarImgSrc: user.profile_image && user.profile_image[0] ? user.profile_image[0].signedUrl : ''
            }" />
              <span class="is_key_person" v-show="user.primary_contact === '2'" style="margin-left: 2px;"> kp </span>
            </div>
          </div>
        </div>
        <div class="their-users-list" v-show="theirSideUsers.length > 0">
          <span class="user-group-title">{{$t('sfa.vcrm.interactivejourney.TheirSide')}}</span>
          <div class="their-users user-group">
            <div
              v-for="user in theirSideUsers"
              :key="user._id"
              class="user-avatar-container"
            >
               <sfaAiAvatar :data="{
              dataId: user.user_id,
              userName: user.user,
              useAvatarImg: !!(user.profile_image && user.profile_image.length),
              backgroundColor: user.avatar_bg_color,
              avatarImgSrc: user.profile_image && user.profile_image[0] ? user.profile_image[0].signedUrl : ''
            }" />
              <span class="is_key_person" v-show="user.primary_contact === '2'" style="margin-left: 2px;"> kp </span>
            </div>
          </div>
        </div>
      </div>
      <!-- 富文本内容 -->
      <div class="popover-interactive-journey-content">
        <div
          class="editor-doc"
          v-if="documentContent && documentContent.content && documentContent.content.length > 0"
        >
          <XXVUIEditorRenderDoc
            :node="documentContent"
            ref="documentViewer"
          />
        </div>
        <div v-else>
          <div class="no-data">
            <div class="image-placeholder"></div>
            <div class="text">{{$t('crm.sfa.list.nodata.tip')}}</div>
          </div>
        </div>
        <span class="see-more" @click="toVcrmDetail(item._id)"> {{$t('查看更多')}}
          <span class="fx-icon-arrow-right2"></span>
           </span>
      </div>
    </div>
  </fx-popover>
</template>

<script>
import sfaAiAvatar from '../sfaAiAvatar/sfaAiAvatar.vue'
export default {
  name: "DetailPopover",
  components: {
    sfaAiAvatar,
  },
  props: {
    value: {
      // for v-model
      type: Boolean,
      default: false,
    },
    position: {
      type: Object,
      required: true,
    },
    item: {
      type: Object,
      default: null,
    },
  },
  computed: {
    ourSideUsers() {
      if (!this.popoverDetail || !this.popoverDetail.activity_users) return [];
      return this.popoverDetail.activity_users.filter(
        (user) => user.participant_types === "our_side"
      );
    },
    theirSideUsers() {
      if (!this.popoverDetail || !this.popoverDetail.activity_users) return [];
      return this.popoverDetail.activity_users.filter(
        (user) => user.participant_types === "their_side"
      );
    },
    popoverTitle(){
      let title = this.popoverDetail?.interactive_scenario__r || this.popoverDetail?.interactive_types__r;
      if(!title && this.popoverDetail?.interaction_records) {
        return this.$t('sfa.vcrm.interactivejourney.Recording')
      }
      if(!title && !this.popoverDetail?.interaction_records){
        return this.popoverDetail?.object_display_name || ''
      }
      return  title
     },
         documentContent() {
      if (this.popoverDetail && this.popoverDetail.active_record_content__e) {
        return this.parseDocumentData(this.popoverDetail.active_record_content__e);
      }
      return {};
    }
  },
  data() {
    return {
      popoverLoading: true,
      popoverDetail: null,
      iconConfigClass:{
         meeting: 'fx-icon-tengxunhuiyi',
         manually_create:'fx-icon-f-obj-app107',
         email:'fx-icon-f-email',
         call:"fx-icon-obj-app405",
         visit:"fx-icon-obj-app367",
         default:"fx-icon-f-obj-app107",
         recording:"fx-icon-yuyinshibie",
      }
    };
  },
  watch: {
    item: {
      handler(newItem, oldItem) {
        if (
          newItem &&
          newItem._id &&
          (!oldItem || newItem._id !== oldItem._id)
        ) {
          this.fetchPopoverDetail(newItem);
        }
      },
      immediate: true,
    },
  },
  methods: {
    formatCreateTime(timestamp) {
      if (!timestamp) return "";
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      const hours = date.getHours().toString().padStart(2, "0");
      const minutes = date.getMinutes().toString().padStart(2, "0");
      const seconds = date.getSeconds().toString().padStart(2, "0");
      return `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}`;
    },
    parseDocumentData(data) {
      const jsonData = JSON.parse(data)?.__xt?.__json;
      return jsonData || {};
    },
    async fetchPopoverDetail(item) {
      this.popoverLoading = true;
      this.popoverDetail = null;
      try {
        const data = await this.getDetailPopoverData(item._id);
        // data.activity_users = [
        //   {
        //     user_name: "张三",
        //     participant_types: "their_side",
        //     answer_score: null,
        //     avatar_bg_color: "#55D48C",
        //     total_num: 1,
        //     user_id: "1",
        //     name: "user_1",
        //     _id: "683eeb1ae3ce1500010384e8",
        //     primary_contact: "2", //2 代表kp
        //   },
        //   {
        //     user_name: "王五",
        //     participant_types: "their_side",
        //     answer_score: null,
        //     avatar_bg_color: "#FFA142",
        //     total_num: 1,
        //     user_id: "1",
        //     name: "user_1",
        //     _id: "683eeb1ae3ce1500010384e9",
        //     primary_contact: "2",
        //   },
        //   {
        //     user_name: "李四",
        //     participant_types: "their_side",
        //     answer_score: null,
        //     avatar_bg_color: "#FFA142",
        //     total_num: 1,
        //     user_id: "1",
        //     name: "user_1",
        //     _id: "683eeb1ae3ce1500010384e7",
        //     primary_contact: "",
        //   },
        //   {
        //     user_name: "李四",
        //     participant_types: "their_side",
        //     answer_score: null,
        //     avatar_bg_color: "#FFA142",
        //     total_num: 1,
        //     user_id: "1",
        //     name: "user_1",
        //     _id: "683eeb1ae3ce1500010384e6",
        //     primary_contact: "",
        //   },
        //   {
        //     user_name: "李四",
        //     participant_types: "their_side",
        //     answer_score: null,
        //     avatar_bg_color: "#FFA142",
        //     total_num: 1,
        //     user_id: "1",
        //     name: "user_1",
        //     _id: "683eeb1ae3ce1500010384e5",
        //     primary_contact: "",
        //   },
        //   {
        //     user_name: "张三",
        //     participant_types: "our_side",
        //     answer_score: null,
        //     avatar_bg_color: "#55D48C",
        //     total_num: 1,
        //     user_id: "1",
        //     name: "user_1",
        //     _id: "683eeb1ae3ce1500010384f4",
        //     primary_contact: "2",
        //   },
        //   {
        //     user_name: "王五",
        //     participant_types: "our_side",
        //     answer_score: null,
        //     avatar_bg_color: "#FFA142",
        //     total_num: 1,
        //     user_id: "1",
        //     name: "user_1",
        //     _id: "683eeb1ae3ce1500010384f3",
        //     primary_contact: "2",
        //   },
        // ];
          console.log(data);
        if (this.item && this.item._id === item._id) {
          this.popoverDetail = data;
          this.$nextTick(() => {
            this.$emit("content-ready");
          });
        }
      } catch (error) {
        console.error("Failed to fetch popover details:", error);
      } finally {
        if (this.item && this.item._id === item._id) {
          this.popoverLoading = false;
        }
      }
    },
    toVcrmDetail(id) {
      console.log(id);
       CRM.api.show_crm_detail({
           type: 'ActiveRecordObj',
           data: {
               crmId: id,
           }
        });
    },
    getDetailPopoverData(id) {
      return new Promise((resolve, reject) => {
        CRM.util.FHHApi({
          url: "/EM1HNCRM/API/v1/object/interactive_journey/service/query_interactive_score_detail",
          data: {
            active_record_id: id,
          },
          success: (res) => {
            resolve(res.Value.interactive_score_detail);
          },
          error: (err) => {
            // FHHApi uses 'error' not 'fail'
            reject(err);
          },
        });
      });
    },
  },
};
</script>

<style lang="less">
/* Using non-scoped styles for popper, as it's often attached to the body */
.detail-popover-interactive-journey {
  width: 327px;
  padding: 12px;
  border-radius: 8px !important;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
  .header-popover-interactive-journey{
    display: flex;
    align-items: center;
    justify-content: space-between;
    align-self: stretch;
    .header-popover-interactive-journey-title{
      display: flex;
      align-items: center;
      color: var(--color-neutrals19);
      font-size: 15px;
      font-style: normal;
      font-weight: 700;
      line-height: 24px; /* 160% */
      margin-right: 2px;
    }
    .header-popover-interactive-journey-score{
      display: flex;
      padding: 6px;
      justify-content: center;
      align-items: center;
      gap: 2px;
      border-radius: 4px;
      background:var(--color-primary01);
      .text{
        font-size: 14px;
        font-weight: 700;
        line-height: 20px;
        color: var(--color-neutrals19);
        margin-left: 2px;
      }
    }
  }
  .popover-content {
    .time-now {
        font-size: 12px;
        color: #888;
        margin: 4px 0 8px 0;
     }
    .interactive-icon{
        display: flex;
  justify-content: center;
  align-items: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  // border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0,0,0,0.3);
  font-size: 10px;
  font-weight: bold;
  color: white;
  cursor: pointer;
      margin-right:3px;
    }
  .interactive-icon.fx-icon-tengxunhuiyi::before{
  color: white;
}
.interactive-icon.fx-icon-f-obj-app107::before{
  color: white;
}
.interactive-icon.fx-icon-f-email::before{
  color: white;
}
.interactive-icon.fx-icon-obj-app405::before{
  color: white;
}
.interactive-icon.fx-icon-obj-app367::before{
  color: white;
}
.interactive-icon.fx-icon-yuyinshibie::before{
  color: white;
}
// /* 根据类型定义不同颜色 */
.interactive-icon.default {
  background-color: #36c2b6;
}
.interactive-icon.visit {
   background-color: #36c2b6;
}
.interactive-icon.meeting {
  background-color: #368DFF;
}
.interactive-icon.call {
  background-color: #FF7383;
}
.interactive-icon.email {
  background-color: #40b6ff;
}
.interactive-icon.icon-more {
  background-color: #FF9B29;
}
.interactive-icon.recording {
  background-color: #A370E5;
}

    .users-list {
         margin: 12px 0;
         display: flex;
         flex-direction: column;
         justify-content: center;
         align-items: flex-start;
         gap: 6px;
         align-self: stretch;
        .our-users-list{
          display: flex;
          align-items: center;
          gap: 8px;
          .user-group-title{
            font-size: 12px;
            margin-right: 8px;
            width: 36px;
          }
        }
        .their-users-list{
          display: flex;
          align-items: center;
          gap: 8px;
          .user-group-title{
            font-size: 12px;
            margin-right: 8px;
            width: 36px;
          }
        }
        .user-group {
          display: flex;
          flex-wrap: wrap;
          gap: 4px;
          width: 275px;
        }
        .user-avatar-container{
          display: flex;
          align-items: center;
          margin-right:8px;
          .is_key_person{
            display: flex;
            width: 20px;
            height: 16px;
            padding: 2px;
            justify-content: center;
            align-items: center;
            gap: 2px;
            border-radius: 2px;
            border: 1px solid var(--color-special01);
            background: #FFF;
            color:var(--color-success06);
          }
        }
    }
  }
  .popover-content p {
    margin: 4px 0;
    font-size: 14px;
    color: #333;
  }
  .popover-content p strong {
    color: #666;
  }
  .popover-loading,
  .popover-details {
    margin-top: 8px;
  }
}

.popover-interactive-journey-content {
  .editor-doc {
    max-height: 264px;
    overflow-y: auto;
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari and Opera */
    }
    
  }
  .no-data {
      width: 100%;
      border-radius: 8px;
      background: #fff;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 265px;
      text-align: center;
      font-family: Arial, sans-serif;
      .image-placeholder {
            width: 150px;
            height: 120px;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
            background: image-set(url("~@assets/images/nodata2.png") 1x,
                    url("~@assets/images/<EMAIL>") 2x) center center no-repeat;
        }
        .text {
            color: var(--Text-H2, #545861);
            text-align: center;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 142.857% */
        }
        .reload {
            color: var(--Text-Blue, #0C6CFF);
            text-align: center;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 142.857% */
        }
    }
  .see-more{
    font-size: 13px;
    line-height: 22px;
    color: var(--color-info06);
  }
}
</style>
