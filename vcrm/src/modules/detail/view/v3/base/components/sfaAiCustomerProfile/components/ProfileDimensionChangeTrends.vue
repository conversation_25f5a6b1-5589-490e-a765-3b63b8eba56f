<template>
    <common-card
        :title="$t('sfa.aiCustomerProfile.cardTitle.dimensionChangeTrends')"
        icon="fx-icon-f-obj-app107"
        class="profile-dimension-change-trends-card"
    >
        <common-data-fetcher-status
            :loading="loading"
            :error="error"
            :data="dimensionHistorySource"
        >
            <trend-chart
                :chart-data="chartData"
                :profile-context="profileContext"
                :data-context="dataContext"
                :min-height="200"
            />
            <common-text-list-display class="trend-summary" :list="trendSummary" />
        </common-data-fetcher-status>
    </common-card>
</template>

<script>
import {CommonCard, CommonDataFetcherStatus, CommonTextListDisplay} from './index'
import * as profileService from '../services/profileService'
import TrendChart from './BaseDimensionTrendChart.vue'
import { DIMENSION_SOURCE_TREND_COLORS } from '../utils'

export default {
    name: 'ProfileDimensionChangeTrends',
    props: {
        profileContext: {
            type: Object,
            default: () => ({
                currentDimension: '',
                currentProfileId: '',
                currentMethodology: '',
                currentMethodologyType: '',
            })
        },
        dataContext: {
            type: Object,
            default: () => ({
                apiName: '',
                dataId: '',
            })
        },
        dimensionsData: {
            type: Array,
            default: () => []
        },
    },
    computed: {
        currentDimension() {
            return this.profileContext.currentDimension
        },
        dimensionsDataWithColor() {
            return this.dimensionsData.map((item, index) => ({
                ...item,
                color: DIMENSION_SOURCE_TREND_COLORS[index]
            }))
        },
        displayDimensions() {
            if (!this.currentDimension) {
                return this.dimensionsDataWithColor
            }
            return this.dimensionsDataWithColor.filter(item => item.id === this.currentDimension)
        },
        chartData() {
            return {
                dimensions: this.displayDimensions,
                dimensionHistorySource: this.dimensionHistorySource
            }
        },
        trendSummary() {
            return this.dimensionHistorySource
                .filter(item => this.currentDimension ? item.id === this.currentDimension : true)
                .slice(-1)[0]?.trend_summary;
        }
    },
    watch: {
        'profileContext.currentProfileId': {
            handler() {
                this.fetchDimensionHistory()
            },
            immediate: true
        },
        currentDimension: {
            handler() {
                this.fetchDimensionHistory()
            },
            immediate: true
        }
    },
    components: {
        CommonCard,
        CommonDataFetcherStatus,
        CommonTextListDisplay,
        TrendChart
    },
    data() {
        return {
            dimensionHistorySource: [],
            loading: false,
            error: null,
        }
    },
    methods: {
        async fetchDimensionHistory() {
            if (this.loading || !this.profileContext.currentMethodologyType) return
            this.loading = true
            this.error = null
            try {
                const res = await profileService.getCustomerDimensionHistory({
                    objectApiName: this.dataContext.apiName,
                    objectId: this.dataContext.dataId,
                    methodologyId: this.profileContext.currentMethodology,
                    methodologyType: this.profileContext.currentMethodologyType,
                    featureDimensionId: this.profileContext.currentDimension,
                })
                this.dimensionHistorySource = res
                this.loading = false
            } catch (error) {
                this.error = error
                this.loading = false
            }
        }
    }
}
</script>

<style lang="less" scoped>
.trend-summary {
    margin-top: 16px;
}
</style>

