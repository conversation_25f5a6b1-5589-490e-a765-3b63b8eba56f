<template>
    <div class="switch-tab">
        <div
            v-for="(item, index) in dimensions"
            :key="index"
            class="tab-item"
            :class="{ active: currentDimension === item.id}"
            @click="handleClick(item.id)"
        >
            <div class="tab-content">
                <div class="tab-left">
                    <i v-if="item.icon" class="tab-icon" :class="item.icon"></i>
                    <span class="tab-text">{{ item.label }}</span>
                </div>
                <div v-if="item.__isTask" class="tag-group">
                    <fx-tag v-if="item.completedTasksCount" type="success" size="small" effect="light-noborder">{{ item.completedText }}</fx-tag>
                    <fx-tag v-if="item.incompleteTasksCount" type="danger" size="small" effect="light-noborder">{{ item.incompleteText }}</fx-tag>
                </div>
                <span v-else-if="item.score" class="tab-score" :style="{ color: item.color }">{{ item.score }}</span>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'ProfileDimensionFeatures',
    props: {
        dimensions: {
            type: Array,
            default: () => []
        },
        currentDimension: {
            type: String,
            default: ''
        }
    },
    methods: {
        handleClick(id) {
            this.$emit('update:currentDimension', id)
        },
    }
};
</script>

<style lang="less" scoped>
.switch-tab {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;

    .tab-item {
        min-width: 100px;
        padding: 8px 12px;
        background-color: var(--color-neutrals01);
        border-radius: 8px;
        cursor: pointer;
        opacity: 0.9;
        transition: all 0.3s;
        border: 1px solid transparent;

        // &:hover {
        //     opacity: 1;
        //     box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        // }

        &:hover, &.active {
            opacity: 1;
            border: 1px solid var(--color-primary06);
        }

        .tab-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 8px;
        }

        .tab-left {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .tab-icon {
            font-size: 16px;
            color: var(--color-primary06);
        }

        .tab-text {
            font-weight: 700;
            font-size: 13px;
            line-height: 18px;
            color: var(--color-neutrals19);
        }

        .tab-score {
            font-weight: 700;
            font-size: 13px;
            line-height: 18px;
        }

        .tag-group {
            display: flex;
            gap: 2px;
        }
    }
}
</style>
