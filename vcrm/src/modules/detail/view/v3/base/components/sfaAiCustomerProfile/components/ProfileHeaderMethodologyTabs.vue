<template>
    <div class="methodlogy-tabs">
        <div class="methodlogy-tabs-item" :class="{ 'active': currentMethodology === methodology.id }" v-for="methodology in methodologies" :key="methodology.id" @click="handleMethodologyChange(methodology)">
            {{ methodology.name }}
        </div>
    </div>
</template>

<script>
export default {
    name: 'ProfileHeaderMethodologyTabs',
    props: {
        methodologies: {
            type: Array,
            default: () => []
        },
        currentMethodology: {
            type: String,
            default: ''
        },
    },
    methods: {
        handleMethodologyChange(methodology) {
            this.$emit('update:currentMethodology', methodology.id);
        }
    }
};
</script>

<style lang="less" scoped>
.methodlogy-tabs {
    display: flex;
    gap: 8px;

    .methodlogy-tabs-item {
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 13px;
        line-height: 18px;
        color: var(--color-neutrals19);
        cursor: pointer;
        background: var(--color-special01);

        &:hover {
            color: var(--color-primary06);
        }

        &.active {
            color: var(--color-primary06);
            background: var(--color-primary01);
            font-weight: 700;
        }
    }
}
</style>
