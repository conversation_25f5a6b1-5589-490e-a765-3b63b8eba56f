<template>
    <div class="operate-actions">
        <span v-if="!firstInit" class="tip">{{ $t('sfa.aiCustomerProfile.operateActions.aiGenerated') }}</span>
        <fx-button size="micro" type="primary2" v-for="operation in operations" :key="operation.value" :icon="operation.icon" @click="handleOperation(operation.value)" :loading="loading[operation.value]">
            {{ operation.label }}
        </fx-button>
        <ProfileHistoryDialog
            v-if="visible"
            :visible.sync="visible"
            :profileContext="profileContext"
            :dataContext="dataContext"
        />
    </div>
</template>

<script>
import ProfileHistoryDialog from './ProfileHistoryDialog.vue';
import profileService from '../services/profileService';
export default {
    name: 'ProfileHeaderOperateActions',
    components: {
        ProfileHistoryDialog
    },
    props: {
        profileContext: {
            type: Object,
            default: () => ({
                currentDimension: '',
                currentProfileId: null,
                currentMethodology: null,
                currentMethodologyInstanceId: null,
                currentMethodologyType: null,
                currentFlowProfileId: null,
                dimensionsData: [],
                switchDimensionsData: [],
                profileData: {},
            })
        },
        dataContext: {
            type: Object,
            default: () => ({
                objectApiName: '',
                objectId: '',
            })
        },
        firstInit: {
            type: Boolean,
            default: false
        },
    },
    computed: {
        operations() {
            const {last_modified_time = ''} = this.profileContext.profileData || {};
            return [
                {
                    label: $t('sfa.aiCustomerProfile.operateActions.viewHistory'),
                    icon: 'fx-icon-clock2',
                    value: 'viewHistory',
                    isShow: !this.firstInit
                },
                {
                    // 画像未刷新显示“刷新画像”，刷新后显示“更新时间”
                    label: !last_modified_time ? $t('sfa.aiCustomerProfile.operateActions.refresh') : ($t('sfa.aiCustomerProfile.operateActions.updateTime') + ' ' + last_modified_time),
                    value: 'updateTime',
                    icon: 'fx-icon-refresh',
                    isShow: !!this.profileContext.currentMethodology
                }
            ].filter((item) => item.isShow)
        }
    },
    data() {
        return {
            visible: false,
            loading: {
                updateTime: false,
            }
        }
    },
    methods: {
        handleOperation(value) {
            console.log(value)
            this.$emit('action', value)
            if (value === 'viewHistory') {
                this.visible = true
            } else if (value === 'updateTime') {
                this.$set(this.loading, 'updateTime', true)
                profileService.refreshProfile({
                    objectApiName: this.dataContext.objectApiName,
                    objectId: this.dataContext.objectId,
                    methodologyType: this.profileContext.currentMethodologyType,
                    methodologyId: this.profileContext.currentMethodology,
                }).then(res => {
                    this.$alert(this.$t('sfa.aiCustomerProfile.operateActions.refreshSuccess'), {
                        confirmButtonText: $t('我知道了'),
                        type: 'success',
                    });
                }).catch(err => {
                    this.$alert(err, {
                        confirmButtonText: $t('我知道了'),
                        type: 'error',
                    });
                }).finally(() => {
                    this.$set(this.loading, 'updateTime', false)
                })
            }
        }
    }
}
</script>

<style lang="less" scoped>
.operate-actions {
    display: flex;
    gap: 8px;
    align-items: center;
    .tip {
        font-size: 13px;
        background: linear-gradient(90deg, #4F68FF -0.11%, #8048FF 100.09%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    /deep/ .el-button {
        margin-left: 0;
    }
}
</style>
