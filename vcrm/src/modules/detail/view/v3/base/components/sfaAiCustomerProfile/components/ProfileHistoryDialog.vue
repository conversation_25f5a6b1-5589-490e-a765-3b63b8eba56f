<template>
    <fx-dialog
        :visible.sync="cVisible"
        :title="$t('sfa.aiCustomerProfile.historyDialog.title')"
        custom-class="profile-history-dialog"
        fullscreen
    >
        <div class="profile-history-dialog-content">
            <div class="dimension-tabs">
                <div
                    v-for="(item, index) in profileContext.switchDimensionsData"
                    :key="index"
                    class="tab-item"
                    :class="{ active: activeDimension === item.id }"
                    @click="activeDimension = item.id"
                >
                    {{ item.label }}
                </div>
            </div>
            <div class="dimension-content">
                <fx-table
                    ref="table"
                    :data="historyList"
                    height="100%"
                    v-loading="loading"
                >
                    <fx-table-column
                        prop="time"
                        :label="$t('sfa.aiCustomerProfile.historyDialog.time')"
                        width="120"
                    >
                        <template slot-scope="scope">
                            <div class="time-text">{{ scope.row.time }}</div>
                        </template>
                    </fx-table-column>
                    <fx-table-column
                        prop="score"
                        :label="$t('sfa.aiCustomerProfile.historyDialog.score')"
                        width="100"
                    >
                        <template slot-scope="scope">
                            <div class="score-box" :style="{ color: scope.row.scoreColor }">{{ scope.row.score }}</div>
                        </template>
                    </fx-table-column>
                    <fx-table-column class-name="summary-column text-column">
                        <template slot="header">
                            <div class="header-with-icon">
                                <img class="cell-icon ai" src="../assets/ai_generate.svg">
                                {{ $t('sfa.aiCustomerProfile.cardTitle.dimensionSummary') }}
                            </div>
                        </template>
                        <template slot-scope="scope">
                            <text-list-display :list="scope.row.summary" :maxLines="6" />
                        </template>
                    </fx-table-column>
                    <fx-table-column class-name="pros-column text-column">
                        <template slot="header">
                            <div class="header-with-icon">
                                <span class="cell-icon fx-icon-f-obj-app185"></span>
                                {{ $t('sfa.aiCustomerProfile.cardTitle.dimensionAdvantage') }}
                            </div>
                        </template>
                        <template slot-scope="scope">
                            <text-list-display
                                :list="scope.row.pros"
                                :showIndex="!!activeDimension"
                                :maxLines="6"
                            />
                        </template>
                    </fx-table-column>
                    <fx-table-column class-name="cons-column text-column">
                        <template slot="header">
                            <div class="header-with-icon">
                                <span class="cell-icon fx-icon-f-obj-app323"></span>
                                {{ $t('sfa.aiCustomerProfile.cardTitle.dimensionDisadvantage') }}
                            </div>
                        </template>
                        <template slot-scope="scope">
                            <text-list-display
                                :list="scope.row.cons"
                                :showIndex="!!activeDimension"
                                :maxLines="6"
                            />
                        </template>
                    </fx-table-column>
                    <fx-table-column :label="getSuggestionTitle()" min-width="200" class-name="advice-column text-column">
                        <template slot="header" slot-scope="scope">
                            <div class="header-with-icon">
                                <span class="cell-icon fx-icon-f-product_hot"></span>
                                {{ scope.column.label }}
                            </div>
                        </template>
                        <template slot-scope="scope">
                            <text-list-display class="suggestion-list" :list="scope.row.advice" :displayCount="2" listItemGap="8px">
                                <template slot="content-item" slot-scope="{ item, index }">
                                    <span class="suggestion-item-index" style="white-space: nowrap;word-break: normal;">{{ index + 1 }}.</span>
                                    <base-dimension-suggestion-item :item="item" :show-actions="false" />
                                </template>
                            </text-list-display>
                        </template>
                    </fx-table-column>
                </fx-table>
            </div>
        </div>
    </fx-dialog>
</template>

<script>
import { getCustomerProfileHistory } from '../services/profileService';
import TextListDisplay from './CommonTextListDisplay.vue';
import BaseDimensionSuggestionItem from './BaseDimensionSuggestionItem.vue';

export default {
    name: 'ProfileHistoryDialog',
    components: {
        TextListDisplay,
        BaseDimensionSuggestionItem
    },
    props: {
        profileContext: {
            type: Object,
            default: () => ({
                currentDimension: '',
                currentProfileId: null,
                currentMethodology: null,
                currentMethodologyInstanceId: null,
                currentMethodologyType: null,
                currentFlowProfileId: null,
                dimensionsData: [],
                switchDimensionsData: [],
                profileData: {},
            })
        },
        dataContext: {
            type: Object,
            default: () => ({
                objectApiName: '',
                objectId: '',
            })
        },
        visible: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        cVisible: {
            get() {
                return this.visible
            },
            set(value) {
                this.$emit('update:visible', value)
            }
        }
    },
    watch: {
        activeDimension: {
            handler() {
                this.fetchHistoryList();
            },
            immediate: true
        }
    },
    data() {
        return {
            activeDimension: this.profileContext.switchDimensionsData[0].id,
            loading: false,
            historyList: [],
            allHistoryList: {},
        }
    },
    methods: {
        getSuggestionTitle() {
            return !this.activeDimension ?
                this.$t('sfa.aiCustomerProfile.cardTitle.dimensionSuggestion.nextAction') :
                this.$t('sfa.aiCustomerProfile.cardTitle.dimensionSuggestion.flow');
        },
        fetchHistoryList() {
            const cache = this.allHistoryList[this.activeDimension || 'all'];
            if (cache) {
                this.historyList = cache;
                return;
            }
            this.loading = true;
            this.historyList = [];
            getCustomerProfileHistory({
                methodologyInstanceId: this.profileContext.currentMethodologyInstanceId,
                methodologyType: this.profileContext.currentMethodologyType,
                featureDimensionId: this.activeDimension,
                objectApiName: this.dataContext.objectApiName,
                objectId: this.dataContext.objectId
            }).then((res) => {
                this.historyList = res;
                this.allHistoryList[this.activeDimension || 'all'] = res;
            }).finally(() => {
                this.loading = false;
            })
        },
    }
}
</script>

<style lang="less">
.profile-history-dialog {
    .el-dialog__body {
        background: #F1F3F8;
        height: calc(100% - 74px);
        padding: 12px;
    }
    .profile-history-dialog-content {
        display: flex;
        gap: 12px;
        height: 100%;
    }

    .dimension-tabs {
        background: #fff;
        padding: 12px;
        border-radius: 8px;
        min-width: 188px;
        display: flex;
        flex-direction: column;
        gap: 10px;

        .tab-item {
            padding: 12px 8px;
            border-radius: 8px;
            font-size: 13px;
            font-weight: 700;
            line-height: 18px;
            color: var(--color-neutrals19);
            background: var(--color-neutrals03);
            cursor: pointer;
            user-select: none;
            transition: all 0.3s;

            &:hover {
                opacity: 0.8;
            }

            &.active {
                background: var(--color-primary01);
                color: var(--color-primary06);
            }
        }
    }

    .dimension-content {
        flex: 1;
        background: #fff;
        border-radius: 8px;
        padding: 12px;
        display: flex;
        flex-direction: column;

        .el-table.fx-table {
            flex: 1;
            border: 2px solid #F2F4FB;
            border-radius: 8px;
            color: var(--color-neutrals19);

            &::before {
                height: 0;
            }

            td {
                padding: 8px;
            }
            th {
                padding: 4px;
            }
            .cell {
                padding: 0;
                white-space: normal;
            }

            .el-table__header {
                .cell {
                    font-size: 13px;
                    line-height: 18px;
                    font-weight: 700;
                    color: var(--color-neutrals19);
                }
            }

            .header-with-icon {
                display: flex;
                align-items: center;
                font-size: 13px;
                font-weight: 700;
                padding: 0;

                .cell-icon {
                    margin-right: 4px;
                    font-size: 14px;
                    display: flex;
                    align-items: center;

                    &.ai {
                        width: 14px;
                        height: 14px;
                    }

                    &.fx-icon-f-obj-app185:before {
                        color: #55D48C;
                    }
                    &.fx-icon-f-obj-app323:before {
                        color: #FFCA2B;
                    }
                }
            }

            .time-text {
                font-size: 12px;
                line-height: 18px;
                white-space: pre-line;
                color: var(--color-neutrals19);
            }

            .score-box {
                font-size: 24px;
                font-weight: 700;
                line-height: 28px;
                padding: 4px;
                border-radius: 8px;
                width: 100%;
                text-align: left;
            }

            .el-table__cell {
                font-size: 13px;
                line-height: 22px;
                color: var(--color-neutrals19);

                &.text-column {
                    vertical-align: top;
                }
            }
        }
    }
}
</style>
