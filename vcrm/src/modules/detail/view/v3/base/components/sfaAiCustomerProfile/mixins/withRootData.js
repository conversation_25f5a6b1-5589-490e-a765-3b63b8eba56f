import utils from '../utils'
import * as profileService from '../services/profileService'
export default {
    data() {
        return {
            objectApiName: null,
            objectId: null,
            objectData: null,
            objectDescribe: null,
            flowMethodology: null,
            currentMethodology: null,
            currentMethodologyIsRelatedFlow: false,
            currentMethodologyType: null,
            currentMethodologyInstanceId: null,
            currentDimension: '',
            currentProfileId: null,
            currentFlowProfileId: null,

            methodologiesData: [],
            dimensionsData: [],
            profileData: {},
            flowProfileData: {},
            flowProfileAdviceSummary: '',
            stageScoreData: {
                stageName: '',
                stageScore: 0,
                stages: [],
            },

            loading: {
                methodologiesData: false,
                dimensionsData: false,
                stageScoreData: false,
                profileData: false,
            }
        }
    },
    computed: {
        profileContext() {
            return {
                currentDimension: this.currentDimension,
                currentProfileId: this.currentProfileId,
                currentMethodology: this.currentMethodology,
                currentMethodologyIsRelatedFlow: this.currentMethodologyIsRelatedFlow,
                currentMethodologyInstanceId: this.currentMethodologyInstanceId,
                currentMethodologyType: this.currentMethodologyType,
                currentFlowProfileId: this.currentFlowProfileId,
                dimensionsData: this.dimensionsData,
                switchDimensionsData: this.switchDimensionsData,
                profileData: this.profileData,
                flowProfileAdviceSummary: this.flowProfileAdviceSummary,
            }
        },
        dataContext() {
            return {
                apiName: this.objectApiName,
                dataId: this.objectId,
                objectId: this.objectId,
                objectApiName: this.objectApiName,
                objectData: this.objectData,
                objectDescribe: this.objectDescribe,
            }
        },
        isC139Methodology() {
            return this.currentMethodology === '683063399c6dd800060bec1c';
        },
        currentSummary() {
            return !this.currentDimension ?
                this.profileData?.summary :
                this.dimensionsData.find(item => item.id === this.currentDimension)?.summary
        },
        isCommonMethodology() {
            return this.currentMethodologyType === 'flow';
        },
        rootDataLoading() {
            return this.loading.profileData || this.loading.methodologiesData;
        },
        winRateScoreData() {
            return {
                winRate: this.profileData.integrated_score,
                winRateTrend: this.profileData.integrated_score_change,
            }
        },

        c139RadarData() {
            return this.dimensionsData.reduce((acc, item) => {
                acc[item.shortName] = item.completedTasksCount;
                return acc
            }, {})
        },


        switchDimensionsData() {
            return [
                {label: $t('sfa.aiCustomerProfile.dimensionSummary'), id: '', icon: 'fx-icon-f-obj-app283', iconColor: '#FFCA2B'},
                ...this.dimensionsData
            ]
        },
    },
    watch: {
        currentMethodology: {
            handler(newVal) {
                if (newVal === null) {
                    return;
                }
                this.currentDimension = '';
                const currentMethodologyData = this.methodologiesData.find(item => item.id === newVal);
                this.currentMethodologyType = currentMethodologyData?.type;
                this.currentMethodologyIsRelatedFlow = currentMethodologyData?.isRelatedFlow;
                this.fetchDimensions();
                this.fetchProfile();
                this.fetchStageScoreData();
            },
            immediate: true,
        },
    },
    created() {
        this.objectApiName = this.apiName;
        this.objectId = this.dataId;
        this.objectData = this.$context.getData();
        this.objectDescribe = this.$context.getDescribe();
        this.fetchMethodologyInstance();
    },
    methods: {
        setLoading(type, value) {
            this.$set(this.loading, type, value);
        },

        async fetchMethodologyInstance() {
            this.setLoading('methodologiesData', true)
            try {
                const result = await profileService.getMethodologyInstance({
                    objectApiName: this.objectApiName,
                    objectId: this.objectId,
                })
                this.methodologiesData = result;
                if (this.methodologiesData.length) {
                    this.flowMethodology = this.methodologiesData[0].id;
                    this.currentMethodology = this.methodologiesData[0].id;
                }
            } catch (error) {
                this.$alert(error)
            } finally {
                this.setLoading('methodologiesData', false)
            }

        },

        async fetchProfile() {
            this.setLoading('profileData', true)
            this.currentProfileId = null;
            this.currentMethodologyInstanceId = null;
            try {
                const profileData = await profileService.getCustomerProfile({
                    objectApiName: this.objectApiName,
                    objectId: this.objectId,
                    methodologyId: this.currentMethodology,
                    methodologyType: this.currentMethodologyType,
                })
                this.profileData = {
                    ...profileData,
                    last_modified_time: profileData.last_modified_time && CRM.util.formatTime(profileData.last_modified_time, 'time'),
                }
                this.currentProfileId = profileData?._id
                this.currentMethodologyInstanceId = profileData?.methodology_instance_id
            } catch (error) {
                console.error(error)
                this.$alert(error)
            } finally {
                this.setLoading('profileData', false)
            }
        },

        async fetchDimensions() {
            this.dimensionsData = []
            this.setLoading('dimensionsData', true)
            try {
                const result = await profileService.getCustomerDimensionScores({
                    objectApiName: this.objectApiName,
                    objectId: this.dataId,
                    methodologyId: this.currentMethodology,
                    methodologyType: this.currentMethodologyType,
                }, {isC139: this.isC139Methodology})
                const {dimensions, nodes} = result;
                // 通用画像取维度、方法论取节点数据
                this.dimensionsData = this.isCommonMethodology ? dimensions : nodes;
            } catch (error) {
                console.error(error)
            } finally {
                this.setLoading('dimensionsData', false)
            }
        },

        async fetchStageScoreData() {
            this.setLoading('stageScoreData', true)
            this.stageScoreData = {
                stageName: '',
                stageScore: 0,
                stages: [],
            }
            this.flowProfileData = {};
            this.flowProfileAdviceSummary = '';
            this.currentFlowProfileId = null;
            try {
                const {stageScoreData, flowPorfile, flowProfileAdviceSummary} = await profileService.getCustomerStageSource({
                    objectApiName: this.objectApiName,
                    objectId: this.objectId,
                    methodologyId: this.flowMethodology,
                    methodologyType: 'flow',
                })
                this.stageScoreData = stageScoreData;
                this.flowProfileData = flowPorfile;
                this.flowProfileAdviceSummary = flowProfileAdviceSummary;
                this.currentFlowProfileId = flowPorfile?._id;
            } catch (error) {
                console.error(error)
            } finally {
                this.setLoading('stageScoreData', false)
            }
        },
    }
}
