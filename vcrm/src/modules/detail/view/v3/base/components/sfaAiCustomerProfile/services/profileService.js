import requestProxy from '../utils/requestProxy';
import { mockService } from './mockService';
import utils from '../utils';

const ajaxBase = (url, data, proxyKey) => {
    const fetch = () => new Promise((resolve, reject) => {
        CRM.util.FHHApi({
            url,
            data,
            success: function (res) {
                if (res.Result.StatusCode === 0) {
                    resolve(res.Value);
                    return;
                }
                reject(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"))
            },
        }, {
            errorAlertModel: 1
        })
    })
    if (proxyKey) {
        return requestProxy.request(proxyKey, fetch);
    }
    return fetch();
}

// 环境配置
// const IS_MOCK = process.env.NODE_ENV === 'development';
const IS_MOCK = false;

/**
 * 刷新画像
 */
export function refreshProfile(params = {
    methodologyId: '',
    methodologyType: '',
    objectApiName: '',
    objectId: '',
}, options = {}) {
    return ajaxBase('/EM1HNCRM/API/v1/object/profile/service/refresh_profile', params);
}

export async function getMethodologyInstance(params = {
    objectApiName: '',
    objectId: '',
}, options = {}) {
    // 方法论实例列表，第一条是流程，其他是画像
    const {methodologyInstanceList = []} = await ajaxBase('/EM1HNCRM/API/v1/object/profile/service/get_methodology_instance', params) || {};
    const result = [];
    let flowMethodologyId = null;
    methodologyInstanceList.forEach((item, index) => {
        const {methodology_id, methodology_id__r, methodology_type, methodology_ids} = item || {};
        if (index === 0) {
            flowMethodologyId = methodology_id;
        }
        result.push({
            id: methodology_id,
            name: methodology_id__r,
            type: methodology_type,
            isRelatedFlow: index === 0 ? true : (methodology_ids || []).find(id => id === flowMethodologyId),
        });
    });
    return result;
}

/**
 * 获取客户画像数据
 */
export async function getCustomerProfile(params = {
    objectApiName: '',
    objectId: '',
    methodologyId: '',
    methodologyType: '',
}, options = {}) {
    const { objectApiName, objectId, methodologyId, methodologyType } = params;

    const result = await (IS_MOCK ?
        mockService.getProfileData() :
        ajaxBase('/EM1HNCRM/API/v1/object/profile/service/get_profile_info', params, `customerProfileSourceInfo:${objectApiName}:${objectId}:${methodologyId}:${methodologyType}`)
    );
    return result.profile || {};
}

/**
 * 获取维度得分
 */
export async function getCustomerDimensionScores(params = {
    objectApiName: '',
    objectId: '',
    methodologyId: '',
    methodologyType: '',
}, options = {}) {
    const { objectApiName, objectId, methodologyId, methodologyType } = params;

    const result = await (IS_MOCK ?
        mockService.getProfileData() :
        ajaxBase('/EM1HNCRM/API/v1/object/profile/service/get_profile_info', params, `customerProfileSourceInfo:${objectApiName}:${objectId}:${methodologyId}:${methodologyType}`)
    );
    const {profileItemScore = [], nodeInstance = []} = result || {};
    return {
        dimensions: profileItemScore
            .filter((item) => item.type === 'dimension')
            .sort((a, b) => a.dimension_order - b.dimension_order)
            .map(item => ({
                label: item.feature_dimension_id__r,
                score: item.score,
                name: item.feature_dimension_id__r,
                value: item.score,
                color: utils.getSourceColor(item.score),
                id: item.feature_dimension_id,
                summary: item.summary,
            })),
        nodes: profileItemScore
            .filter((item) => item.type === 'node')
            .sort((a, b) => a.node_order - b.node_order)
            .map((item) => {
                let extendsData = {};
                // 以下是c139的特殊数据
                if (options.isC139) {
                    const incompleteTask = item?.incompleteTask || [];
                    const completedTask = item?.completedTask || [];
                    const tasks = [...incompleteTask, ...completedTask]
                        .sort((a, b) => a.task_order - b.task_order)
                        .map((task) => ({
                            name: task.short_name,
                            id: task._id,
                            status: !!completedTask.find(_task => _task._id === task._id),
                        }));
                    extendsData = {
                        shortName: item.node_short_name,
                        tasks,
                        columns: {
                            '1W': 1,
                            '3F': 2,
                            '9C': 3,
                        }[item.node_short_name],
                        layout: 'grid',
                        completedTasksCount: completedTask.length || 0,
                        incompleteTasksCount: incompleteTask.length || 0,
                        completedText: item.node_short_name === '9C' ? $t('sfa.aiCustomerProfile.nodeCompletedNumText.9C', { num: completedTask.length }) : $t('sfa.aiCustomerProfile.nodeCompletedNumText.default', { num: completedTask.length }),
                        incompleteText: item.node_short_name === '9C' ? $t('sfa.aiCustomerProfile.nodeIncompleteNumText.9C', { num: incompleteTask.length }) : $t('sfa.aiCustomerProfile.nodeIncompleteNumText.default', { num: incompleteTask.length }),
                        __isTask: tasks.length > 0,
                    }
                }

                return {
                    label: item.node_id__r,
                    title: item.node_id__r,
                    name: item.node_id__r,
                    id: item.node_id,
                    summary: item?.summary,
                    score: item.score,
                    value: item.score,
                    color: utils.getSourceColor(item.score),
                    ...extendsData,
                }
            })
    }
}

/**
 * 获取节点实例
 */
export async function getCustomerStageSource(params = {
    objectApiName: '',
    objectId: '',
    methodologyId: '',
    methodologyType: '',
}, options = {}) {
    const { objectApiName, objectId, methodologyId, methodologyType } = params;

    const result = await (IS_MOCK ?
        mockService.getProfileData() :
        ajaxBase('/EM1HNCRM/API/v1/object/profile/service/get_profile_info', params, `customerProfileSourceInfo:${objectApiName}:${objectId}:${methodologyId}:${methodologyType}`)
    );

    const {profile = {}, nodeInstance = [], profileAdviceSummary = ''} = result || {};
    return {
        stageScoreData: {
            stageName: profile.methodology_id__r,
            stageScore: profile.phase_cumulative_score,
            stages: nodeInstance
                .sort((a, b) => a.node_order - b.node_order)
                .map(item => {
                    const sourceItem = (result?.profileItemScore || []).find(sourceItem => sourceItem.node_id === item.node_id);
                    return {
                        name: item.node_id__r,
                        value: sourceItem?.score,
                    }
                }),
        },
        flowPorfile: profile,
        flowProfileAdviceSummary: profileAdviceSummary,
    }
}

/**
 * 维度历史
 */
export async function getCustomerDimensionHistory(params = {
    objectApiName: '',
    objectId: '',
    methodologyId: '',
    methodologyType: '',
    featureDimensionId: '',
}, options = {}) {
    const result = await (IS_MOCK ?
        mockService.getProfileData() :
        ajaxBase('/EM1HNCRM/API/v1/object/profile/service/trends_change', params)
    );
    return (result?.trendsChange || []).sort((a, b) => a.calc_time - b.calc_time).map((item) => ({
        ...item,
        id: item.feature_dimension_id || item.node_id,
        date: CRM.util.moment(Number(item.calc_time)).format('YYYYMMDD'),
    }));
}

/**
 * 维度历史特征值
 */
export async function getCustomerDimensionTrendDetail(params = {
    profileId: '',
    featureDimensionId: '',
    methodologyType: '',
    methodologyInstanceId: '',
    objectApiName: '',
    objectId: '',
}) {
    const result = await ajaxBase('/EM1HNCRM/API/v1/object/profile/service/feature_original_value', params, `customerDimensionTrendDetail:${params.profileId}:${params.featureDimensionId}:${params.nodeId}`)
    return (result?.featureTriggerValueList || []).map(item => {
        const {featureName, featureDimensionId, type, originalValue = '', originalValueDocument = {}, fieldDescribes = []} = item;
        const fieldValues = () => {
            return fieldDescribes.map((fieldDescribe) => {
                return FxUI.objectApi.format_field_value(fieldDescribe, originalValueDocument[fieldDescribe.api_name], originalValueDocument)
            }).join(',')
        }
        return {
            name: featureName,
            value: type === 'feature_value' ? _.escape(originalValue) : fieldValues(),
            type,
            featureDimensionId,
        }
    });
}

/**
 * 画像历史
 */
export async function getCustomerProfileHistory(params = {
    methodologyInstanceId: '',
    methodologyType: '',
    featureDimensionId: '',
    objectApiName: '',
    objectId: '',
}, options = {}) {
    const { methodologyInstanceId, methodologyType, featureDimensionId, objectApiName, objectId } = params;

    const result = await (IS_MOCK ?
        mockService.getProfileData() :
        ajaxBase('/EM1HNCRM/API/v1/object/profile/service/profile_history', params)
    );
    return (result?.profileHistory || []).map((item) => {
        const { profileItemScore, profileProsCons, profileAdvice, calc_time } = item;
        const score = featureDimensionId ? (profileItemScore || []).find(item => (item.feature_dimension_id || item.node_id) === featureDimensionId)?.score : item.integrated_score;
        return {
            time: CRM.util.formatTime(calc_time, 'time'),
            summary: item.summary,
            score,
            scoreColor: utils.getSourceColor(score),
            pros: (profileProsCons || []).filter(item => item.type === 'pros').map(item => item.information),
            cons: (profileProsCons || []).filter(item => item.type === 'cons').map(item => item.information),
            advice: parseSuggestions(profileAdvice || []),
        }
    });
}


/**
 * 获取综合得分趋势
 */
export async function getCustomerProfileTrend(params = {
    methodologyId: '',
    methodologyType: '',
    objectApiName: '',
    objectId: '',
    startDate: '',
    endDate: '',
}, options = {}) {
    const { methodologyId, methodologyType, objectApiName, objectId, startDate, endDate } = params;
    if (!methodologyType) {
        return Promise.reject(new Error('argument error'));
    }

    const result = await (IS_MOCK ?
        mockService.getProfileData() :
        ajaxBase('/EM1HNCRM/API/v1/object/profile/service/integrated_score_trend', params)
    );
    return (result?.integratedScoreTrend || []).sort((a, b) => a.calc_time - b.calc_time).map((item) => ({
        value: item.integrated_score,
        date: CRM.util.moment(item.calc_time).format('YYYYMMDD'),
    }));
}

/**
 * 获取建议列表
 * @param {Object} params 请求参数
 * @param {string} params.profileId 客户画像ID
 * @param {string} params.featureDimensionId 维度ID
 * @param {Object} options 请求选项
 * @returns {Promise<Object>}
 */
const parseSuggestions = (suggestions = []) => {
    return suggestions.map((item) => {
        return {
            ...item,
            content: item.advice,
            reference: item.knowledge_document_ids?.map((id, i) => ({
                name: item.knowledge_document_names?.[i],
                value: id
            })) || [],
        }
    })
}
export async function getCustomerSuggestions(params = {
    profileId: '',
    featureDimensionId: '',
}, options = {}) {
    const { profileId, featureDimensionId } = params;

    const result = await (IS_MOCK ?
        mockService.getProfileData() :
        ajaxBase('/EM1HNCRM/API/v1/object/profile/service/get_advice', params, `customerSuggestions:${profileId}:${featureDimensionId}`)
    );
    const advices = parseSuggestions(result.profileAdvice);
    return {
        profileAdviceSummary: result.profileAdviceSummary,
        profileAdvice: advices.reduce((acc, item) => {
            const accTaskItem = acc.find(aItem => aItem.task_id === item.task_id);
            if (accTaskItem) {
                accTaskItem.children.push(item);
                accTaskItem.children.sort((a, b) => {
                    const typeOrder = {
                        'customer': 1,
                        'fs': 2,
                    };
                    return typeOrder[a.type] - typeOrder[b.type];
                });
            } else {
                acc.push({
                    task: item.task_id__r,
                    task_id: item.task_id,
                    children: [item],
                });
            }
            return acc;
        }, []),
    };
}

/**
 * 获取优势列表
 * @param {Object} params 请求参数
 * @param {string} params.profileId 客户画像ID
 * @param {string} params.featureDimensionId 维度ID
 * @param {Object} options 请求选项
 * @returns {Promise<string[]>}
 */
export async function getCustomerAdvantages(params = {
    profileId: '',
    featureDimensionId: '',
}, options = {}) {
    const { profileId, featureDimensionId } = params;

    const result = await (IS_MOCK ?
        mockService.getProfileData() :
        ajaxBase('/EM1HNCRM/API/v1/object/profile/service/get_pros_cons', {
            profileId,
            featureDimensionId,
        }, `customerProsCons:${profileId}:${featureDimensionId}`)
    );
    return (result?.profileProsCons || []).filter((item) => item.type === 'pros').map((item) => item.information);
}

/**
 * 获取劣势列表
 * @param {Object} params 请求参数
 * @param {string} params.profileId 客户画像ID
 * @param {string} params.featureDimensionId 维度ID
 * @param {Object} options 请求选项
 * @returns {Promise<string[]>}
 */
export async function getCustomerDisadvantages(params = {
    profileId: '',
    featureDimensionId: '',
}, options = {}) {
    const { profileId, featureDimensionId } = params;

    const result = await (IS_MOCK ?
        mockService.getProfileData() :
        ajaxBase('/EM1HNCRM/API/v1/object/profile/service/get_pros_cons', {
            profileId,
            featureDimensionId,
        }, `customerProsCons:${profileId}:${featureDimensionId}`)
    );
    return (result?.profileProsCons || []).filter((item) => item.type === 'cons').map((item) => item.information);
}

export function clearCache() {
    requestProxy.clearCache();
}


export default {
    refreshProfile,
    getMethodologyInstance,
    getCustomerProfile,
    getCustomerDimensionScores,
    getCustomerDimensionHistory,
    getCustomerDimensionTrendDetail,
    getCustomerProfileHistory,
    getCustomerStageSource,
    getCustomerProfileTrend,
    getCustomerSuggestions,
    getCustomerAdvantages,
    getCustomerDisadvantages,
    clearCache,
}
