/**
 * @desc: 开属性-web
 * @author: wangshaoh
 * @date: 12/29/21
 */
import AttributeBase_Base from 'attribute'
import Base from 'plugin_base'

export default class Attribute extends Base{

    options() {
        return {}
    }

    constructor(pluginService, pluginParam) {
        super(...arguments)
        this.BaseCom = new AttributeBase_Base(pluginService, pluginParam);
        this.cacheChildren([this.BaseCom]);

    }

    // 添加参数，选数据时展示属性
    _batchAddBefore(plugin, param) {
        let {product_id} = this.getAllFields(param.objApiName);
        let {price_book_product_id} = this.getPluginFields('price-service') || {};
        if (!plugin.preData) plugin.preData = {};
        if (!plugin.preData.extendParam) plugin.preData.extendParam = {};
        let lookUpApiName = param.lookupField.api_name;

        if([price_book_product_id, product_id].includes(lookUpApiName)){
            let masterData = param.dataGetter.getMasterData();
            let priceService = this.getPluginFields('price-service');
            let bomPlugin = this.getPluginFields('bom');
            let p = this.getPickSelfPath(plugin, param);
            let res =  Object.assign({
                extendParam:{
                    _from: 'attribute',
                  master_data: masterData,
                  noGetRealPrice: !priceService,
                  notShowBomChildren: !bomPlugin,
                }
            }, p);
            return {
                __execResult: res,
                __mergeDataType: {
                    array: 'concat'
                }
            }
        }
    }

    // 模式2
    _isModule2(){
        return CRM.util.getConfigStatusByKey('cpq_ui_mode') === '1';
    }

    getPickSelfPath(plugin, param) {
        if(this._isModule2()){
            let path = 'crm-modules/buscomponents/pickselfobject_cpq_style2/pickselfobject_cpq_style2';
            return {modulePath: path}
        }
        return {};
    }

    // editBefore(plugin, options) {
    //     console.log(options);
    //     console.log(plugin.pluginExecResult);
    //     console.log('exit second hook handle');
    //     plugin.skipPlugin(); // 不会执行第三个钩子
    // }

    getHook() {
        return [
            {
                event: 'md.batchAdd.before',
                functional: this._batchAddBefore.bind(this)
            }
        ];
    }


}
